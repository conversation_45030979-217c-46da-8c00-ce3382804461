import { useState } from 'react';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';
import { Typography } from '@hxnova/react-components/Typography';
import { FormControl } from '@hxnova/react-components/FormControl';
import { FormLabel } from '@hxnova/react-components/FormLabel';
import { FormHelperText } from '@hxnova/react-components/FormHelperText';
import { Tag } from '@hxnova/react-components/Tag';
import { Link } from '@hxnova/react-components/Link';
import { Checkbox } from '@hxnova/react-components/Checkbox';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Button } from '@hxnova/react-components/Button';
import { Divider } from '@hxnova/react-components/Divider';
import Icon from '@hxnova/icons/Icon';

const generateOptions = (count: number) => {
  const result = [];
  for (let i = 0; i < count; i++) {
    result.push({
      label: `Option ${i + 1}`,
      value: `option${i + 1}`,
    });
  }
  return result;
};
const allOptions = generateOptions(20);

export default function DropdownKitchen() {
  const [open, setOpen] = useState<boolean>(false);
  const [options, setOptions] = useState<string[]>(['option1', 'option2']);
  const handleOptionChange = (event: React.SyntheticEvent | null, values: string[]) => {
    setOptions(values);
  };
  const onApply = () => {
    setOpen(false);
  };
  const onClear = () => {
    setOptions([]);
    setOpen(false);
  };
  return (
    <div style={{ margin: '20px', display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Basic</Typography>
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <Dropdown placeholder="Select" style={{ width: '200px' }}>
            <Option value="option1">Option 1</Option>
            <Option value="option2">Option 2</Option>
            <Option value="option3">Option 3</Option>
          </Dropdown>
          <Dropdown placeholder="Select" style={{ width: '200px' }} defaultValue={'option2'}>
            <Option value="option1">Option 1</Option>
            <Option value="option2">Option 2</Option>
            <Option value="option3">Option 3</Option>
          </Dropdown>
        </div>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Different State</Typography>
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <Dropdown placeholder="Disabled" style={{ width: '200px' }} disabled>
            <Option value="option1">Option 1</Option>
            <Option value="option2">Option 2</Option>
            <Option value="option3">Option 3</Option>
          </Dropdown>
          <Dropdown placeholder="Error" style={{ width: '200px' }} error>
            <Option value="option1">Option 1</Option>
            <Option value="option2">Option 2</Option>
            <Option value="option3">Option 3</Option>
          </Dropdown>
          <Dropdown placeholder="Readonly" style={{ width: '200px' }} readOnly>
            <Option value="option1">Option 1</Option>
            <Option value="option2">Option 2</Option>
            <Option value="option3">Option 3</Option>
          </Dropdown>
        </div>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">FormControl</Typography>
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <FormControl>
            <FormLabel>Label</FormLabel>
            <Dropdown placeholder="Select" style={{ width: '200px' }}>
              <Option value="option1">Option 1</Option>
              <Option value="option2">Option 2</Option>
              <Option value="option3">Option 3</Option>
            </Dropdown>
            <FormHelperText>Supporting text</FormHelperText>
          </FormControl>

          <FormControl required>
            <FormLabel>Required</FormLabel>
            <Dropdown placeholder="Select" style={{ width: '200px' }}>
              <Option value="option1">Option 1</Option>
              <Option value="option2">Option 2</Option>
              <Option value="option3">Option 3</Option>
            </Dropdown>
            <FormHelperText>Supporting text</FormHelperText>
          </FormControl>

          <FormControl disabled>
            <FormLabel>Disabled</FormLabel>
            <Dropdown placeholder="Select" style={{ width: '200px' }}>
              <Option value="option1">Option 1</Option>
              <Option value="option2">Option 2</Option>
              <Option value="option3">Option 3</Option>
            </Dropdown>
            <FormHelperText>Supporting text</FormHelperText>
          </FormControl>

          <FormControl error>
            <FormLabel>Error</FormLabel>
            <Dropdown placeholder="Select" style={{ width: '200px' }}>
              <Option value="option1">Option 1</Option>
              <Option value="option2">Option 2</Option>
              <Option value="option3">Option 3</Option>
            </Dropdown>
            <FormHelperText>Supporting text</FormHelperText>
          </FormControl>
        </div>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Size</Typography>
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <FormControl size="small">
            <FormLabel>Small</FormLabel>
            <Dropdown placeholder="Select" style={{ width: '200px' }} slotProps={{ listbox: { density: 'compact' } }}>
              <Option value="option1">Option 1</Option>
              <Option value="option2">Option 2</Option>
              <Option value="option3">Option 3</Option>
            </Dropdown>
          </FormControl>
          <FormControl size="medium">
            <FormLabel>Medium</FormLabel>
            <Dropdown placeholder="Select" style={{ width: '200px' }} slotProps={{ listbox: { density: 'standard' } }}>
              <Option value="option1">Option 1</Option>
              <Option value="option2">Option 2</Option>
              <Option value="option3">Option 3</Option>
            </Dropdown>
          </FormControl>
          <FormControl size="large">
            <FormLabel>large</FormLabel>
            <Dropdown
              placeholder="Select"
              style={{ width: '200px' }}
              slotProps={{ listbox: { density: 'comfortable' } }}
            >
              <Option value="option1">Option 1</Option>
              <Option value="option2">Option 2</Option>
              <Option value="option3">Option 3</Option>
            </Dropdown>
          </FormControl>
        </div>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Multiple Selections</Typography>
        <Dropdown
          multiple
          value={options}
          onChange={handleOptionChange}
          placeholder="Select"
          style={{ width: '400px' }}
        >
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
          <Option value="option4">Option 4</Option>
          <Option value="option5">Option 5</Option>
        </Dropdown>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Custom Rendering of Selected Values</Typography>
        <Dropdown
          multiple
          value={options}
          renderValue={(values) => (
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {values.map((v) => (
                <Tag key={v.value} label={v.label as string} variant="info" intensity="subtle" />
              ))}
            </div>
          )}
          onChange={handleOptionChange}
          placeholder="Select"
          style={{ width: '400px' }}
        >
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
          <Option value="option4">Option 4</Option>
          <Option value="option5">Option 5</Option>
        </Dropdown>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Decorators</Typography>
        <Dropdown
          placeholder="Assign a role"
          style={{ width: '400px' }}
          startDecorator={<Icon family="material" name="supervisor_account" />}
          endDecorator={
            <Link
              href="#sample"
              target="_blank"
              underline={'always'}
              onMouseDown={(event) => {
                event.stopPropagation();
              }}
            >
              Learn more
            </Link>
          }
        >
          <Option value="viewer">Viewer</Option>
          <Option value="editor">Editor</Option>
          <Option value="owner">Owner</Option>
        </Dropdown>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">List box</Typography>
        <Dropdown
          multiple
          value={options}
          onChange={handleOptionChange}
          placeholder="Select"
          style={{ width: '400px' }}
          startDecorator={<Icon family="material" name="calendar_today" />}
          renderValue={(items) => (items.length > 1 ? `${items.length} options selected` : items[0]?.label)}
          listboxOpen={open}
          onListboxOpenChange={(isOpen) => {
            setOpen(isOpen);
          }}
          onClose={onApply}
        >
          <div style={{ maxHeight: '300px', overflow: 'auto' }}>
            {allOptions.map((i) => (
              <Option key={i.value} value={i.value}>
                <ListItemDecorator>
                  <Checkbox checked={options.includes(i.value)} />
                </ListItemDecorator>
                <ListItemContent primary={i.label} />
              </Option>
            ))}
          </div>
          <Divider />
          <div style={{ padding: '8px 16px', display: 'flex', justifyContent: 'end', gap: '8px' }}>
            <Button variant="text" onClick={onClear}>
              Clear
            </Button>
            <Button onClick={onApply}>Apply</Button>
          </div>
        </Dropdown>
      </div>
    </div>
  );
}
