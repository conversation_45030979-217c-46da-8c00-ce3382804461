import React from 'react';
import { Autocomplete } from '@hxnova/react-components/Autocomplete';
import { TextField } from '@hxnova/react-components/TextField';
import { CircularProgress } from '@hxnova/react-components/CircularProgress';

interface Film {
  label: string;
  year: number;
}

function sleep(duration: number): Promise<void> {
  return new Promise<void>((resolve) => {
    setTimeout(() => {
      resolve();
    }, duration);
  });
}

const topFilms: readonly Film[] = [
  { label: 'The Shawshank Redemption', year: 1994 },
  { label: 'The Godfather', year: 1972 },
  { label: 'The Godfather: Part II', year: 1974 },
  { label: 'The Dark Knight', year: 2008 },
  { label: '12 Angry Men', year: 1957 },
  { label: "Schindler's List", year: 1993 },
  { label: 'Pulp Fiction', year: 1994 },
];

const groupedOptions = topFilms.map((option) => {
  const firstLetter = option.label[0].toUpperCase();
  return {
    firstLetter: /[0-9]/.test(firstLetter) ? '0-9' : firstLetter,
    ...option,
  };
});

export default function AutocompleteKitchen() {
  const [value, setValue] = React.useState<Film | null>(topFilms[0]);
  const [inputValue, setInputValue] = React.useState('');

  const [open, setOpen] = React.useState(false);
  const [options, setOptions] = React.useState<readonly Film[]>([]);
  const [loading, setLoading] = React.useState(false);

  const handleOpen = () => {
    setOpen(true);
    (async () => {
      setLoading(true);
      await sleep(1e3); // For demo purposes.
      setLoading(false);

      setOptions([...topFilms]);
    })();
  };

  const handleClose = () => {
    setOpen(false);
    setOptions([]);
  };

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: 32 }}>
      <h2>Combo box</h2>
      <div>
        <Autocomplete
          disablePortal
          options={topFilms}
          sx={{ width: 500 }}
          renderInput={(params) => <TextField {...params} label="Combo box" placeholder="Placeholder" />}
        />
      </div>
      <h2>Controlled states</h2>
      <div>
        <div>{`value: ${value !== null ? `'${value.label}'` : 'null'}`}</div>
        <div>{`inputValue: '${inputValue}'`}</div>
        <br />
        <Autocomplete
          value={value}
          onChange={(_event, newValue) => {
            setValue(newValue);
          }}
          inputValue={inputValue}
          onInputChange={(_event, newInputValue) => {
            setInputValue(newInputValue);
          }}
          id="controllable-states-demo"
          options={topFilms}
          sx={{ width: 500 }}
          renderInput={(params) => <TextField {...params} label="Controllable" placeholder="Placeholder" />}
        />
      </div>
      <h2>Grouped options</h2>
      <div>
        <Autocomplete
          disablePortal
          options={groupedOptions}
          groupBy={(option) => option.firstLetter}
          sx={{ width: 500 }}
          renderInput={(params) => <TextField {...params} label="Grouped" placeholder="Placeholder" />}
        />
      </div>
      <h2>Loading</h2>
      <div>
        <Autocomplete
          sx={{ width: 500 }}
          open={open}
          onOpen={handleOpen}
          onClose={handleClose}
          isOptionEqualToValue={(option, value) => option.label === value.label}
          options={options}
          loading={loading}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Asynchronous"
              placeholder="Placeholder"
              slotProps={{
                ...params.slotProps,
                input: {
                  endDecorator: (
                    <React.Fragment>
                      {loading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.endDecorator}
                    </React.Fragment>
                  ),
                },
              }}
            />
          )}
        />
      </div>
      <h2>Multiple selection</h2>
      <div>
        <Autocomplete
          multiple
          disablePortal
          options={topFilms}
          sx={{ width: 500 }}
          renderInput={(params) => <TextField {...params} label="Multiple" placeholder="Placeholder" />}
        />
      </div>
    </div>
  );
}
