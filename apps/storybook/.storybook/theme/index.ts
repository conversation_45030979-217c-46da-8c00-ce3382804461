import { create } from '@storybook/theming';
import { NovaTheme } from '@hxnova/themes';

export const NovaLight = create({
  base: 'light',
  brandTitle: 'Nova Design System',
  brandUrl: 'https://zeroheight.com/9a7698df1/p/310550-nova-design-system',
  appBg: NovaTheme.colorSchemes.light?.palette.surface,
  appContentBg: NovaTheme.colorSchemes.light?.palette.surfaceContainer,
  appBorderColor: NovaTheme.colorSchemes.light?.palette.outline,
  appPreviewBg: NovaTheme.colorSchemes.light?.palette.surfaceContainer,
  //   appBorderRadius: NovaTheme.borderRadius,

  // Typography
  fontBase: NovaTheme.typography.fontFamily,
  fontCode: 'monospace',

  // Text colors
  textColor: NovaTheme.colorSchemes.light?.palette.onSurface,
  // textInverseColor: 'rgba(255,255,255,0.9)',

  // Toolbar default and active colors
  barTextColor: NovaTheme.colorSchemes.light?.palette.onSurface,
  barSelectedColor: NovaTheme.colorSchemes.light?.palette.primary,
  barBg: NovaTheme.colorSchemes.light?.palette.surfaceContainer,

  // Form colors
  inputBg: NovaTheme.colorSchemes.light?.palette.surfaceContainer,
  inputBorder: NovaTheme.colorSchemes.light?.palette.outlineVariant,
  inputTextColor: NovaTheme.colorSchemes.light?.palette.onSurface,
  //   inputBorderRadius: NovaTheme.shape.borderRadius,
});

export const NovaDark = create({
  base: 'dark',
  brandTitle: 'Nova Design System',
  brandUrl: 'https://zeroheight.com/9a7698df1/p/310550-nova-design-system',
  appBg: NovaTheme.colorSchemes.dark?.palette.surface,
  appContentBg: NovaTheme.colorSchemes.dark?.palette.surfaceContainer,
  appBorderColor: NovaTheme.colorSchemes.dark?.palette.outlineVariant,
  appPreviewBg: NovaTheme.colorSchemes.dark?.palette.surfaceContainer,
  //   appBorderRadius: NovaTheme.shape.borderRadius,

  // Typography
  fontBase: NovaTheme.typography.fontFamily,
  fontCode: 'monospace',

  // Text colors
  textColor: NovaTheme.colorSchemes.dark?.palette.onSurface,
  // textInverseColor: 'rgba(255,255,255,0.9)',

  // Toolbar default and active colors
  barTextColor: NovaTheme.colorSchemes.dark?.palette.onSurface,
  barSelectedColor: NovaTheme.colorSchemes.dark?.palette.primary,
  barBg: NovaTheme.colorSchemes.dark?.palette.surfaceContainer,

  // Form colors
  inputBg: NovaTheme.colorSchemes.dark?.palette.surfaceContainer,
  inputBorder: NovaTheme.colorSchemes.dark?.palette.outlineVariant,
  inputTextColor: NovaTheme.colorSchemes.dark?.palette.onSurface,
  //   inputBorderRadius: NovaTheme.shape.borderRadius,
});
