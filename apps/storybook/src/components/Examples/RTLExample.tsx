import React from 'react';
import { NovaProvider } from '@hxnova/react-components/NovaProvider';
import arEG from '@hxnova/react-components/Locale/ar_EG';
import { CssBaseline, TextField } from '@hxnova/react-components';
import { DataGrid } from '@hxnova/react-components/DataGrid';
import { LinearProgress } from '@hxnova/react-components/LinearProgress';
import { Typography } from '@hxnova/react-components/Typography';

export default function RTLExample() {
  const [progress, setProgress] = React.useState(10);
  React.useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prevProgress) => (prevProgress >= 100 ? 10 : prevProgress + 10));
    }, 800);
    return () => {
      clearInterval(timer);
    };
  }, []);
  return (
    <NovaProvider locale={arEG} rtl>
      <CssBaseline />
      <div dir="rtl">
        <TextField label="ملصق" placeholder="العنصر النائب" helperText="هذا نص مساعد" autoComplete="off" />
        <div sx={{ textAlign: 'left' }}>المجموع: 5 عناصر</div>
        <DataGrid
          rowsPerPage={5}
          paginationConfig={{ rowsPerPageOptions: [5, 10, 25] }}
          columns={[
            { field: 'id', header: 'ID', width: 150 },
            { field: 'firstName', header: 'First Name', width: 200 },
            { field: 'lastName', header: 'Last Name', width: 200 },
          ]}
          data={[
            { id: 1, firstName: 'A1', lastName: 'A2' },
            { id: 2, firstName: 'B1', lastName: 'B2' },
            { id: 3, firstName: 'C1', lastName: 'C2' },
            { id: 4, firstName: 'D1', lastName: 'D2' },
            { id: 5, firstName: 'E1', lastName: 'E2' },
            { id: 6, firstName: 'F1', lastName: 'F2' },
          ]}
        />
        <div sx={{ display: 'flex', alignItems: 'center' }}>
          <div sx={{ width: '100%', marginRight: '8px' }}>
            <LinearProgress variant="determinate" value={progress} />
          </div>
          <div sx={{ minWidth: '35px' }}>
            <Typography
              variant="bodySmall"
              sx={{ color: 'var(--palette-primary)' }}
            >{`${Math.round(progress)}%`}</Typography>
          </div>
        </div>
      </div>
    </NovaProvider>
  );
}
