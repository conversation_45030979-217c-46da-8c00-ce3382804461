import { useState } from 'react';
import { Button } from '@hxnova/react-components/Button';
import { Tooltip } from '@hxnova/react-components/Tooltip';
import { IconButton } from '@hxnova/react-components/IconButton';
import { useDarkMode } from 'storybook-dark-mode';
import { Source } from '@storybook/addon-docs';

const CodeExpand = (props: {
  code: string;
  defaultExpanded?: boolean;
  // hideSandbox?: boolean;
  showBorderTop?: boolean;
}) => {
  const isDark = useDarkMode();
  const { code, defaultExpanded, /*hideSandbox,*/ showBorderTop, ...otherProps } = props;
  const [expanded, setExpanded] = useState(defaultExpanded);
  const [copyTitle, setCopyTitle] = useState('Copy code');

  const handleCopyClick = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopyTitle('Code copied!');
      setTimeout(() => {
        setCopyTitle('Copy code');
      }, 2000);
    } catch (error) {
      console.error(error);
    }
  };
  return (
    <div
      sx={[
        {
          display: 'flex',
          flexDirection: 'column',
          marginBottom: 16,
          boxShadow: 'none',
          '& .docblock-source': {
            margin: '0 !important',
            boxShadow: 'none',
            border: 'none',
            background: 'unset',
            maxHeight: '400px',
            overflowY: 'auto',
            button: {
              display: 'none',
            },
          },
          borderTop: !showBorderTop ? 0 : '1px solid rgba(0, 0, 0, 0.1)',
        },
      ]}
      {...otherProps}
    >
      <div
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'flex-end',
          borderBottom: `1px solid rgba(0, 0, 0, 0.1)`,
          padding: 8,
        }}
      >
        {expanded && (
          <Tooltip title={copyTitle} showArrow>
            <IconButton
              variant={'standard'}
              size={'small'}
              sx={{ marginRight: '4px' }}
              color="primary"
              onClick={handleCopyClick}
              aria-label={copyTitle}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                height="24px"
                viewBox="0 -960 960 960"
                width="24px"
                fill="currentColor"
              >
                <path d="M360-240q-33 0-56.5-23.5T280-320v-480q0-33 23.5-56.5T360-880h360q33 0 56.5 23.5T800-800v480q0 33-23.5 56.5T720-240H360Zm0-80h360v-480H360v480ZM200-80q-33 0-56.5-23.5T120-160v-560h80v560h440v80H200Zm160-240v-480 480Z" />
              </svg>
            </IconButton>
          </Tooltip>
        )}
        <Button
          sx={{ minWidth: 100, justifyContent: 'center' }}
          size="small"
          variant="outlined"
          aria-label="Code sandbox"
          onClick={() => {
            setExpanded(!expanded);
          }}
        >
          {expanded ? 'Hide code' : 'Show code'}
        </Button>
        {/* {code && !hideSandbox && <CodeSandboxTemplate code={code} />} */}
      </div>
      {expanded && <Source code={code} language="tsx" dark={isDark} />}
    </div>
  );
};

export default CodeExpand;
