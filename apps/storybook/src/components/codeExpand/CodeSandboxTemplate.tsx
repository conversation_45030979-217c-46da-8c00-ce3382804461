/**
 * This template is taken from the implementation in NexusUI
 *
 * Unfortunately, I was not able to find a way to configure a Vite project
 * in CodeSandbox, so it's unclear if we will be able to provide this
 * functionality for <PERSON>. For the time being, I will comment out the
 * CodeSandbox button but leave the implementation here for future reference.
 */
import { getParameters } from 'codesandbox/lib/api/define';
import { Tooltip } from '@hxnova/react-components/Tooltip';
import { IconButton } from '@hxnova/react-components/IconButton';

const BoxIcon = () => {
  return (
    <svg viewBox="0 0 1024 1024" fill={'currentColor'} style={{ height: 20, width: 20 }}>
      <path d="M755 140.3l0.5-0.3h0.3L512 0 268.3 140h-0.3l0.8 0.4L68.6 256v512L512 1024l443.4-256V256L755 140.3z m-30 506.4v171.2L548 920.1V534.7L883.4 341v215.7l-158.4 90z m-584.4-90.6V340.8L476 534.4v385.7L300 818.5V646.7l-159.4-90.6zM511.7 280l171.1-98.3 166.3 96-336.9 194.5-337-194.6 165.7-95.7L511.7 280z" />
    </svg>
  );
};

const templateFiles = () => {
  const packageContent = `{
  "name": "nova-template",
  "version": "1.0.0",
  "description": "",
  "keywords": [],
  "main": "src/index.tsx",
  "dependencies": {
    "clsx": "^2.1.1",
    "@emotion/react": "latest",
    "@emotion/styled": "latest",
    "@mui/system": "latest",
    "react-hook-form": "^7.27.1",
    "@nexusui/components": "alpha",
    "@nexusui/theme": "alpha",
    "@fontsource/open-sans": "latest",
    "@fontsource/roboto": "latest",
    "react": "18.2.0",
    "react-dom": "18.2.0",
    "react-scripts": "4.0.3"
  },
  "devDependencies": {
    "@types/react": "18.0.0",
    "@types/react-dom": "18.0.0",
    "typescript": "4.4.2"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test --env=jsdom",
    "eject": "react-scripts eject"
  },
  "browserslist": [
    ">0.2%",
    "not dead",
    "not ie <= 11",
    "not op_mini all"
  ]
}`;

  const indexContent = `import '@nexusui/theme/fonts';
import { StrictMode } from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { useThemeWithLocale } from '@nexusui/theme';

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
  
const RootDOM = () => {
  const themeWithLocale = useThemeWithLocale('en-US');
  return (
    <ThemeProvider theme={themeWithLocale}>
      <CssBaseline />
      <App />
    </ThemeProvider>
  );
};

root.render(
  <StrictMode>
    <RootDOM />
  </StrictMode>
);`;

  return {
    files: {
      '.prettierrc': {
        content:
          '{\r\n  "singleQuote": true,\r\n  "printWidth": 250,\r\n  "proseWrap": "always",\r\n  "tabWidth": 2,\r\n  "useTabs": false,\r\n  "trailingComma": "none",\r\n  "bracketSpacing": true,\r\n  "semi": true\r\n}\r\n',
        isBinary: false,
      },
      'package.json': {
        content: packageContent,
        isBinary: false,
      },
      'tsconfig.json': {
        content:
          '{\n  "include": [\n      "./src*"\n  ],\n  "compilerOptions": {\n      "strict": true,\n      "esModuleInterop": true,\n      "lib": [\n          "dom",\n          "es2015"\n      ],\n      "jsx": "react-jsx"\n  }\n}',
        isBinary: false,
      },
      'public/index.html': {
        content:
          '<!DOCTYPE html>\n<html lang="en">\n<head>\n\t<meta charset="utf-8">\n\t<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">\n\t<meta name="theme-color" content="#000000">\n\t<link rel="manifest" href="%PUBLIC_URL%/manifest.json">\n\t<link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico">\n\t<title>React App</title>\n</head>\n<body>\n\t<noscript>\n\t\tYou need to enable JavaScript to run this app.\n\t</noscript>\n\t<div id="root"></div>\n</body>\n\n</html>',
        isBinary: false,
      },
      'src/App.tsx': {
        content: '',
        isBinary: false,
      },
      'src/index.tsx': {
        content: indexContent,
        isBinary: false,
      },
    },
  };
};

export const CodeSandboxTemplate = ({ code }: { code: string }) => {
  const parameters = getParameters({
    files: {
      ...templateFiles().files,
      'src/App.tsx': { content: code, isBinary: false },
    },
  });

  return (
    <form action="https://codesandbox.io/api/v1/sandboxes/define" method="POST" target="_blank">
      <input type="hidden" name="parameters" value={parameters} />
      <Tooltip title="Open Code sandbox" showArrow>
        <IconButton variant={'standard'} size={'small'} type="submit" color="primary">
          <BoxIcon />
        </IconButton>
      </Tooltip>
    </form>
  );
};
