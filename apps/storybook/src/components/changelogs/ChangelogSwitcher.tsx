import React, { useState } from 'react';
import { Markdown } from '@storybook/blocks';
import ComponentAlphaChangelog from '@hxnova/react-components/CHANGELOG.prerelease.md?raw';
import ComponentChangelog from '@hxnova/react-components/CHANGELOG.md?raw';
import ThemeAlphaChangelog from '@hxnova/themes/CHANGELOG.prerelease.md?raw';
import ThemeChangelog from '@hxnova/themes/CHANGELOG.md?raw';
import IconsAlphaChangelog from '@hxnova/icons/CHANGELOG.prerelease.md?raw';
import IconsChangelog from '@hxnova/icons/CHANGELOG.md?raw';
import TemplatesAlphaChangelog from '@hxnova/templates/CHANGELOG.prerelease.md?raw';
import TemplatesChangelog from '@hxnova/templates/CHANGELOG.md?raw';
import { Switch } from '@hxnova/react-components';

export default function ChangelogSwitcher({ packageName }: { packageName: string }) {
  const [alpha, setAlpha] = useState(false);
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 8, width: '100%' }}>
      <div style={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
        <Switch
          endDecorator={<div style={{ marginLeft: '8px' }}>Pre-release</div>}
          onChange={(e) => setAlpha(e.target.checked)}
        />
      </div>
      {packageName === 'components' && <Markdown>{alpha ? ComponentAlphaChangelog : ComponentChangelog}</Markdown>}
      {packageName === 'themes' && <Markdown>{alpha ? ThemeAlphaChangelog : ThemeChangelog}</Markdown>}
      {packageName === 'icons' && <Markdown>{alpha ? IconsAlphaChangelog : IconsChangelog}</Markdown>}
      {packageName === 'templates' && <Markdown>{alpha ? TemplatesAlphaChangelog : TemplatesChangelog}</Markdown>}
    </div>
  );
}
