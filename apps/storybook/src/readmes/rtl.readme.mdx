import { Meta, Markdown } from '@storybook/blocks';
import CodeExpand from '../components/codeExpand/CodeExpand';
import RTLExample from '../components/Examples/RTLExample';
import RTLExampleSource from '../components/Examples/RTLExample.tsx?raw';

<Meta title="@hxnova/react-components/RTL Support" />

## Right-to-Left (RTL) Support

This guide outlines the implementation of right-to-left (RTL) text using Nova components, specifically designed to accommodate languages such as Arabic, Persian, and Hebrew.

### Setup 

To enable RTL support, follow these key steps:

#### App Global Config

* **Locale Configuration**: Utilize the `locale` prop in the `NovaProvider` to specify the desired language for your application.
* **RTL Support**: The inclusion of the `rtl` prop in `NovaProvider` ensures that components are rendered with a right-to-left layout, accommodating the needs of RTL languages.
* **Direction Attribute**: Apply the `dir='rtl'` attribute to your app's `html` element or any other equivalent top level container to enforce the text direction throughout your application.
  * Components that exists outside the DOM hierarchy of the parent component (like the `Dialog`, `Drawer`) do not inherit the `dir` attribute from parents, you need apply the `dir` attribute directly to these components.

```tsx
import { NovaProvider, Dialog } from '@hxnova/react-components';
import arEG from '@hxnova/react-components/Locale/ar_EG';

return (
  <NovaProvider locale={arEG} rtl>
    <div dir='rtl'>
      <App />
      <Dialog.Root dir='rtl'>
         {/* You need apply the `dir` attribute directly to these Portals components */}
      </Dialog.Root>
    </div>
  </NovaProvider>
);
```

#### PigmentCSS Plugin Config

* **Next.js app plugin config**: Output CSS for both `ltr` and `rtl`, which automatically changes direction based on the selector.

```tsx
import { withPigment, extendTheme } from '@pigment-css/nextjs-plugin';
import { NovaTheme } from '@hxnova/themes';

// ...
export default withPigment(nextConfig, {
  theme: extendTheme(NovaTheme),
  // CSS output option
  css: {
    /**
    * To denote that whatever default css is being authored pertains to this
    * direction so that when Pigment CSS generates the CSS for the other direction,
    * it can revert the direction of the selector accordingly.
    * @default 'ltr'
    */
    defaultDirection: 'ltr',
    /**
    * Pass this as true if you want to output the CSS for both ltr and rtl.
    * The css of the non-default direction will be wrapped in a `dir` selector.
    */
    generateForBothDir: true,
  },
});

```

* **Vite app plugin config**: Output CSS for both `ltr` and `rtl`, which automatically changes direction based on the selector.

```tsx
import { pigment, extendTheme } from '@pigment-css/vite-plugin';
import { NovaTheme } from '@hxnova/themes';

export default defineConfig({
  plugins: [
    pigment({
      theme: extendTheme(NovaTheme),
      transformLibraries: ['@hxnova/react-components'],
      css: {
        // Specify your default CSS authoring direction
        defaultDirection: 'ltr',
        // Generate CSS for the opposite of the `defaultDirection`
        generateForBothDir: true,
      },
      // ... other config
    }),
    // ... other plugins.
  ],
});
```

* Generated CSS

For example, if you've specified defaultDirection: 'ltr' and dir="rtl", and your authored CSS looks like this:

```tsx
import { css } from '@pigment-css/react';

const className = css`
  margin-left: 10px,
  margin-right: 20px,
  padding: '0 10px 20px 30px'
`;
```

Then the actual CSS output would be:

```css
.cmip3v5 {
  margin-left: 10px;
  margin-right: 20px;
  padding: 0 10px 20px 30px;
}

[dir='rtl'] .cmip3v5 {
  margin-right: 10px;
  margin-left: 20px;
  padding: 0 30px 20px 10px;
}
```

### Examples

<div className="sb-unstyled">
  <RTLExample />
</div>
<CodeExpand code={RTLExampleSource} showBorderTop style={{marginTop: 16}}/>


