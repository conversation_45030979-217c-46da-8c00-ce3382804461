import { Meta, Markdown } from '@storybook/blocks';
import localeTable from './localization.readme.md?raw';

<Meta title="@hxnova/react-components/Localization" />

## Localization

The default language for the Nova component is **en-US**. To support internationalization, you can easily switch to other languages by following the instructions below.

### NovaProvider

The `@hxnova/react-components` package provides the `NovaProvider` component, which allows you to configure the locale text for all components globally.

```tsx
import { NovaProvider } from '@hxnova/react-components';
import zhCN from '@hxnova/react-components/Locale/zh_CN';

return (
  <NovaProvider locale={zhCN}>
    <App />
  </NovaProvider>
);
```

### Supported languages

<Markdown>
{localeTable}
</Markdown>


### Support extra locales
If you need to support an extra locale.
* You can create a new locale file and import it in a similar way. Ensure that your locale object follows the structure of the existing ones.
* Consider contributing your locale file to the Nova repository. This allows others to benefit from your work and helps expand the community's language support.

### Examples Usage
Here’s an example of how to switch between locales dynamically:

```tsx
import { useState } from 'react';
import { NovaProvider } from '@hxnova/react-components';
import enUS from '@hxnova/react-components/Locale/en_US';
import zhCN from '@hxnova/react-components/Locale/zh_CN';

const App = () => {
  const [locale, setLocale] = useState(enUS);

  const toggleLocale = () => {
    setLocale(locale === enUS ? zhCN : enUS);
  };

  return (
    <NovaProvider locale={locale}>
      <button onClick={toggleLocale}>Toggle Language</button>
      {/* Other components */}
    </NovaProvider>
  );
};
```

Here's an example of how to use `useLanguage` to get current locale code.

```tsx
import { NovaProvider, useLanguage } from '@hxnova/react-components';
import zhCN from '@hxnova/react-components/Locale/zh_CN';

const InnerComponent = () => {
  // lang will be 'zh-CN'
  const lang = useLanguage();
  return `https://google.com/search?q=${lang}`;
}

const App = () => {
  return (
    <NovaProvider locale={zhCN}>
      <InnerComponent />
    </NovaProvider>
  );
};
```


