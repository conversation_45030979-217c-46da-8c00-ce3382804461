import React from 'react';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';
import { FormControl } from '@hxnova/react-components/FormControl';
import { FormLabel } from '@hxnova/react-components/FormLabel';
import { FormHelperText } from '@hxnova/react-components/FormHelperText';

export default function Example() {
  return (
    <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
      <FormControl>
        <FormLabel>Label</FormLabel>
        <Dropdown placeholder="Select" style={{ width: '200px' }}>
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Dropdown>
        <FormHelperText>Supporting text</FormHelperText>
      </FormControl>

      <FormControl required>
        <FormLabel>Required</FormLabel>
        <Dropdown placeholder="Select" style={{ width: '200px' }}>
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Dropdown>
        <FormHelperText>Supporting text</FormHelperText>
      </FormControl>

      <FormControl disabled>
        <FormLabel>Disabled</FormLabel>
        <Dropdown placeholder="Select" style={{ width: '200px' }}>
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Dropdown>
        <FormHelperText>Supporting text</FormHelperText>
      </FormControl>

      <FormControl error>
        <FormLabel>Error</FormLabel>
        <Dropdown placeholder="Select" style={{ width: '200px' }}>
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Dropdown>
        <FormHelperText>Supporting text</FormHelperText>
      </FormControl>
    </div>
  );
}
