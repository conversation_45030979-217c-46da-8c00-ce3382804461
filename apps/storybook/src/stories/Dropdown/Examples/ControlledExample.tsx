import React, { useState } from 'react';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';

export default function Example() {
  const [option, setOption] = useState<string | null>('option2');
  const handleOptionChange = (event: React.SyntheticEvent | null, value: string | null) => {
    setOption(value);
  };
  return (
    <Dropdown placeholder="Select" style={{ width: '400px' }} value={option} onChange={handleOptionChange}>
      <Option value="option1">Option 1</Option>
      <Option value="option2">Option 2</Option>
      <Option value="option3">Option 3</Option>
    </Dropdown>
  );
}
