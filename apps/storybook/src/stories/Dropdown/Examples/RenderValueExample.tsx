import React, { useState } from 'react';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';
import { Tag } from '@hxnova/react-components/Tag';

export default function Example() {
  const [options, setOptions] = useState<string[]>(['option1', 'option2']);
  const handleOptionChange = (event: React.SyntheticEvent | null, values: string[]) => {
    setOptions(values);
  };
  return (
    <Dropdown
      multiple
      value={options}
      renderValue={(values) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          {values.map((v) => (
            <Tag key={v.value} label={v.label as string} variant="info" intensity="subtle" />
          ))}
        </div>
      )}
      onChange={handleOptionChange}
      placeholder="Select"
      style={{ width: '400px' }}
    >
      <Option value="option1">Option 1</Option>
      <Option value="option2">Option 2</Option>
      <Option value="option3">Option 3</Option>
      <Option value="option4">Option 4</Option>
      <Option value="option5">Option 5</Option>
    </Dropdown>
  );
}
