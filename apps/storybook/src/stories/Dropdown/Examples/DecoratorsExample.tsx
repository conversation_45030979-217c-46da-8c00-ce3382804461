import React from 'react';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';
import { Link } from '@hxnova/react-components/Link';
import Icon from '@hxnova/icons/Icon';

export default function Example() {
  return (
    <Dropdown
      placeholder="Assign a role"
      style={{ width: '400px' }}
      startDecorator={<Icon family="material" name="supervisor_account" />}
      endDecorator={
        <Link
          href="#sample"
          target="_blank"
          underline={'always'}
          onMouseDown={(event) => {
            event.stopPropagation();
          }}
        >
          Learn more
        </Link>
      }
    >
      <Option value="viewer">Viewer</Option>
      <Option value="editor">Editor</Option>
      <Option value="owner">Owner</Option>
    </Dropdown>
  );
}
