import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import StateExample from './Examples/StateExample';
import StateExampleSource from './Examples/StateExample.tsx?raw';
import FormAttributesExample from './Examples/FormAttributesExample';
import FormAttributesExampleSource from './Examples/FormAttributesExample.tsx?raw';
import FullWidthExample from './Examples/FullWidthExample';
import FullWidthExampleSource from './Examples/FullWidthExample.tsx?raw';
import ControlledExample from './Examples/ControlledExample';
import ControlledExampleSource from './Examples/ControlledExample.tsx?raw';
import MultipleExample from './Examples/MultipleExample';
import MultipleExampleSource from './Examples/MultipleExample.tsx?raw';
import RenderValueExample from './Examples/RenderValueExample';
import RenderValueExampleSource from './Examples/RenderValueExample.tsx?raw';
import DecoratorsExample from './Examples/DecoratorsExample';
import DecoratorsExampleSource from './Examples/DecoratorsExample.tsx?raw';
import ListboxExample from './Examples/ListboxExample';
import ListboxExampleSource from './Examples/ListboxExample.tsx?raw';

<Meta title="@hxnova/react-components/Dropdown/Examples" />
 
## Basic

The `Dropdown` component allows users to select items from a pop-up list of options. By default, it supports single option selection, making it ideal for simple use cases where only one choice is needed.

<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Different State

This section demonstrates the `Dropdown` component's various states, including disabled, error, and read-only modes. Each state provides visual feedback, enhancing user experience and accessibility.

<div className="sb-unstyled">
  <StateExample />
</div>
<CodeExpand code={ StateExampleSource } showBorderTop style={{marginTop: 16}}/>


## FormControl

Integrating the `Dropdown` component with `FormControl`, `FormLabel` and `FormHelperText` provides a structured way to manage form elements, including labels, helper texts, and validation states. This enhances the form's usability and accessibility.

<div className="sb-unstyled">
  <FormAttributesExample />
</div>
<CodeExpand code={FormAttributesExampleSource} showBorderTop style={{marginTop: 16}}/>

## Size

The `Dropdown` component supports different size variations, allowing developers to customize the appearance to fit the design of their application. This section illustrates `small`, `medium`(default), and `large` sizes.

<div className="sb-unstyled">
  <SizeExample />
</div>
<CodeExpand code={ SizeExampleSource } showBorderTop style={{marginTop: 16}}/>

## Full Width

The `fullWidth` prop can be used to make the dropdown take up the full width of its container.

<div className="sb-unstyled">
  <FullWidthExample />
</div>
<CodeExpand code={ FullWidthExampleSource } showBorderTop style={{marginTop: 16}}/>


## Controlled

A controlled `Dropdown` allows you to manage its value through state, enabling precise control over the selected option. This is useful for scenarios where the selection may depend on other inputs or actions.

<div className="sb-unstyled">
  <ControlledExample />
</div>
<CodeExpand code={ ControlledExampleSource } showBorderTop style={{marginTop: 16}}/>

## Multiple Selections

The `Dropdown` component can also support multiple selections, allowing users to select several options at once. This feature is ideal for scenarios where users need to make multiple related choices.

<div className="sb-unstyled">
  <MultipleExample />
</div>
<CodeExpand code={ MultipleExampleSource } showBorderTop style={{marginTop: 16}}/>


## Custom Rendering of Selected Values

This feature allows for custom rendering of selected values in the `Dropdown`. You can display selected options in a more visually appealing way, such as using tags or chips, enhancing the user interface.

<div className="sb-unstyled">
  <RenderValueExample />
</div>
<CodeExpand code={ RenderValueExampleSource } showBorderTop style={{marginTop: 16}}/>

## Decorators

The `Dropdown` component supports decorators, allowing you to enhance its appearance and functionality with additional elements. This feature is useful for providing context or actions related to the dropdown selection.

<div className="sb-unstyled">
  <DecoratorsExample />
</div>
<CodeExpand code={ DecoratorsExampleSource } showBorderTop style={{marginTop: 16}}/>

## List box

The `Dropdown` component allows for custom list boxes and option items, enabling features like checkboxes for multi-select functionality. This enhancement provides users with a more interactive and informative selection experience.

<div className="sb-unstyled">
  <ListboxExample />
</div>
<CodeExpand code={ ListboxExampleSource } showBorderTop style={{marginTop: 16}}/>