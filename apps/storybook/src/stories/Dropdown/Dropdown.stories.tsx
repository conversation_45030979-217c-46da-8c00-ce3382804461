import type { StoryFn } from '@storybook/react';
import { Dropdown, DropdownProps } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';
import { FormControl } from '@hxnova/react-components/FormControl';
import { FormLabel } from '@hxnova/react-components/FormLabel';
import { FormHelperText } from '@hxnova/react-components/FormHelperText';
import { action } from '@storybook/addon-actions';
import Icon from '@hxnova/icons/Icon';

const meta = {
  title: '@hxnova/react-components/Dropdown',
  component: Dropdown,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Components?node-id=1423-1103&p=f&t=8tA252Cv0BhDBHXY-0',
    },
  },
  tags: ['!autodocs'],
};

export default meta;

type DropdownTemplateProps<Value, Multiple extends boolean = false> = DropdownProps<Value, Multiple> & {
  labelText: string;
  supportingText: string;
  menuDensity: 'compact' | 'standard' | 'comfortable';
};

const DropdownTemplate: StoryFn<DropdownTemplateProps<string, false>> = (props) => {
  const { size, disabled, error, required, labelText, supportingText, menuDensity, ...rest } = props;
  return (
    <FormControl size={size} disabled={disabled} error={error} required={required}>
      {labelText && <FormLabel>{labelText}</FormLabel>}
      <Dropdown {...rest} slotProps={{ listbox: { density: menuDensity } }}>
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
        <Option value="option4">Option 4</Option>
        <Option value="option5">Option 5</Option>
        <Option value="option6">Option 6</Option>
      </Dropdown>
      {supportingText && <FormHelperText>{supportingText}</FormHelperText>}
    </FormControl>
  );
};

export const Basic = {
  render: DropdownTemplate,
  args: {
    size: 'medium',
    placeholder: 'Choose an option',
    labelText: 'Label',
    supportingText: 'Supporting text',
    menuDensity: 'standard',
    multiple: false,
    disabled: false,
    required: false,
    error: false,
    showErrorIcon: true,
    readOnly: false,
    style: {
      width: '300px',
    },
    startDecorator: <Icon family="material" name="calendar_today" />,
    onChange: (e: React.MouseEvent, value: string) => {
      action('onChange')(value);
    },
  },
  argTypes: {
    size: {
      control: { type: 'radio' },
      options: ['small', 'medium', 'large'],
    },
    placeholder: {
      type: 'string',
    },
    disabled: {
      type: 'boolean',
    },
    required: {
      type: 'boolean',
    },
    error: {
      type: 'boolean',
    },
    showErrorIcon: {
      type: 'boolean',
    },
    readOnly: {
      type: 'boolean',
    },
    labelText: {
      name: 'Label text',
    },
    supportingText: {
      name: 'Supporting text',
    },
    menuDensity: {
      name: 'Menu density',
      control: { type: 'radio' },
      options: ['compact', 'standard', 'comfortable'],
    },
    multiple: {
      type: 'boolean',
    },
  },
  parameters: {
    controls: {
      include: [
        'size',
        'placeholder',
        'disabled',
        'required',
        'error',
        'showErrorIcon',
        'readOnly',
        'Label text',
        'Supporting text',
        'Menu density',
        'multiple',
      ],
    },
  },
};
