import { Badge } from '@hxnova/react-components/Badge';
import Icon from '@hxnova/icons/Icon';

export default function ColorExample() {
  return (
    <div style={{ display: 'flex', gap: 24 }}>
      <Badge badgeContent={4} color="primary">
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={4} color="error">
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={4} color="warning">
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={4} color="info">
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={4} color="success">
        <Icon family="material" name="mail" size={24} />
      </Badge>
    </div>
  );
}
