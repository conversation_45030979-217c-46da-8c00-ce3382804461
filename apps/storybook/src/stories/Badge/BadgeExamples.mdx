import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import BasicExample from './Examples/BasicExample';
import ColorExampleSource from './Examples/ColorExample.tsx?raw';
import ColorExample from './Examples/ColorExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import VisibilityExampleSource from './Examples/VisibilityExample.tsx?raw';
import VisibilityExample from './Examples/VisibilityExample';
import MaxExampleSource from './Examples/MaxExample.tsx?raw';
import MaxExample from './Examples/MaxExample';
import AlignmentExampleSource from './Examples/AlignmentExample.tsx?raw';
import AlignmentExample from './Examples/AlignmentExample';

<Meta title="@hxnova/react-components/Badge/Examples" />
 
## Basic badge

Basic example of badge containing text, using primary color. The badge is applied to its children.
<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>


## Badge color

Badge comes with five colors: `primary`(default), `error`, `warning`, `info`, `success`. These can be adjusted using the `color` prop.
<div className="sb-unstyled">
  <ColorExample />
</div>
<CodeExpand code={ColorExampleSource} showBorderTop style={{marginTop: 16}}/>

## Badge size

Badge comes with two sizes: `small` and `large` (default). These can be adjusted using the `size` prop.
<div className="sb-unstyled"> 
    <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Badge visibility
The visibility of badges can be controlled using the `invisible` prop. The badge hides automatically when `badgeContent` is zero. You can override this with the `showZero` prop.
<div className="sb-unstyled"> 
    <VisibilityExample />
</div>
<CodeExpand code={VisibilityExampleSource} showBorderTop style={{marginTop: 16}}/>


## Badge maximum value
You can use the `max` prop to cap the value of the badge content.
<div className="sb-unstyled"> 
    <MaxExample />
</div>
<CodeExpand code={MaxExampleSource} showBorderTop style={{marginTop: 16}}/>

## Badge alignment
You can use the `anchorOrigin` prop to move the badge to any corner of the wrapped element.
<div className="sb-unstyled"> 
    <AlignmentExample />
</div>
<CodeExpand code={AlignmentExampleSource} showBorderTop style={{marginTop: 16}}/>



