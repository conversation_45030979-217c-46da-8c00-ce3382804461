# API Documentation

- [Badge](#badge)

# Badge

API reference docs for the React Badge component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Badge from '@hxnova/react-components/Badge';
// or
import { Badge } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **anchorOrigin** | ``{ vertical: "top" \| "bottom"; horizontal: "left" \| "right"; }`` | `{ vertical: 'top', horizontal: 'right', }` | The anchor of the badge. |
| **badgeContent** | `ReactNode` | - | The content rendered within the badge. |
| **children** | `ReactNode` | - | The badge will be added relative to this node. |
| **color** | ``"primary" \| "error" \| "info" \| "warning" \| "success"`` | `'primary'` | The system color of badge |
| **component** | `ElementType` | - |  |
| **disabled** | ``false \| true`` | - | The disable state of badge |
| **invisible** | ``false \| true`` | `false` | If `true`, the badge is invisible. |
| **max** | `number` | `99` | Max count to show. |
| **showZero** | ``false \| true`` | `false` | Controls whether the badge is hidden when `badgeContent` is zero. |
| **size** | ``"small" \| "large"`` | `'large'` | The badge size 'small' or 'large' |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaBadge-root | `'span'` | The component that renders the root. |
| badge | .NovaBadge-badge | `'span'` | The component that renders the badge. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaBadge-small | `small` | Styles applied to the badge `span` element if `size="small"`. |
| .NovaBadge-large | `large` | Styles applied to the badge `span` element if `size="large"`. |
| .NovaBadge-anchorOriginTopRight | `anchorOriginTopRight` | Styles applied to the badge `span` element if `anchorOrigin={{ 'top', 'right' }}`. |
| .NovaBadge-anchorOriginBottomRight | `anchorOriginBottomRight` | Styles applied to the badge `span` element if `anchorOrigin={{ 'bottom', 'right' }}`. |
| .NovaBadge-anchorOriginTopLeft | `anchorOriginTopLeft` | Styles applied to the badge `span` element if `anchorOrigin={{ 'top', 'left' }}`. |
| .NovaBadge-anchorOriginBottomLeft | `anchorOriginBottomLeft` | Styles applied to the badge `span` element if `anchorOrigin={{ 'bottom', 'left' }}`. |
| .NovaBadge-invisible | `invisible` | State class applied to the badge `span` element if `invisible={true}`. |
| .NovaBadge-colorPrimary | `colorPrimary` | Styles applied to the badge `span` element if `color="primary"`. |
| .NovaBadge-colorError | `colorError` | Styles applied to the badge `span` element if `color="error"`. |
| .NovaBadge-colorInfo | `colorInfo` | Styles applied to the badge `span` element if `color="info"`. |
| .NovaBadge-colorWarning | `colorWarning` | Styles applied to the badge `span` element if `color="warning"`. |
| .NovaBadge-colorSuccess | `colorSuccess` | Styles applied to the badge `span` element if `color="success"`. |
| .NovaBadge-disabled | `disabled` | Styles applied to the badge `span` element if `disabled` is true. |

