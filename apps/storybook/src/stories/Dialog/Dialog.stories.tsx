import { useState } from 'react';
import type { Meta } from '@storybook/react';
import { Button } from '@hxnova/react-components/Button';
import { unstable_createUseMediaQuery as createUseMediaQuery } from '@mui/system/useMediaQuery';
import { Dialog, DialogRootProps } from '@hxnova/react-components/Dialog';
import Icon from '@hxnova/icons/Icon';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Typography } from '@hxnova/react-components/Typography';
import { TextField } from '@hxnova/react-components/TextField';
import { IconButton } from '@hxnova/react-components/IconButton';

const useMediaQuery = createUseMediaQuery();

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: '@hxnova/react-components/Dialog',
  component: Dialog.Root,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1230-19907&p=f&t=U1jNTwoWcSWnx9W4-0',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<DialogRootProps>;

export default meta;

type DialogTypeExtension = {
  type?: 'default' | 'alert';
  showTopIcon?: boolean;
  title?: string;
  supportingText?: string;
  show3rdAction?: boolean;
  showTopDivider?: boolean;
  showBottomDivider?: boolean;
};

const Template = (props: DialogRootProps & DialogTypeExtension, args: { viewMode: string }) => {
  const { type, show3rdAction, showTopIcon, title, supportingText, ...rest } = props;
  const [open, setOpen] = useState(args.viewMode !== 'docs');
  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <Button onClick={handleClickOpen}>Open Dialog</Button>
      <Dialog.Root {...rest} open={open} onClose={handleClose}>
        <Dialog.Header
          icon={showTopIcon ? <Icon family="material" name="delete" size={24} /> : undefined}
          supportingText={supportingText}
        >
          {title}
        </Dialog.Header>
        {!show3rdAction && (
          <Dialog.Actions>
            <Button variant="text" onClick={handleClose}>
              Button
            </Button>
            <Button variant="filled" color={type === 'alert' ? 'error' : 'primary'} onClick={handleClose}>
              Button
            </Button>
          </Dialog.Actions>
        )}
        {show3rdAction && (
          <Dialog.Actions sx={{ justifyContent: 'space-between' }}>
            <Button variant="text" onClick={handleClose}>
              Button
            </Button>
            <div sx={{ display: 'flex', gap: '8px' }}>
              <Button variant="text" onClick={handleClose}>
                Button
              </Button>
              <Button variant="filled" color={type === 'alert' ? 'error' : 'primary'} onClick={handleClose}>
                Button
              </Button>
            </div>
          </Dialog.Actions>
        )}
      </Dialog.Root>
    </>
  );
};

const TemplateWithContent = (props: DialogRootProps & DialogTypeExtension, args: { viewMode: string }) => {
  const { type, showTopIcon, title, supportingText, showTopDivider, showBottomDivider, ...rest } = props;
  const [open, setOpen] = useState(args.viewMode !== 'docs');
  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <Button onClick={handleClickOpen}>Open Dialog</Button>
      <Dialog.Root {...rest} open={open} onClose={handleClose}>
        <Dialog.Header
          icon={showTopIcon ? <Icon family="material" name="restart_alt" size={24} /> : undefined}
          supportingText={supportingText}
        >
          {title}
        </Dialog.Header>
        <Dialog.Content
          topDivider={!!showTopDivider}
          bottomDivider={!!showBottomDivider}
          sx={(th) => ({
            gap: 16,
            color: th.vars.palette.onSurfaceVariant,
          })}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Avatar color="info">JC</Avatar>
            <Typography variant="bodySmall"><EMAIL></Typography>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Avatar color="warning">CW</Avatar>
            <Typography variant="bodySmall"><EMAIL></Typography>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Avatar color="error">EH</Avatar>
            <Typography variant="bodySmall"><EMAIL></Typography>
          </div>
        </Dialog.Content>
        <Dialog.Actions>
          <Button variant="text" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="filled" color={type === 'alert' ? 'error' : 'primary'} onClick={handleClose}>
            Accept
          </Button>
        </Dialog.Actions>
      </Dialog.Root>
    </>
  );
};

const TemplateWithResponsiveLayout = (props: DialogRootProps & DialogTypeExtension, args: { viewMode: string }) => {
  const { type, showTopIcon, title, supportingText, showTopDivider, showBottomDivider, ...rest } = props;

  const isSmall = useMediaQuery('(max-width:599px)');

  const [open, setOpen] = useState(args.viewMode !== 'docs');
  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <Button onClick={handleClickOpen}>Open Dialog</Button>
      <Dialog.Root {...rest} open={open} onClose={handleClose} fullScreen={isSmall}>
        <Dialog.Header
          icon={showTopIcon ? <Icon family="material" name="restart_alt" size={24} /> : undefined}
          supportingText={supportingText}
        >
          {isSmall ? (
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
              <IconButton variant="standard" onClick={handleClose}>
                <Icon family="material" name="close" size={24} />
              </IconButton>
              <Typography variant="titleMedium" style={{ fontWeight: 400, color: 'var(--palette-onSurface)' }}>
                {title}
              </Typography>
              <Button variant="filled" color={type === 'alert' ? 'error' : 'primary'} onClick={handleClose}>
                Save
              </Button>
            </div>
          ) : (
            title
          )}
        </Dialog.Header>
        <Dialog.Content topDivider={!!showTopDivider} bottomDivider={!!showBottomDivider} style={{ gap: 16 }}>
          <TextField label="Project name" placeholder="Project name" fullWidth />
          <TextField label="Project description" placeholder="Project description" fullWidth />
          <TextField label="Start date" placeholder="Start date" fullWidth />
          <TextField label="End date" placeholder="End date" fullWidth />
        </Dialog.Content>
        {!isSmall && (
          <Dialog.Actions>
            <Button variant="text" onClick={handleClose}>
              Cancel
            </Button>
            <Button variant="filled" color={type === 'alert' ? 'error' : 'primary'} onClick={handleClose}>
              Save
            </Button>
          </Dialog.Actions>
        )}
      </Dialog.Root>
    </>
  );
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Basic = {
  render: Template,
  args: {
    type: 'default',
    fullWidth: false,
    maxWidth: 'xs',
    fullScreen: false,
    showTopIcon: false,
    hideBackdrop: false,
    title: 'Basic dialog title',
    supportingText:
      'A dialog is a type of modal window that appears in front of app content to provide critical information, or ask for a decision.',
    show3rdAction: false,
  },
  argTypes: {
    type: {
      control: { type: 'radio' },
      options: ['default', 'alert'],
    },
    fullWidth: {
      type: 'boolean',
    },
    fullScreen: {
      type: 'boolean',
    },
    hideBackdrop: {
      type: 'boolean',
    },
    show3rdAction: {
      type: 'boolean',
      name: 'Show 3rd action',
    },
    maxWidth: {
      control: { type: 'radio' },
      options: [false, 'xs', 'sm', 'md', 'lg', 'xl'],
    },
    showTopIcon: {
      type: 'boolean',
      name: 'Show top icon',
    },
    title: {
      name: 'Title text',
    },
    supportingText: {
      name: 'Supporting text',
    },
  },
  parameters: {
    controls: {
      include: [
        'type',
        'fullWidth',
        'maxWidth',
        'fullScreen',
        'Show top icon',
        'hideBackdrop',
        'Title text',
        'Supporting text',
        'Show 3rd action',
      ],
    },
  },
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const DialogWithContent = {
  render: TemplateWithContent,
  args: {
    ...Basic.args,
    showTopIcon: true,
    title: 'Reset settings?',
    supportingText:
      'This will reset your app preferences back to their default settings. The following accounts will also be signed out:',
    showTopDivider: true,
    showBottomDivider: true,
  },
  argTypes: {
    ...Basic.argTypes,
    showTopDivider: {
      type: 'boolean',
      name: 'Show top divider',
    },
    showBottomDivider: {
      type: 'boolean',
      name: 'Show bottom divider',
    },
  },
  parameters: {
    controls: {
      include: [
        'type',
        'fullWidth',
        'maxWidth',
        'fullScreen',
        'Show top icon',
        'hideBackdrop',
        'Title text',
        'Supporting text',
        'Show top divider',
        'Show bottom divider',
      ],
    },
  },
};

export const DialogResponsiveLayout = {
  render: TemplateWithResponsiveLayout,
  args: {
    ...DialogWithContent.args,
    title: 'Create project',
    supportingText: '',
    showTopIcon: false,
    showTopDivider: false,
    showBottomDivider: false,
    fullWidth: true,
  },
  argTypes: {
    ...DialogWithContent.argTypes,
  },
  parameters: {
    controls: {
      include: [
        'type',
        'fullWidth',
        'maxWidth',
        'Show top icon',
        'hideBackdrop',
        'Title text',
        'Supporting text',
        'Show top divider',
        'Show bottom divider',
      ],
    },
  },
};
