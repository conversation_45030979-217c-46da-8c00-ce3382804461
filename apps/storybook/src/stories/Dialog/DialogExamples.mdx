import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import BasicExample from './Examples/BasicExample';
import CustomActionsExampleSource from './Examples/CustomActionsExample.tsx?raw';
import CustomActionsExample from './Examples/CustomActionsExample';
import ContentExampleSource from './Examples/ContentExample.tsx?raw';
import ContentExample from './Examples/ContentExample';
import FullScreenExampleSource from './Examples/FullScreenExample.tsx?raw';
import FullScreenExample from './Examples/FullScreenExample';
import MaxWidthExampleSource from './Examples/MaxWidthExample.tsx?raw';
import MaxWidthExample from './Examples/MaxWidthExample';

<Meta title="@hxnova/react-components/Dialog/Examples" />

## Introduction

Dialogs are implemented using a collection of related components:

* `Dialog`: the parent component that renders the modal.
* `DialogHeader`: a wrapper used for the icon, title and supporting text of a Dialog.
* `DialogContent`: an optional container for displaying the Dialog's content.
* `DialogActions`: an optional container for a Dialog's actions.
 
## Basic dialog

You can use the `Dialog`, `DialogHeader` and `DialogActions` components to build the basic dialog with title, supporting text and actions. You can change the button color to red to indicate it is a critical alert.

<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## With three action buttons

You can custom the dialog actions to put more buttons.

<div className="sb-unstyled">
  <CustomActionsExample />
</div>
<CodeExpand code={CustomActionsExampleSource} showBorderTop style={{marginTop: 16}}/>

## With header icon and dialog content 

* You can pass the `icon` to `DialogHeader`, it will show a top icon.
* You build the custom content via `DialogContent`.

<div className="sb-unstyled">
  <ContentExample />
</div>
<CodeExpand code={ContentExampleSource} showBorderTop style={{marginTop: 16}}/>


## Full screen

You can use the `fullScreen` prop to indicate whether the current dialog should cover the entire screen. This is useful for responsive layouts on small screens.

<div className="sb-unstyled">
  <FullScreenExample />
</div>
<CodeExpand code={FullScreenExampleSource} showBorderTop style={{marginTop: 16}}/>

## Optional sizes

You can set a dialog maximum width by using the `maxWidth` enumerable in combination with the `fullWidth` boolean. When the `fullWidth` prop is true, the dialog will adapt based on the `maxWidth` value.

<div className="sb-unstyled">
  <MaxWidthExample />
</div>
<CodeExpand code={MaxWidthExampleSource} showBorderTop style={{marginTop: 16}}/>
