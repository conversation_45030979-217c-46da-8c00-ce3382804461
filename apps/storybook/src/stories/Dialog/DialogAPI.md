# API Documentation

- [Dialog.Actions](#dialogactions)
- [Dialog.Content](#dialogcontent)
- [Dialog.Header](#dialogheader)
- [Dialog.Root](#dialogroot)

# Dialog.Actions

API reference docs for the React Dialog.Actions component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import DialogActions from '@hxnova/react-components/DialogActions';
// or
import { DialogActions } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDialogActions-root | `'div'` | The component that renders the root. |

<br><br>

# Dialog.Content

API reference docs for the React Dialog.Content component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import DialogContent from '@hxnova/react-components/DialogContent';
// or
import { DialogContent } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **bottomDivider** | ``false \| true`` | `false` | Display the bottom divider. |
| **topDivider** | ``false \| true`` | `false` | Display the top divider. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDialogContent-root | `'div'` | The component that renders the root. |

<br><br>

# Dialog.Header

API reference docs for the React Dialog.Header component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import DialogHeader from '@hxnova/react-components/DialogHeader';
// or
import { DialogHeader } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **icon** | `ReactNode` | - | Icon for dialog header. |
| **supportingText** | `ReactNode` | - | Supporting text for dialog header. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDialogHeader-root | `'div'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaDialogHeader-icon | `icon` | Class name applied to the icon element. |
| .NovaDialogHeader-headline | `headline` | Class name applied to the headline element. |
| .NovaDialogHeader-supportingText | `supportingText` | Class name applied to the supporting text element. |

<br><br>

# Dialog.Root

API reference docs for the React Dialog.Root component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import DialogRoot from '@hxnova/react-components/DialogRoot';
// or
import { DialogRoot } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **open*** | ``false \| true`` | - | If `true`, the component is shown. |
| **aria-describedby** | `string` | - | The id(s) of the element(s) that describe the dialog. |
| **aria-labelledby** | `string` | - | The id(s) of the element(s) that label the dialog. |
| **aria-modal** | ``false \| true \| "true" \| "false"`` | `true` | Informs assistive technologies that the element is modal.<br>It's added on the element with role="dialog". |
| **closeAfterTransition** | ``false \| true`` | `false` | When set to true the Modal waits until a nested Transition is completed before closing. |
| **container** | ``null \| Element \| () => Element \| null`` | - | An HTML element or function that returns one.<br>The `container` will have the portal children appended to it.<br>You can also provide a callback, which is called in a React layout effect.<br>This lets you set the container from a ref, and also makes server-side rendering possible.<br>By default, it uses the body of the top-level document object,<br>so it's simply `document.body` most of the time. |
| **disableAutoFocus** | ``false \| true`` | `false` | If `true`, the modal will not automatically shift focus to itself when it opens, and<br>replace it to the last focused element when it closes.<br>This also works correctly with any modal children that have the `disableAutoFocus` prop.<br>Generally this should never be set to `true` as it makes the modal less<br>accessible to assistive technologies, like screen readers. |
| **disableEnforceFocus** | ``false \| true`` | `false` | If `true`, the modal will not prevent focus from leaving the modal while open.<br>Generally this should never be set to `true` as it makes the modal less<br>accessible to assistive technologies, like screen readers. |
| **disableEscapeKeyDown** | ``false \| true`` | `false` | If `true`, hitting escape will not fire the `onClose` callback. |
| **disablePortal** | ``false \| true`` | `false` | The `children` will be under the DOM hierarchy of the parent component. |
| **disableRestoreFocus** | ``false \| true`` | `false` | If `true`, the modal will not restore focus to previously focused element once<br>modal is hidden or unmounted. |
| **disableScrollLock** | ``false \| true`` | `false` | Disable the scroll lock behavior. |
| **fullScreen** | ``false \| true`` | `false` | If `true`, the dialog is full-screen. |
| **fullWidth** | ``false \| true`` | `false` | If `true`, the dialog stretches to `maxWidth`.<br>Notice that the dialog width grow is limited by the default margin. |
| **hideBackdrop** | ``false \| true`` | `false` | If `true`, the backdrop is not rendered. |
| **keepMounted** | ``false \| true`` | `false` | Always keep the children in the DOM.<br>This prop can be useful in SEO situation or<br>when you want to maximize the responsiveness of the Modal. |
| **maxWidth** | ``false \| "xs" \| "sm" \| "md" \| "lg" \| "xl"`` | `'sm'` | Determine the max-width of the dialog.<br>The dialog width grows with the size of the screen.<br>Set to `false` to disable `maxWidth`. |
| **onBackdropClick** | ``ReactEventHandler<object>`` | - | ⚠️ **Deprecated**: Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.<br><br>Callback fired when the backdrop is clicked. |
| **onClose** | ``(event: object, reason: "backdropClick" \| "escapeKeyDown") => void`` | - | Callback fired when the component requests to be closed.<br>The `reason` parameter can optionally be used to control the response to `onClose`.<br>@param event The event source of the callback.<br>@param reason Can be: `"escapeKeyDown"`, `"backdropClick"`. |
| **onTransitionEnter** | ``() => void`` | - | A function called when a transition enters. |
| **onTransitionExited** | ``() => void`` | - | A function called when a transition has exited. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDialogRoot-root | `'div'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaDialogRoot-container | `container` | Styles applied to the container element. |
| .NovaDialogRoot-paper | `paper` | Styles applied to the Paper component. |
| .NovaDialogRoot-widthFalse | `widthFalse` | Styles applied to the Paper component if `maxWidth=false`. |
| .NovaDialogRoot-widthXs | `widthXs` | Styles applied to the Paper component if `maxWidth="xs"`. |
| .NovaDialogRoot-widthSm | `widthSm` | Styles applied to the Paper component if `maxWidth="sm"`. |
| .NovaDialogRoot-widthMd | `widthMd` | Styles applied to the Paper component if `maxWidth="md"`. |
| .NovaDialogRoot-widthLg | `widthLg` | Styles applied to the Paper component if `maxWidth="lg"`. |
| .NovaDialogRoot-widthXl | `widthXl` | Styles applied to the Paper component if `maxWidth="xl"`. |
| .NovaDialogRoot-fullWidth | `fullWidth` | Styles applied to the Paper component if `fullWidth={true}`. |
| .NovaDialogRoot-fullScreen | `fullScreen` | Styles applied to the Paper component if `fullScreen={true}`. |

