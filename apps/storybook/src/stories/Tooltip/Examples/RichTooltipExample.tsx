import { Popover } from '@hxnova/react-components/Popover';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function RichTooltipExample() {
  return (
    <Popover
      content={
        <Box
          style={{
            display: 'flex',
            width: '280px',
            height: '140px',
            flexDirection: 'column',
            justifyContent: 'space-between',
            gap: 'var(--spaceBetween-vertical-lg, 24px)',
          }}
        >
          <Typography variant="titleSmall" style={{ color: 'var(--palette-onSurfaceVariant)' }}>
            Title
          </Typography>
          <Typography variant="bodySmall" style={{ color: 'var(--palette-onSurfaceVariant)' }}>
            Supporting line text
          </Typography>
          <Box style={{ display: 'flex', justifyContent: 'flex-end', gap: 'var(--spaceBetween-horizontal-lg, 16px)' }}>
            <Button variant="text">Button</Button>
            <Button variant="filled">Button</Button>
          </Box>
        </Box>
      }
      showArrow
    >
      <Button variant="outlined">Click for Rich Tooltip</Button>
    </Popover>
  );
}
