# API Documentation

- [Collapse](#collapse)

# Collapse

API reference docs for the React Collapse component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Collapse from '@hxnova/react-components/Collapse';
// or
import { Collapse } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content node to be collapsed. |
| **className** | `string` | - | Override or extend the styles applied to the component. |
| **collapsedSize** | `string \| number` | `'0px'` | The width (horizontal) or height (vertical) of the container when collapsed. |
| **component** | `ElementType` | - | The component used for the root node. Either a string to use a HTML element or a component. |
| **easing** | `string \| { enter?: string; exit?: string }` | - | The transition timing function. You may specify a single easing or an object containing enter and exit values. |
| **in** | `boolean` | - | If `true`, the component will transition in. |
| **orientation** | `'horizontal' \| 'vertical'` | `'vertical'` | The transition orientation. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **timeout** | `number \| { enter?: number; exit?: number } \| 'auto'` | `duration.standard` | The duration for the transition, in milliseconds. You may specify a single timeout for all transitions, or individually with an object. Set to 'auto' to automatically calculate transition time based on height. |
| **TransitionComponent** | `ComponentType<any>` | `Transition` | The component used for the transition. |

## Transition props

The following props are available during the transition:

| Name | Type | Description |
| ---- | ---- | ----------- |
| **addEndListener** | `(node: HTMLElement, done: () => void) => void` | Add a custom transition end trigger. |
| **onEnter** | `(node: HTMLElement, isAppearing: boolean) => void` | Callback fired before the "entering" status is applied. |
| **onEntered** | `(node: HTMLElement, isAppearing: boolean) => void` | Callback fired after the "entered" status is applied. |
| **onEntering** | `(node: HTMLElement, isAppearing: boolean) => void` | Callback fired after the "entering" status is applied. |
| **onExit** | `(node: HTMLElement) => void` | Callback fired before the "exiting" status is applied. |
| **onExited** | `(node: HTMLElement) => void` | Callback fired after the "exited" status is applied. |
| **onExiting** | `(node: HTMLElement) => void` | Callback fired after the "exiting" status is applied. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCollapse-root | `root` | Class name applied to the root element. |
| .NovaCollapse-horizontal | `horizontal` | State class applied to the root element if `orientation="horizontal"`. |
| .NovaCollapse-vertical | `vertical` | State class applied to the root element if `orientation="vertical"`. |
| .NovaCollapse-entered | `entered` | State class applied to the root element when the transition has entered. |
| .NovaCollapse-hidden | `hidden` | State class applied to the root element when the transition has exited and `collapsedSize` = 0px. |
| .NovaCollapse-wrapper | `wrapper` | Class name applied to the outer wrapper element. |
| .NovaCollapse-wrapperInner | `wrapperInner` | Class name applied to the inner wrapper element. |
