import { useState } from 'react';
import type { Meta, StoryFn, StoryObj } from '@storybook/react';
import { Collapse, CollapseProps } from '@hxnova/react-components/Collapse';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

const meta = {
  title: '@hxnova/react-components/Transitions/Collapse',
  component: Collapse,
  parameters: {
    layout: 'centered',
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof Collapse>;

export default meta;
type Story = StoryObj<typeof meta>;

const Template: StoryFn<(props: CollapseProps) => JSX.Element> = ({ ...args }) => {
  const [checked, setChecked] = useState(false);

  return (
    <div>
      <Switch checked={checked} onChange={() => setChecked((prev: boolean) => !prev)} endDecorator="Toggle Collapse" />
      <Collapse {...args} in={checked}>
        <Box
          sx={{
            margin: 1,
            padding: 2,
            border: '1px solid var(--palette-outlineVariant)',
            borderRadius: 'var(--radius-2xs)',
            backgroundColor: 'var(--palette-surfaceContainer)',
            width: args.orientation === 'horizontal' ? 300 : 'auto',
            height: args.orientation === 'horizontal' ? 100 : 'auto',
          }}
        >
          <Typography>
            This is the content that will be collapsed. The Collapse component smoothly animates the{' '}
            {args.orientation === 'horizontal' ? 'width' : 'height'} changes.
          </Typography>
        </Box>
      </Collapse>
    </div>
  );
};

export const Default: Story = {
  render: Template,
  args: {
    orientation: 'vertical',
    collapsedSize: '0px',
    timeout: 300,
  },
  argTypes: {
    orientation: {
      control: { type: 'radio' },
      options: ['vertical', 'horizontal'],
    },
    collapsedSize: {
      control: 'text',
    },
    timeout: {
      control: { type: 'number' },
      defaultValue: 300,
    },
  },
  parameters: {
    controls: {
      include: ['orientation', 'collapsedSize', 'timeout'],
    },
  },
};
