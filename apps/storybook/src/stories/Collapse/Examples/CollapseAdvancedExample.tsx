import React from 'react';
import { Collapse } from '@hxnova/react-components/Collapse';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function CollapseAdvancedExample() {
  const [checkedFast, setCheckedFast] = React.useState(false);
  const [checkedSlow, setCheckedSlow] = React.useState(false);
  const [checkedAuto, setCheckedAuto] = React.useState(false);

  return (
    <Box style={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      <Typography variant="titleMedium" style={{ marginBottom: 1 }}>
        Collapse with Different Transition Speeds
      </Typography>

      <Box style={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        {/* Fast transition */}
        <Box style={{ flex: 1, minWidth: 250 }}>
          <Switch
            checked={checkedFast}
            onChange={() => setCheckedFast((prev) => !prev)}
            endDecorator="Toggle Fast Transition (150ms)"
          />
          <Collapse in={checkedFast} timeout={150}>
            <Box
              style={{
                margin: 1,
                padding: 2,
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 1,
                backgroundColor: 'var(--palette-surfaceContainer)',
                height: 100,
              }}
            >
              <Typography>Fast animation (150ms) - Good for small content changes.</Typography>
            </Box>
          </Collapse>
        </Box>

        {/* Slow transition */}
        <Box style={{ flex: 1, minWidth: 250 }}>
          <Switch
            checked={checkedSlow}
            onChange={() => setCheckedSlow((prev) => !prev)}
            endDecorator="Toggle Slow Transition (1000ms)"
          />
          <Collapse in={checkedSlow} timeout={1000}>
            <Box
              style={{
                margin: 1,
                padding: 2,
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 1,
                backgroundColor: 'var(--palette-secondaryContainer)',
                height: 100,
              }}
            >
              <Typography>Slow animation (1000ms) - More dramatic effect for large content.</Typography>
            </Box>
          </Collapse>
        </Box>

        {/* Auto transition */}
        <Box style={{ flex: 1, minWidth: 250 }}>
          <Switch
            checked={checkedAuto}
            onChange={() => setCheckedAuto((prev) => !prev)}
            endDecorator="Toggle Auto Transition (Content-based)"
          />
          <Collapse in={checkedAuto} timeout="auto">
            <Box
              style={{
                margin: 1,
                padding: 2,
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 1,
                backgroundColor: 'var(--palette-primaryContainer)',
                height: 100,
              }}
            >
              <Typography style={{ color: 'var(--palette-onPrimaryContainer)' }}>
                Auto timeout - Duration calculated based on content height. This provides the most natural feeling
                animation.
              </Typography>
            </Box>
          </Collapse>
        </Box>
      </Box>
    </Box>
  );
}
