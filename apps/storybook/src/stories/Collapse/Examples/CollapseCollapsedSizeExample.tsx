import React from 'react';
import { Collapse } from '@hxnova/react-components/Collapse';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function CollapseCollapsedSizeExample() {
  const [checked, setChecked] = React.useState(false);

  const handleChange = () => {
    setChecked((prev) => !prev);
  };

  return (
    <Box style={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      <Typography variant="titleMedium" style={{ marginBottom: 1 }}>
        Collapse with Different Collapsed Sizes
      </Typography>

      <Switch checked={checked} onChange={handleChange} endDecorator="Toggle Collapse" />

      <Box style={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        {/* Default collapsedSize (0px) */}
        <Box style={{ flex: 1, minWidth: 200 }}>
          <Typography variant="labelMedium" style={{ marginBottom: 1 }}>
            Default (0px)
          </Typography>
          <Collapse in={checked}>
            <Box
              style={{
                padding: 2,
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 1,
                backgroundColor: 'var(--palette-surfaceContainer)',
                height: 120,
              }}
            >
              <Typography>This content completely collapses to 0px height when closed.</Typography>
            </Box>
          </Collapse>
        </Box>

        {/* collapsedSize 40px */}
        <Box style={{ flex: 1, minWidth: 200 }}>
          <Typography variant="labelMedium" style={{ marginBottom: 1 }}>
            Collapsed Size: 40px
          </Typography>
          <Collapse in={checked} collapsedSize="40px">
            <Box
              style={{
                padding: 2,
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 1,
                backgroundColor: 'var(--palette-secondaryContainer)',
                height: 120,
              }}
            >
              <Typography>This content maintains a minimum height of 40px when collapsed.</Typography>
            </Box>
          </Collapse>
        </Box>

        {/* collapsedSize 80px */}
        <Box style={{ flex: 1, minWidth: 200 }}>
          <Typography variant="labelMedium" style={{ marginBottom: 1 }}>
            Collapsed Size: 80px
          </Typography>
          <Collapse in={checked} collapsedSize="80px">
            <Box
              style={{
                padding: 2,
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 1,
                backgroundColor: 'var(--palette-primaryContainer)',
                height: 120,
              }}
            >
              <Typography style={{ color: 'var(--palette-onPrimaryContainer)' }}>
                This content maintains a minimum height of 80px when collapsed.
              </Typography>
            </Box>
          </Collapse>
        </Box>
      </Box>
    </Box>
  );
}
