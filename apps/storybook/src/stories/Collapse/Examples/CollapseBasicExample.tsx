import React from 'react';
import { Collapse } from '@hxnova/react-components/Collapse';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function CollapseBasicExample() {
  const [checked, setChecked] = React.useState(false);

  const handleChange = () => {
    setChecked((prev) => !prev);
  };

  return (
    <div>
      <Switch checked={checked} onChange={handleChange} endDecorator="Show" />
      <Collapse in={checked}>
        <Box
          style={{
            margin: 1,
            padding: 2,
            border: '1px solid var(--palette-outlineVariant)',
            borderRadius: 1,
            backgroundColor: 'var(--palette-surfaceContainer)',
          }}
        >
          <Typography>
            This content will be collapsed when the switch is turned off. The Collapse component smoothly animates the
            height changes.
          </Typography>
        </Box>
      </Collapse>
    </div>
  );
}
