import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';                                                                                                                                                                                
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import BasicExample from './Examples/BasicExample';
import GroupExampleSource from './Examples/GroupExample.tsx?raw';
import GroupExample from './Examples/GroupExample';
import ListExampleSource from './Examples/ListExample.tsx?raw';
import ListExample from './Examples/ListExample';
import DensityExampleSource from './Examples/DensityExample.tsx?raw';
import DensityExample from './Examples/DensityExample';
import PlacementExampleSource from './Examples/PlacementExample.tsx?raw';
import PlacementExample from './Examples/PlacementExample';
import SelectedExampleSource from './Examples/SelectedExample.tsx?raw';
import SelectedExample from './Examples/SelectedExample';


<Meta title="@hxnova/react-components/Menu/Examples" />
 
## Basic Menu

The Basic menu is implemented using `Menu` and `MenuItem`.
<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>


## Menu with Density

The Menu component comes in three density: `compact`, `standard`(default), and `comfortable`. 
<div className="sb-unstyled"> 
    <DensityExample />
</div>
<CodeExpand code={DensityExampleSource} showBorderTop style={{marginTop: 16}}/>


## List Menu

You can use `MenuList` component to show a list menu.
<div className="sb-unstyled"> 
    <ListExample />
</div>
<CodeExpand code={ListExampleSource} showBorderTop style={{marginTop: 16}}/>


## Selected Menu

Use the `selected` prop to signal whether a MenuItem is selected or not.

<div className="sb-unstyled">
  <SelectedExample />
</div>
<CodeExpand code={SelectedExampleSource} showBorderTop style={{marginTop: 16}}/>


## Positioned Menu

You can use the `placement` prop in the `Menu` component to position itself.
<div className="sb-unstyled"> 
    <PlacementExample />
</div>
<CodeExpand code={PlacementExampleSource} showBorderTop style={{marginTop: 16}}/>


## Grouped Menu

You can display categories with the `ListSubheader` component.
<div className="sb-unstyled"> 
    <GroupExample />
</div>
<CodeExpand code={GroupExampleSource} showBorderTop style={{marginTop: 16}}/>

