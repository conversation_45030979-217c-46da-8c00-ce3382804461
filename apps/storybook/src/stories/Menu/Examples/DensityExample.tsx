import React from 'react';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import { MenuList } from '@hxnova/react-components/MenuList';
import { Typography } from '@hxnova/react-components/Typography';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Divider } from '@hxnova/react-components/Divider';
import Icon from '@hxnova/icons/Icon';

export default function DensityMenu() {
  return (
    <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: '16px' }}>
      {(['compact', 'standard', 'comfortable'] as const).map((density) => (
        <div key={density || 'default'}>
          <Typography variant="bodySmall" style={{ marginBottom: '16px' }}>
            <code>{density ? `density="${density}"` : '(default)'}</code>
          </Typography>
          <MenuList density={density}>
            <MenuItem>
              <ListItemDecorator /> Single
            </MenuItem>
            <MenuItem>
              <ListItemDecorator />
              1.15
            </MenuItem>
            <MenuItem>
              <ListItemDecorator />
              Double
            </MenuItem>
            <MenuItem>
              <ListItemDecorator>
                <Icon family="material" name="check" size={24} />
              </ListItemDecorator>
              Custom: 1.2
            </MenuItem>
            <Divider />
            <MenuItem>Add space before paragraph</MenuItem>
            <MenuItem>Add space after paragraph</MenuItem>
            <Divider />
            <MenuItem>Custom spacing...</MenuItem>
          </MenuList>
        </div>
      ))}
    </div>
  );
}
