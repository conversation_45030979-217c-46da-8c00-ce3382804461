import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { Menu } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';

const options = ['Developing project', 'Production project', 'Staging project'];

export default function SimpleListMenu() {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [selectedIndex, setSelectedIndex] = React.useState(1);
  const open = Boolean(anchorEl);
  const handleClickListItem = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuItemClick = (event: React.MouseEvent<HTMLElement>, index: number) => {
    setSelectedIndex(index);
    setAnchorEl(null);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <List aria-label="Device settings" style={{ width: '200px' }}>
        <ListItemButton
          id="lock-button"
          aria-haspopup="listbox"
          aria-controls="lock-menu"
          aria-label="when device is locked"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClickListItem}
        >
          <ListItemContent primary="When device is locked" secondary={options[selectedIndex]} />
        </ListItemButton>
      </List>
      <Menu id="lock-menu" anchorEl={anchorEl} open={open} onClose={handleClose}>
        {options.map((option, index) => (
          <MenuItem
            key={option}
            disabled={index === 0}
            selected={index === selectedIndex}
            onClick={(event) => handleMenuItemClick(event, index)}
          >
            {option}
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
}
