import React from 'react';
import { But<PERSON> } from '@hxnova/react-components/Button';
import { ListSubheader } from '@hxnova/react-components/ListSubheader';
import { Menu } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';

export default function GroupedMenu() {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <Button
        id="basic-button"
        aria-controls={open ? 'grouped-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
      >
        Dashboard
      </Button>
      <Menu id="grouped-menu" anchorEl={anchorEl} open={open} onClose={handleClose}>
        <ListSubheader>Category 1</ListSubheader>
        <MenuItem onClick={handleClose}>Option 1</MenuItem>
        <MenuItem onClick={handleClose}>Option 2</MenuItem>
        <ListSubheader>Category 2</ListSubheader>
        <MenuItem onClick={handleClose}>Option 3</MenuItem>
        <MenuItem onClick={handleClose}>Option 4</MenuItem>
      </Menu>
    </div>
  );
}
