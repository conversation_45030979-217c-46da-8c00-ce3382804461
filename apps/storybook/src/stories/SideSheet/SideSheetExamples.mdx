import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';  
import * as SideSheetStories from './SideSheet.stories';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import AnchorExample from './Examples/AnchorExample';
import AnchorExampleSource from './Examples/AnchorExample.tsx?raw';
import WidthExample from './Examples/WidthExample';
import WidthExampleSource from './Examples/WidthExample.tsx?raw';

<Meta title="@hxnova/react-components/SideSheet/Examples" />
 
## Basic SideSheet

The SideSheet component is a panel that slides in from the edge of the screen. It contains supplementary content and actions.

<div className="sb-unstyled">
    <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Anchor Position

SideSheets can slide in from either the left or right side of the screen using the `anchor` prop.
* Always place side sheets to the right edge of the screen.
* In right-to-left (RTL) languages, side sheets should appear on the left edge of the screen with all elements reversed.

<div className="sb-unstyled">
    <AnchorExample />
</div>
<CodeExpand code={AnchorExampleSource} showBorderTop style={{marginTop: 16}}/>

## Custom Width

You can customize the width of the SideSheet using the `width` prop.

<div className="sb-unstyled">
    <WidthExample />
</div>
<CodeExpand code={WidthExampleSource} showBorderTop style={{marginTop: 16}}/> 