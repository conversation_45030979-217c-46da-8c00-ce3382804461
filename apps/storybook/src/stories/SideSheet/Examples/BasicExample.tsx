import React, { useState } from 'react';
import { SideSheet } from '@hxnova/react-components/SideSheet';
import { Button } from '@hxnova/react-components/Button';
import { Typography } from '@hxnova/react-components/Typography';

export default function BasicExample() {
  const [open, setOpen] = useState(false);

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <div>
      <Button onClick={() => setOpen(true)}>Open Side Sheet</Button>
      <SideSheet.Root open={open} onClose={handleClose} anchor="right" width={360}>
        <SideSheet.Content>
          <Typography>This is a basic side sheet that appears from the right side of the screen.</Typography>
          <br />
          <Typography>Click outside the sheet, press Escape, or use the close button to dismiss it.</Typography>
        </SideSheet.Content>
      </SideSheet.Root>
    </div>
  );
}
