import React, { useState } from 'react';
import { SideSheet } from '@hxnova/react-components/SideSheet';
import { Button } from '@hxnova/react-components/Button';
import { Typography } from '@hxnova/react-components/Typography';
import { IconButton } from '@hxnova/react-components/IconButton';
import Icon from '@hxnova/icons/Icon';

export default function WidthExample() {
  const [openNarrow, setOpenNarrow] = useState(false);
  const [openWide, setOpenWide] = useState(false);

  return (
    <div style={{ display: 'flex', gap: '16px' }}>
      <Button onClick={() => setOpenNarrow(true)}>Narrow (320px)</Button>
      <Button onClick={() => setOpenWide(true)}>Wide (500px)</Button>

      {/* Narrow Side Sheet */}
      <SideSheet.Root open={openNarrow} onClose={() => setOpenNarrow(false)} width={320}>
        <SideSheet.Header>
          <IconButton variant="neutral" style={{ width: 'fit-content' }} onClick={() => setOpenNarrow(false)}>
            <Icon family="material" name="arrow_back" size={24} />
          </IconButton>
          <Typography variant="titleLarge" style={{ fontWeight: 'normal' }}>
            Narrow Side Sheet
          </Typography>
          <IconButton
            size="small"
            variant="neutral"
            style={{ width: 'fit-content' }}
            onClick={() => setOpenNarrow(false)}
          >
            <Icon family="material" name="close" size={24} />
          </IconButton>
        </SideSheet.Header>
        <SideSheet.Content>
          <Typography>This side sheet has a width of 320px.</Typography>
        </SideSheet.Content>
        <SideSheet.Footer>
          <Button onClick={() => setOpenNarrow(false)}>Close</Button>
        </SideSheet.Footer>
      </SideSheet.Root>

      {/* Wide Side Sheet */}
      <SideSheet.Root open={openWide} onClose={() => setOpenWide(false)} width={500}>
        <SideSheet.Header>
          <IconButton variant="neutral" style={{ width: 'fit-content' }} onClick={() => setOpenWide(false)}>
            <Icon family="material" name="arrow_back" size={24} />
          </IconButton>
          <Typography variant="titleLarge" style={{ fontWeight: 'normal' }}>
            Wide Side Sheet
          </Typography>
          <IconButton size="small" variant="neutral" onClick={() => setOpenWide(false)}>
            <Icon family="material" name="close" size={24} />
          </IconButton>
        </SideSheet.Header>
        <SideSheet.Content>
          <Typography>This side sheet has a width of 500px, making it wider than the default.</Typography>
          <br />
          <Typography>More content can fit in this wider layout.</Typography>
        </SideSheet.Content>
        <SideSheet.Footer>
          <Button onClick={() => setOpenWide(false)}>Close</Button>
        </SideSheet.Footer>
      </SideSheet.Root>
    </div>
  );
}
