import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import * as FloatingActionBarStories from './FloatingActionBar.stories';
import { FloatingActionBar } from '@hxnova/react-components';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import VerticalExample from './Examples/VerticalExample';
import VerticalExampleSource from './Examples/VerticalExample.tsx?raw';
import BasicDropdownExample from './Examples/BasicDropdownExample';
import BasicDropdownExampleSource from './Examples/BasicDropdownExample.tsx?raw';
import ClickAwayDropdownExample from './Examples/ClickAwayDropdownExample';
import ClickAwayDropdownExampleSource from './Examples/ClickAwayDropdownExample.tsx?raw';


<Meta title="@hxnova/react-components/Floating Action Bar/Examples" />

## Basic FloatingActionBar

The basic example includes multiple action buttons. You can click the buttons to toggle their selected state - clicking a selected button will deselect it, and clicking an unselected button will select it.

<div className="sb-unstyled">
    <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## FloatingActionBar Sizes

FloatingActionBar come in three sizes: small, medium (default), and large. These can be adjusted using the `size` prop.

<div className="sb-unstyled">
    <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Vertical FloatingActionBar

To render FloatingActionBar vertically, set the `orientation` prop to `vertical` on the `<FloatingActionBar>` component.

<div className="sb-unstyled">
    <VerticalExample />
</div>
<CodeExpand code={VerticalExampleSource} showBorderTop style={{marginTop: 16}}/>


## Dropdown 

Two dropdown menu scenarios are provided:

### Basic Dropdown

Basic dropdown: requires clicking the action button again to close the dropdown menu

<div className="sb-unstyled">
    <BasicDropdownExample />
</div>
<CodeExpand code={BasicDropdownExampleSource} showBorderTop style={{marginTop: 16}}/>

### Dropdown with ClickAway

Dropdown with ClickAway: automatically closes the dropdown menu when clicking outside

<div className="sb-unstyled">
    <ClickAwayDropdownExample />
</div>
<CodeExpand code={ClickAwayDropdownExampleSource} showBorderTop style={{marginTop: 16}}/>

