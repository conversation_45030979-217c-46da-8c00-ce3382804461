import { FloatingActionBar } from '@hxnova/react-components/FloatingActionBar';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <FloatingActionBar.Root orientation="vertical">
        <FloatingActionBar.Item selected>
          <Icon family="material" name="bookmark_border" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item showDivider>
          <Icon family="material" name="palette" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item>
          <Icon family="material" name="apps" />
        </FloatingActionBar.Item>
      </FloatingActionBar.Root>
    </div>
  );
}
