# API Documentation

- [FloatingAction](#floatingaction)
- [FloatingActionBar](#floatingactionbar)

# FloatingAction

API reference docs for the React FloatingAction component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import FloatingAction from '@hxnova/react-components/FloatingAction';
// or
import { FloatingAction } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<ButtonActions>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **className** | `string` | - |  |
| **disabled** | ``false \| true`` | `false` | If `true`, the component is disabled. |
| **focusableWhenDisabled** | ``false \| true`` | `false` | If `true`, allows a disabled button to receive focus. |
| **href** | `string` | - |  |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **rootElementName** | `keyof HTMLElementTagNameMap` | `'button'` | The HTML element that is ultimately rendered, for example 'button' or 'a' |
| **selected** | ``false \| true`` | `false` | Whether the component is selected. |
| **showDivider** | ``false \| true`` | `false` | Display a divider after the component.<br>- When FloatingActionBar is horizontal, the divider will be vertical (on the right)<br>- When FloatingActionBar is vertical, the divider will be horizontal (on the bottom) |
| **showDropdownIcon** | ``false \| true`` | `false` | Whether a dropdown icon should appear on the corner. |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | How large the FloatingAction should be. |
| **tabIndex** | `number` | - |  |
| **to** | `string` | - |  |
| **type** | ``"button" \| "submit" \| "reset"`` | `'button'` | Type attribute applied when the `component` is `button`. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaFloatingAction-root | `root` | Styles applied to the root element. |
| .NovaFloatingAction-dropdown | `dropdown` | Styles applied to the dropdown element. |

<br><br>

# FloatingActionBar

API reference docs for the React FloatingActionBar component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import FloatingActionBar from '@hxnova/react-components/FloatingActionBar';
// or
import { FloatingActionBar } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - |  |
| **orientation** | ``"horizontal" \| "vertical"`` | `'horizontal'` | The component orientation. |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | How large the FloatingActionBar contents should be. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaFloatingActionBar-root | `'div'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaFloatingActionBar-horizontal | `horizontal` | Styles applied to the root element if `orientation="horizontal"`. |
| .NovaFloatingActionBar-vertical | `vertical` | Styles applied to the root element if `orientation="vertical"`. |
| .NovaFloatingActionBar-sizeSmall | `sizeSmall` | Styles name applied to the root element if `size="small"`. |
| .NovaFloatingActionBar-sizeMedium | `sizeMedium` | Styles name applied to the root element if `size="medium"`. |
| .NovaFloatingActionBar-sizeLarge | `sizeLarge` | Styles name applied to the root element if `size="large"`. |

