# API Documentation

- [Stack](#stack)

# Stack

API reference docs for the React Stack component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Stack from '@hxnova/react-components/Stack';
// or
import { Stack } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **direction** | ``"row" | "row-reverse" | "column" | "column-reverse" | ("row" | "row-reverse" | "column" | "column-reverse")[] | Partial<Record<Breakpoint, "row" | "row-reverse" | "column" | "column-reverse">>`` | `'column'` | Defines the `flex-direction` style property.<br>It is applied for all screen sizes. |
| **divider** | `ReactNode` | - | Add an element between each child. |
| **spacing** | ``string | number | (string | number)[] | Partial<Record<Breakpoint, string | number>>`` | `0` | Defines the space between immediate children. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaStack-root | `root` | Styles applied to the root element. |

