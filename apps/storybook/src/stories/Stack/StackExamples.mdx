import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import StackBasicExample from './Examples/StackBasicExample';
import StackBasicExampleSource from './Examples/StackBasicExample.tsx?raw';
import StackDirectionExample from './Examples/StackDirectionExample';
import StackDirectionExampleSource from './Examples/StackDirectionExample.tsx?raw';
import StackDividerExample from './Examples/StackDividerExample';
import StackDividerExampleSource from './Examples/StackDividerExample.tsx?raw';
import StackResponsiveExample from './Examples/StackResponsiveExample';
import StackResponsiveExampleSource from './Examples/StackResponsiveExample.tsx?raw';

<Meta title="@hxnova/react-components/Layout/Stack/Examples" />

## Basic Stack
The `Stack` component serves as a flexible container for arranging elements, with the `spacing` prop controlling the gap between children. In this example, three items are stacked vertically, with a spacing factor of `4px` multiplied by the specified value, demonstrating how the Stack component can efficiently manage layout and spacing.

<div className="sb-unstyled">
    <StackBasicExample />
</div>
<CodeExpand code={StackBasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Stack Direction
By default, the `Stack` component arranges items vertically. This demo illustrates how to use the `direction` prop to change the layout to horizontal. Here, three items are aligned in a row, showcasing the versatility of the Stack component in adapting to different layout requirements while maintaining consistent spacing.

<div className="sb-unstyled">
  <StackDirectionExample />
</div>
<CodeExpand code={ StackDirectionExampleSource } showBorderTop style={{marginTop: 16}}/>

## Stack Divider
This `Stack` component provides the `divider` prop, which allows for the insertion of an element between each child. This feature is particularly effective when used with the `Divider` component, enhancing the visual separation between items and improving the overall aesthetic of the stacked layout.

<div className="sb-unstyled">
  <StackDividerExample />
</div>
<CodeExpand code={ StackDividerExampleSource } showBorderTop style={{marginTop: 16}}/>

## Stack Responsive
The `Stack` component supports responsive design by allowing the `direction` and `spacing` values to change based on the active breakpoint. This flexibility enables developers to create layouts that adapt to various screen sizes, ensuring optimal display and usability across devices.

<div className="sb-unstyled">
  <StackResponsiveExample />
</div>
<CodeExpand code={ StackResponsiveExampleSource } showBorderTop style={{marginTop: 16}}/>