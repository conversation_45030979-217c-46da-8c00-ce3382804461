import React from 'react';
import { Stack } from '@hxnova/react-components/Stack';
import { styled } from '@pigment-css/react';

const Item = styled('div')(({ theme }) => ({
  border: `1px solid ${theme.vars.palette.outlineDisabled}`,
  borderRadius: '4px',
  padding: '4px',
  textAlign: 'center',
}));

export default function StackDemo() {
  return (
    <Stack spacing={2}>
      <Item>Item 1</Item>
      <Item>Item 2</Item>
      <Item>Item 3</Item>
    </Stack>
  );
}
