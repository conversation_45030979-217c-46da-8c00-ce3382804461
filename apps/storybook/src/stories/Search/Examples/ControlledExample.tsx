import React from 'react';
import { Search } from '@hxnova/react-components/Search';
import { IconButton } from '@hxnova/react-components/IconButton';
import Icon from '@hxnova/icons/Icon';

export default function ControlledExample() {
  const [value, setValue] = React.useState('Hello');
  const inputRef = React.useRef<HTMLInputElement>(null);

  return (
    <div style={{ display: 'flex', gap: 8 }}>
      <Search
        id="controlled"
        value={value}
        onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
          setValue(event.target.value);
        }}
        onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) => {
          if (event.key === 'Enter') {
            alert(`You searched for: ${value.trim()}`);
          }
        }}
        startDecorator={<Icon family="material" name="search" size={24} />}
        endDecorator={
          <IconButton variant="neutral">
            <Icon family="material" name="mic_none" size={24} />
          </IconButton>
        }
      />
      <Search
        id="uncontrolled"
        ref={inputRef}
        defaultValue="Hello"
        startDecorator={<Icon family="material" name="search" size={24} />}
        endDecorator={
          <IconButton variant="neutral">
            <Icon family="material" name="mic_none" size={24} />
          </IconButton>
        }
        onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) => {
          if (inputRef.current && event.key === 'Enter') {
            alert(`You searched for: ${inputRef.current.value.trim()}`);
          }
        }}
      />
    </div>
  );
}
