import { Search } from '@hxnova/react-components/Search';
import { IconButton } from '@hxnova/react-components/IconButton';
import Icon from '@hxnova/icons/Icon';

export default function DecoratorsExample() {
  return (
    <Search
      placeholder="Hinted search text"
      startDecorator={<Icon family="material" name="menu" size={24} />}
      endDecorator={
        <IconButton variant="neutral">
          <Icon family="material" name="mic_none" size={24} />
        </IconButton>
      }
    />
  );
}
