# API Documentation

- [Search](#search)

# Search

API reference docs for the React Search component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Search from '@hxnova/react-components/Search';
// or
import { Search } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **endDecorator** | `ReactNode` | - | Trailing adornment for this input. |
| **fullWidth** | ``false \| true`` | `false` | If `true`, the input will take up the full width of its container. |
| **id** | `string` | - | The id of the `input` element. |
| **name** | `string` | - | Name attribute of the `input` element. |
| **onBlur** | ``FocusEventHandler<HTMLInputElement \| HTMLTextAreaElement>`` | - | Callback fired when the `input` is blurred.<br>Notice that the first argument (event) might be undefined. |
| **onChange** | ``ChangeEventHandler<HTMLInputElement \| HTMLTextAreaElement>`` | - | Callback fired when the value is changed.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (string). |
| **onFocus** | ``FocusEventHandler<HTMLInputElement \| HTMLTextAreaElement>`` | - |  |
| **onKeyDown** | ``KeyboardEventHandler<HTMLInputElement \| HTMLTextAreaElement>`` | - |  |
| **onKeyUp** | ``KeyboardEventHandler<HTMLInputElement \| HTMLTextAreaElement>`` | - |  |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | The size of the component. |
| **startDecorator** | `ReactNode` | - | Leading adornment for this input. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaSearch-root | `'div'` | The component that renders the root. |
| input | .NovaSearch-input | `'div'` | The component that renders the input. |
| htmlInput | .NovaSearch-htmlInput | `'div'` | The component that renders the inner html input. |

