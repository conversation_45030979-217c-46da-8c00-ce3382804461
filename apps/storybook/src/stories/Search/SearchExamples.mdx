import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import ControlledExampleSource from './Examples/ControlledExample.tsx?raw';
import ControlledExample from './Examples/ControlledExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import FullWidthExampleSource from './Examples/FullWidthExample.tsx?raw';
import FullWidthExample from './Examples/FullWidthExample';
import DecoratorsExampleSource from './Examples/DecoratorsExample.tsx?raw';
import DecoratorsExample from './Examples/DecoratorsExample';
import SuggestionsExampleSource from './Examples/SuggestionsExample.tsx?raw';
import SuggestionsExample from './Examples/SuggestionsExample';

<Meta title="@hxnova/react-components/Search/Examples" />

## Uncontrolled vs. Controlled

The component can be controlled or uncontrolled. This allows you to control its value programmatically.

<div className="sb-unstyled">
  <ControlledExample />
</div>
<CodeExpand code={ControlledExampleSource} showBorderTop style={{marginTop: 16}}/>


## Size 

Search comes with three sizes: small, medium(default), large. These can be adjusted using the `size` prop.

<div className="sb-unstyled">
  <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Full width 

The `fullWidth` prop can be used to make the input take up the full width of its container.

<div className="sb-unstyled">
  <FullWidthExample />
</div>
<CodeExpand code={FullWidthExampleSource} showBorderTop style={{marginTop: 16}}/>


## Input decorators

You can add prefixes, suffixes or operation icons to the input using `startDecorator` and `endDecorator`.

<div className="sb-unstyled">
  <DecoratorsExample />
</div>
<CodeExpand code={ DecoratorsExampleSource } showBorderTop style={{marginTop: 16}}/>


## Search input with suggestions

Use `Autocomplete` component with `freeSolo` prop to create a search input with suggestions experience.

<div className="sb-unstyled">
  <SuggestionsExample />
</div>
<CodeExpand code={SuggestionsExampleSource} showBorderTop style={{marginTop: 16}}/>





