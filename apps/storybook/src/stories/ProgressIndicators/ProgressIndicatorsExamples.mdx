import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import * as ProgressIndicatorsStories from './ProgressIndicators.stories';
import { CircularProgress, LinearProgress } from '@hxnova/react-components';
import CircularBasicExample from './Examples/CircularBasicExample';
import CircularBasicExampleSource from './Examples/CircularBasicExample.tsx?raw';
import CircularDeterminateExample from './Examples/CircularDeterminateExample';
import CircularDeterminateExampleSource from './Examples/CircularDeterminateExample.tsx?raw';
import CircularLabelExample from './Examples/CircularLabelExample';
import CircularLabelExampleSource from './Examples/CircularLabelExample.tsx?raw';
import CircularSizeExample from './Examples/CircularSizeExample';
import CircularSizeExampleSource from './Examples/CircularSizeExample.tsx?raw';
import CircularColorExample from './Examples/CircularColorExample';
import CircularColorExampleSource from './Examples/CircularColorExample.tsx?raw';
import CircularThicknessExample from './Examples/CircularThicknessExample';
import CircularThicknessExampleSource from './Examples/CircularThicknessExample.tsx?raw';
import LinearBasicExample from './Examples/LinearBasicExample';
import LinearBasicExampleSource from './Examples/LinearBasicExample.tsx?raw';
import LinearDeterminateExample from './Examples/LinearDeterminateExample';
import LinearDeterminateExampleSource from './Examples/LinearDeterminateExample.tsx?raw';
import LinearLabelExample from './Examples/LinearLabelExample';
import LinearLabelExampleSource from './Examples/LinearLabelExample.tsx?raw';
import LinearColorExample from './Examples/LinearColorExample';
import LinearColorExampleSource from './Examples/LinearColorExample.tsx?raw';
import LinearThicknessExample from './Examples/LinearThicknessExample';
import LinearThicknessExampleSource from './Examples/LinearThicknessExample.tsx?raw';

<Meta title="@hxnova/react-components/Progress indicators/Examples" />

# Progress Indicators

Progress indicators inform users about the status of ongoing processes.

## Circular Progress

### Basic Usage

The `CircularProgress` component displays a circular indicator for processes that take an indeterminate amount of time.

<div className="doc-story sb-story sb-unstyled">
  <CircularBasicExample />
</div>
<CodeExpand code={CircularBasicExampleSource} showBorderTop style={{marginTop: 16}}/>

### Determinate Mode

Use the `variant="determinate"` prop along with a `value` (0-100) to show specific progress.

<div className="doc-story sb-story sb-unstyled">
  <CircularDeterminateExample />
</div>
<CodeExpand code={CircularDeterminateExampleSource} showBorderTop style={{marginTop: 16}}/>

### Circular with a label

You can add a label inside the progress indicator.
<div className="doc-story sb-story sb-unstyled">
  <CircularLabelExample />
</div>
<CodeExpand code={CircularLabelExampleSource} showBorderTop style={{marginTop: 16}}/>


### Size Variations

The `size` prop accepts a number or string value to control the component's dimensions.

<div className="doc-story sb-story sb-unstyled">
  <CircularSizeExample />
</div>
<CodeExpand code={CircularSizeExampleSource} showBorderTop style={{marginTop: 16}}/>

### Color Variations

The `color` prop accepts values like `primary`, `error`, `success`, `info`, `warning`, or `inherit`.

<div className="doc-story sb-story sb-unstyled">
  <CircularColorExample />
</div>
<CodeExpand code={CircularColorExampleSource} showBorderTop style={{marginTop: 16}}/>

### Thickness Customization

The `thickness` prop controls the width of the progress indicator.

<div className="doc-story sb-story sb-unstyled">
  <CircularThicknessExample />
</div>
<CodeExpand code={CircularThicknessExampleSource} showBorderTop style={{marginTop: 16}}/>

## Linear Progress

### Basic Usage

The `LinearProgress` component displays a horizontal indicator for processes that take an indeterminate amount of time.

<div className="doc-story sb-story sb-unstyled">
  <LinearBasicExample />
</div>
<CodeExpand code={LinearBasicExampleSource} showBorderTop style={{marginTop: 16}}/>

### Determinate Mode

Use the `variant="determinate"` prop along with a `value` (0-100) to show specific progress.

<div className="doc-story sb-story sb-unstyled">
  <LinearDeterminateExample />
</div>
<CodeExpand code={LinearDeterminateExampleSource} showBorderTop style={{marginTop: 16}}/>

### Linear with a label

You can add a label inside the Linear indicator.
<div className="doc-story sb-story sb-unstyled">
  <LinearLabelExample />
</div>
<CodeExpand code={LinearLabelExampleSource} showBorderTop style={{marginTop: 16}}/>


### Color Variations

The `color` prop accepts values like  `primary`, `error`, `success`, `info`, `warning`, or `inherit`.

<div className="doc-story sb-story sb-unstyled">
  <LinearColorExample />
</div>
<CodeExpand code={LinearColorExampleSource} showBorderTop style={{marginTop: 16}}/>

### Thickness Customization

The `thickness` prop controls the height of the progress indicator.

<div className="doc-story sb-story sb-unstyled">
  <LinearThicknessExample />
</div>
<CodeExpand code={LinearThicknessExampleSource} showBorderTop style={{marginTop: 16}}/>
