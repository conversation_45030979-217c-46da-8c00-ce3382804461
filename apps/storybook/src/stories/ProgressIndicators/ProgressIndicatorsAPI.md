# API Documentation

- [CircularProgress](#circularprogress)
- [LinearProgress](#linearprogress)

# CircularProgress

API reference docs for the React CircularProgress component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import CircularProgress from '@hxnova/react-components/CircularProgress';
// or
import { CircularProgress } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **color** | ``"inherit" \| "primary" \| "error" \| "info" \| "success" \| "warning"`` | `'primary'` | The color of the component. |
| **component** | `ElementType` | - |  |
| **size** | `number` | `48` | The size of the component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **thickness** | `number` | `3` | The thickness of the circle. |
| **value** | `number` | `0` | The value of the progress indicator for the determinate variant.<br>Value between 0 and 100. |
| **variant** | ``"determinate" \| "indeterminate"`` | `'indeterminate'` | The variant to use.<br>Use indeterminate when there is no progress value. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCircularProgress-root | `'span'` | The component that renders the root. |
| svg | .NovaCircularProgress-svg | `'svg'` | The component that renders the svg. |
| track | .NovaCircularProgress-track | `'circle'` | The component that renders the track. |
| progress | .NovaCircularProgress-progress | `'circle'` | The component that renders the progress. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCircularProgress-determinate | `determinate` | Styles applied to the root element if `variant="determinate"`. |
| .NovaCircularProgress-indeterminate | `indeterminate` | Styles applied to the root element if `variant="indeterminate"`. |
| .NovaCircularProgress-colorPrimary | `colorPrimary` | Styles applied to the root element if `color="primary"`. |
| .NovaCircularProgress-colorError | `colorError` | Styles applied to the root element if `color="error"`. |
| .NovaCircularProgress-colorInfo | `colorInfo` | Styles applied to the root element if `color="info"`. |
| .NovaCircularProgress-colorWarning | `colorWarning` | Styles applied to the root element if `color="warning"`. |
| .NovaCircularProgress-colorSuccess | `colorSuccess` | Styles applied to the root element if `color="success"`. |

<br><br>

# LinearProgress

API reference docs for the React LinearProgress component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import LinearProgress from '@hxnova/react-components/LinearProgress';
// or
import { LinearProgress } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **color** | ``"inherit" \| "primary" \| "error" \| "info" \| "success" \| "warning"`` | `'primary'` | The color of the component. |
| **component** | `ElementType` | - |  |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **thickness** | `number` | `4` | The thickness of the bar. |
| **value** | `number` | `variant === 'determinate' ? 0 : 25` | The value of the progress indicator for the determinate and buffer variants.<br>Value between 0 and 100. |
| **variant** | ``"determinate" \| "indeterminate"`` | `'indeterminate'` | The variant to use.<br>Use indeterminate or query when there is no progress value. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaLinearProgress-root | `'div'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaLinearProgress-colorPrimary | `colorPrimary` | Styles applied to the root element if `color="primary"`; |
| .NovaLinearProgress-determinate | `determinate` | Styles applied to the root element if `variant="determinate"`. |
| .NovaLinearProgress-indeterminate | `indeterminate` | Styles applied to the root element if `variant="indeterminate"`. |

