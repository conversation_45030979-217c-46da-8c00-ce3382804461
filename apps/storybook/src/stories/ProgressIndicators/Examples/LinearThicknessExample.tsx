import React from 'react';
import { LinearProgress } from '@hxnova/react-components/LinearProgress';

export default function LinearThicknessExample() {
  return (
    <div>
      <div style={{ marginBottom: '8px', fontWeight: 600 }}>Linear Progress with different thickness</div>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', width: '100%', maxWidth: '400px' }}>
        <LinearProgress thickness={2} />
        <LinearProgress thickness={4} />
        <LinearProgress thickness={6} />
        <LinearProgress thickness={8} />
      </div>

      <div style={{ marginTop: '24px', marginBottom: '8px', fontWeight: 600 }}>
        Determinate with different thickness
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', width: '100%', maxWidth: '400px' }}>
        <LinearProgress variant="determinate" value={75} thickness={2} />
        <LinearProgress variant="determinate" value={75} thickness={4} />
        <LinearProgress variant="determinate" value={75} thickness={6} />
        <LinearProgress variant="determinate" value={75} thickness={8} />
      </div>
    </div>
  );
}
