import React from 'react';
import { CircularProgress } from '@hxnova/react-components/CircularProgress';

export default function CircularColorExample() {
  return (
    <div>
      <div style={{ marginBottom: '8px', fontWeight: 600 }}>Circular Progress with different colors</div>
      <div style={{ display: 'flex', flexDirection: 'row', gap: '24px' }}>
        <CircularProgress color="primary" />
        <CircularProgress color="error" />
        <CircularProgress color="success" />
        <CircularProgress color="info" />
        <CircularProgress color="warning" />
      </div>

      <div style={{ marginTop: '24px', marginBottom: '8px', fontWeight: 600 }}>Determinate with different colors</div>
      <div style={{ display: 'flex', flexDirection: 'row', gap: '24px' }}>
        <CircularProgress variant="determinate" value={75} color="primary" />
        <CircularProgress variant="determinate" value={75} color="error" />
        <CircularProgress variant="determinate" value={75} color="success" />
        <CircularProgress variant="determinate" value={75} color="info" />
        <CircularProgress variant="determinate" value={75} color="warning" />
      </div>
    </div>
  );
}
