import React from 'react';
import { CircularProgress } from '@hxnova/react-components/CircularProgress';

export default function CircularThicknessExample() {
  return (
    <div>
      <div style={{ marginBottom: '8px', fontWeight: 600 }}>Circular Progress with different thickness</div>
      <div style={{ display: 'flex', flexDirection: 'row', gap: '24px' }}>
        <CircularProgress thickness={1} />
        <CircularProgress thickness={3} />
        <CircularProgress thickness={5} />
        <CircularProgress thickness={8} />
      </div>

      <div style={{ marginTop: '24px', marginBottom: '8px', fontWeight: 600 }}>
        Determinate with different thickness
      </div>
      <div style={{ display: 'flex', flexDirection: 'row', gap: '24px' }}>
        <CircularProgress variant="determinate" value={75} thickness={1} />
        <CircularProgress variant="determinate" value={75} thickness={3} />
        <CircularProgress variant="determinate" value={75} thickness={5} />
        <CircularProgress variant="determinate" value={75} thickness={8} />
      </div>
    </div>
  );
}
