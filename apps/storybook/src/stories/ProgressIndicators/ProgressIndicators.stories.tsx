import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import {
  CircularProgress as NovaCircularProgress,
  CircularProgressProps,
} from '@hxnova/react-components/CircularProgress';
import { LinearProgress as NovaLinearProgress, LinearProgressProps } from '@hxnova/react-components/LinearProgress';
import { Typography } from '@hxnova/react-components/Typography';
import { Box } from '@hxnova/react-components/Box';

export default {
  title: '@hxnova/react-components/Progress indicators',
  component: NovaCircularProgress,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?m=auto&node-id=1092-35544&t=2GOMCLV2TPpwgnNJ-1',
    },
  },
  tags: ['!autodocs'],
} as Meta<typeof NovaCircularProgress>;

const CircularTemplate: StoryFn<(props: CircularProgressProps) => JSX.Element> = (args) => {
  return (
    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
      <NovaCircularProgress {...args} />
      {args.variant === 'determinate' && (
        <Box
          style={{
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            position: 'absolute',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography variant="bodySmall" style={{ color: 'var(--palette-primary)' }}>{`${args.value}%`}</Typography>
        </Box>
      )}
    </Box>
  );
};

const LinearTemplate: StoryFn<(props: LinearProgressProps) => JSX.Element> = (args) => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Box sx={{ width: '600px', marginRight: '8px' }}>
        <NovaLinearProgress {...args} />
      </Box>
      <Box sx={{ minWidth: '35px' }}>
        {args.variant === 'determinate' && (
          <Typography variant="bodySmall" style={{ color: 'var(--palette-primary)' }}>
            {args.value}%
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export const CircularProgress = {
  render: CircularTemplate,
  args: {
    color: 'primary',
    variant: 'determinate',
    thickness: 4,
    size: 56,
    value: 30,
  },
  parameters: {
    controls: {
      include: ['color', 'variant', 'thickness', 'size', 'value'],
    },
  },
};

export const LinearProgress = {
  render: LinearTemplate,
  args: {
    color: 'primary',
    variant: 'indeterminate',
    thickness: 4,
    value: 20,
  },
  parameters: {
    controls: {
      include: ['color', 'variant', 'thickness', 'value'],
    },
  },
};
