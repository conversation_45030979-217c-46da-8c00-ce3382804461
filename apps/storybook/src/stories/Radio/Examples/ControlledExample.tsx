import React from 'react';
import { Radio } from '@hxnova/react-components/Radio';
import { RadioGroup } from '@hxnova/react-components/RadioGroup';

export default function ControlledRadioButtonsGroup() {
  const [value, setValue] = React.useState('female');

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.value);
  };

  return (
    <RadioGroup defaultValue="female" name="controlled-radio-buttons-group" value={value} onChange={handleChange}>
      <Radio value="female" label="Female" />
      <Radio value="male" label="Male" />
      <Radio value="other" label="Other" />
    </RadioGroup>
  );
}
