import { Radio } from '@hxnova/react-components/Radio';
import { RadioGroup } from '@hxnova/react-components/RadioGroup';

export default function HorizontalExample() {
  return (
    <RadioGroup defaultValue="female" name="radio-buttons-group" orientation="horizontal">
      <Radio value="female" label="Female" slotProps={{ input: { 'aria-describedby': 'female-helper-text' } }} />
      <Radio value="male" label="Male" />
      <Radio value="other" label="Other" />
    </RadioGroup>
  );
}
