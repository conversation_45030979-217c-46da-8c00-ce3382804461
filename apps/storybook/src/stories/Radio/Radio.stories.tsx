import { StoryFn, Meta } from '@storybook/react';
import { Radio as NovaRadio, RadioProps } from '@hxnova/react-components/Radio';
import { RadioGroup as NovaRadioGroup, RadioGroupProps } from '@hxnova/react-components/RadioGroup';
import { action } from '@storybook/addon-actions';
import { useState } from 'react';

export default {
  title: '@hxnova/react-components/Radio',
  component: NovaRadio,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1180-20306&p=f&t=qT1XcTZl2ZoZOwrw-0',
    },
  },
  tags: ['!autodocs'],
} as Meta<typeof NovaRadio>;

const Template: StoryFn<(props: RadioProps) => JSX.Element> = (args) => {
  const [checked, setChecked] = useState(false);
  return <NovaRadio {...args} checked={checked} onChange={(e) => setChecked((c) => !c)} />;
};

const RadioGroupTemplate: StoryFn<(props: RadioGroupProps) => JSX.Element> = (args) => {
  const [value, setValue] = useState('1');
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <NovaRadioGroup
        {...args}
        value={value}
        onChange={(e) => {
          setValue(e.target.value);
          action('Selected:')(e.target.value);
        }}
      >
        <NovaRadio name="age" value="1" label="Option 1" />
        <NovaRadio name="age" value="2" label="Option 2" />
      </NovaRadioGroup>
    </div>
  );
};

export const Radio = {
  render: Template,
  args: {
    size: 'medium',
    disabled: false,
    label: 'Label',
  },
  argTypes: {
    size: {
      control: { type: 'radio' },
      options: ['medium', 'small', 'large'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
    checked: {
      control: { type: 'boolean' },
    },
  },
  parameters: {
    controls: {
      include: ['size', 'disabled'],
    },
  },
};

export const RadioGroup = {
  render: RadioGroupTemplate,
  args: {
    size: 'medium',
    disabled: false,
    orientation: 'vertical',
  },
  argTypes: {
    size: {
      control: { type: 'radio' },
      options: ['medium', 'small', 'large'],
    },
    orientation: {
      control: { type: 'radio' },
      options: ['horizontal', 'vertical'],
    },
  },
  parameters: {
    controls: {
      include: ['size', 'orientation'],
    },
  },
};
