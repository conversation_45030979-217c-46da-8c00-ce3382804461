import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import * as RadioStories from './Radio.stories';
import { Radio } from '@hxnova/react-components/Radio';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import GroupExample from './Examples/GroupExample';
import GroupExampleSource from './Examples/GroupExample.tsx?raw';
import ControlledExample from './Examples/ControlledExample';
import ControlledExampleSource from './Examples/ControlledExample.tsx?raw';
import HorizontalExample from './Examples/HorizontalExample';
import HorizontalExampleSource from './Examples/HorizontalExample.tsx?raw';

<Meta title="@hxnova/react-components/Radio/Examples" />

## Basic Usage

The Radio component's state is controlled through several boolean props: `defaultChecked` for initial checked state, `disabled` for preventing interaction, and `readOnly` for display-only mode.

<div className="doc-story sb-story sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Radio Sizes

The `size` prop accepts "small", "medium" (default), or "large" to control the Radio button's dimensions.

<div className="doc-story sb-story sb-unstyled">
  <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Radio Group

The `RadioGroup` component manages selection state through the `defaultValue` prop, while each Radio's `value` prop determines its selection value.

<div className="doc-story sb-story sb-unstyled">
  <GroupExample />
</div>
<CodeExpand code={GroupExampleSource} showBorderTop style={{marginTop: 16}}/>

## Horizontal Layout

The `orientation` prop on RadioGroup accepts "vertical" (default) or "horizontal" to control the layout direction of radio buttons.

<div className="doc-story sb-story sb-unstyled">
  <HorizontalExample />
</div>
<CodeExpand code={HorizontalExampleSource} showBorderTop style={{marginTop: 16}}/>

## Controlled

The `value` and `onChange` props enable controlled behavior. The `value` prop sets the current selection, while `onChange` handles selection updates.

<div className="doc-story sb-story sb-unstyled">
  <ControlledExample />
</div>
<CodeExpand code={ControlledExampleSource} showBorderTop style={{marginTop: 16}}/>

