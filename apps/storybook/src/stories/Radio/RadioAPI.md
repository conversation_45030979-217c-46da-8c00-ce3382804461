# API Documentation

- [Radio](#radio)
- [RadioGroup](#radiogroup)

# Radio

API reference docs for the React Radio component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Radio from '@hxnova/react-components/Radio';
// or
import { Radio } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **checked** | ``false \| true`` | - | If `true`, the component is checked. |
| **checkedIcon** | `ReactNode` | - | The icon to display when the component is checked. |
| **className** | `string` | - | Class name applied to the root element. |
| **component** | `ElementType` | - | The component used for the Root slot.<br>Either a string to use a HTML element or a component. |
| **defaultChecked** | ``false \| true`` | - | The default checked state. Use when the component is not controlled. |
| **disabled** | ``false \| true`` | - | If `true`, the component is disabled. |
| **disableIcon** | ``false \| true`` | `false` | If `true`, the checked icon is removed and the selected variant is applied on the `action` element instead. |
| **id** | `string` | - | The id of the `input` element. |
| **label** | `ReactNode` | - | The label element at the end the radio. |
| **name** | `string` | - | The `name` attribute of the input. |
| **onBlur** | ``FocusEventHandler<Element>`` | - |  |
| **onChange** | ``ChangeEventHandler<HTMLInputElement>`` | - | Callback fired when the state is changed.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (string).<br>You can pull out the new checked state by accessing `event.target.checked` (boolean). |
| **onFocus** | ``FocusEventHandler<Element>`` | - |  |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **overlay** | ``false \| true`` | `false` | If `true`, the root element's position is set to initial which allows the action area to fill the nearest positioned parent.<br>This prop is useful for composing Radio with ListItem component. |
| **readOnly** | ``false \| true`` | - | If `true`, the component is read only. |
| **required** | ``false \| true`` | - | If `true`, the `input` element is required. |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | The size of the component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **uncheckedIcon** | `ReactNode` | - | The icon to display when the component is not checked. |
| **value** | `unknown` | - | The value of the component. The DOM API casts this to a string. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaRadio-root | `'span'` | The component that renders the root. |
| container | .NovaRadio-container | `'span'` | The component that renders the container. |
| radio | .NovaRadio-radio | `'span'` | The component that renders the radio. |
| icon | .NovaRadio-icon | `'span'` | The component that renders the icon. |
| action | .NovaRadio-action | `'span'` | The component that renders the action. |
| input | .NovaRadio-input | `'input'` | The component that renders the input. |
| label | .NovaRadio-label | `'label'` | The component that renders the label. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaRadio-checked | `checked` | State class applied to the root, action slots if `checked`. |
| .NovaRadio-disabled | `disabled` | State class applied to the root, action slots if `disabled`. |
| .NovaRadio-focusVisible | `focusVisible` | Class name applied to the root element if the switch has visible focus |
| .NovaRadio-sizeSmall | `sizeSmall` | Styles applied to the root element if `size="small"`. |
| .NovaRadio-sizeMedium | `sizeMedium` | Styles applied to the root element if `size="medium"`. |
| .NovaRadio-sizeLarge | `sizeLarge` | Styles applied to the root element if `size="large"`. |

<br><br>

# RadioGroup

API reference docs for the React RadioGroup component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import RadioGroup from '@hxnova/react-components/RadioGroup';
// or
import { RadioGroup } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **className** | `string` | - | Class name applied to the root element. |
| **component** | `ElementType` | - | The component used for the Root slot.<br>Either a string to use a HTML element or a component. |
| **defaultValue** | `any` | - | The default value. Use when the component is not controlled. |
| **disableIcon** | ``false \| true`` | `false` | The radio's `disabledIcon` prop. If specified, the value is passed down to every radios under this element. |
| **name** | `string` | - | The name used to reference the value of the control.<br>If you don't provide this prop, it falls back to a randomly generated name. |
| **onChange** | ``(event: ChangeEvent<HTMLInputElement>) => void`` | - | Callback fired when a radio button is selected.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (string). |
| **orientation** | ``"horizontal" \| "vertical"`` | `'vertical'` | The component orientation. |
| **overlay** | ``false \| true`` | `false` | The radio's `overlay` prop. If specified, the value is passed down to every radios under this element. |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | The size of the component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | `any` | - | Value of the selected radio button. The DOM API casts this to a string. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaRadioGroup-root | `'div'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaRadioGroup-sizeSmall | `sizeSmall` | Styles applied to the root element if `size="small"`. |
| .NovaRadioGroup-sizeMedium | `sizeMedium` | Styles applied to the root element if `size="medium"`. |
| .NovaRadioGroup-sizeLarge | `sizeLarge` | Styles applied to the root element if `size="large"`. |
| .NovaRadioGroup-horizontal | `horizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaRadioGroup-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |

