import type { Meta, StoryFn } from '@storybook/react';
import {
  DataGrid as NovaDataGrid,
  DataGridProps,
  ColumnType,
  RowEventParas,
  CellEventParas,
  RowId,
} from '@hxnova/react-components/DataGrid';
import { action } from '@storybook/addon-actions';
import {
  DataType,
  expandedRowPanelRender,
  getExpandedRowHeight,
  isRowExpandable,
  mockData,
  renderCellGenerator,
  renderStatusEditCell,
  sortFnGenerator,
} from './mockData';
import { CellType } from '@hxnova/react-components/dist/DataGrid';

const meta = {
  title: '@hxnova/react-components/DataGrid',
  component: NovaDataGrid,
  parameters: {
    layout: 'centered',
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof NovaDataGrid>;

export default meta;

type DataGridTemplateProps = DataGridProps & {
  visibleColumns: string[];
  showRowExpansion: boolean;
};

const expandProps = {
  isRowExpandable,
  expandedRowPanelRender,
  getExpandedRowHeight,
};

const DataGridTemplate: StoryFn<DataGridTemplateProps> = (props) => {
  const { visibleColumns, density, data, showRowExpansion } = props;
  const columns = props.columns.map((i) => ({
    ...i,
    cell: renderCellGenerator(i, density || 'standard'),
    sortFn: sortFnGenerator(i),
    visible: visibleColumns.includes(i.field),
    renderEditCell: i.field === 'status' ? renderStatusEditCell : undefined,
  })) as ColumnType<DataType>[];
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        maxHeight: '600px',
        maxWidth: '1000px',
        background: 'var(--palette-surfaceContainer)',
      }}
    >
      <NovaDataGrid {...props} columns={columns} data={data} {...(showRowExpansion ? expandProps : {})} />
    </div>
  );
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const UniversalStory = {
  render: DataGridTemplate,
  args: {
    density: 'standard',
    rowSelectionMode: undefined,
    disableRowSelectionOnClick: false,
    cellSelection: false,
    showRowExpansion: false,
    visibleColumns: ['id', 'firstName', 'lastName', 'fullName', 'status'],
    editMode: 'cell',
    columns: [
      { field: 'id', header: 'ID', width: 100 },
      { field: 'avatar', header: 'User', width: 300 },
      { field: 'firstName', header: 'First Name', width: 200, editable: true },
      { field: 'lastName', header: 'Last Name', width: 200, editable: true },
      {
        field: 'fullName',
        header: 'Full Name',
        width: 250,
      },
      {
        field: 'status',
        header: 'Status',
        width: 200,
        editable: true,
      },
      {
        field: 'actions',
        minWidth: 100,
        flex: 1,
        align: 'end',
        sortable: false,
      },
    ] as ColumnType[],
    data: mockData,
    onPageChange: (page: number) => {
      action('onPageChange')(page);
    },
    onRowsPerPageChange: (rowsPerPage: number) => {
      action('onRowsPerPageChange')(rowsPerPage);
    },
    onSelectedRowsChange: (rowIds: Array<RowId>) => {
      action('onSelectedRowsChange')(rowIds);
    },
    onSortedColumnsChange: (sorts: unknown) => {
      action('onSortedColumnsChange')(sorts);
    },
    onSelectedCellsChange: (cells: Array<CellType>) => {
      action('onSelectedCellsChange')(cells);
    },
    onExpandedRowsChange: (rowIds: Array<RowId>) => {
      action('onExpandedRowsChange')(rowIds);
    },
    rowEvents: {
      onClick: (paras: RowEventParas<DataType>, event: React.MouseEvent<HTMLElement>) => {
        action('Row onClick')(paras, event);
      },
    },
    cellEvents: {
      onClick: (paras: CellEventParas<DataType>, event: React.MouseEvent<HTMLElement>) => {
        action('Cell onClick')(paras, event);
      },
    },
  },
  argTypes: {
    density: {
      control: { type: 'radio' },
      options: ['compact', 'standard', 'comfortable'],
    },
    rowSelectionMode: {
      control: { type: 'radio' },
      options: ['checkboxSelection', 'radioSelection', undefined],
    },
    visibleColumns: {
      name: 'Visible columns',
      control: { type: 'multi-select' },
      options: ['id', 'avatar', 'firstName', 'lastName', 'fullName', 'status', 'actions'],
    },
    editMode: {
      control: { type: 'radio' },
      options: ['cell', 'row'],
    },
    cellSelection: { type: 'boolean' },
    showRowExpansion: { type: 'boolean' },
    disableRowSelectionOnClick: { type: 'boolean' },
  },
  parameters: {
    controls: {
      include: [
        'data',
        'columns',
        'rowSelectionMode',
        'disableRowSelectionOnClick',
        'cellSelection',
        'density',
        'Visible columns',
        'editMode',
        'showRowExpansion',
      ],
    },
  },
};
