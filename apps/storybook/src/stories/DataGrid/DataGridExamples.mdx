import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import BasicDataGrid from './Examples/BasicDataGrid';
import BasicDataGridSource from './Examples/BasicDataGrid.tsx?raw';
import DataGridWithFlexCell from './Examples/DataGridWithFlexCell';
import DataGridWithFlexCellSource from './Examples/DataGridWithFlexCell.tsx?raw';
import DataGridWithCheckboxSelection from './Examples/DataGridWithCheckboxSelection';
import DataGridWithCheckboxSelectionSource from './Examples/DataGridWithCheckboxSelection.tsx?raw';
import DataGridWithRadioSelection from './Examples/DataGridWithRadioSelection';
import DataGridWithRadioSelectionSource from './Examples/DataGridWithRadioSelection.tsx?raw';
import DataGridWithCellSelection from './Examples/DataGridWithCellSelection';
import DataGridWithCellSelectionSource from './Examples/DataGridWithCellSelection.tsx?raw';
import DataGridWithCustomCell from './Examples/DataGridWithCustomCell';
import DataGridWithCustomCellSource from './Examples/DataGridWithCustomCell.tsx?raw';
import DataGridWithSortConfig from './Examples/DataGridWithSortConfig';
import DataGridWithSortConfigSource from './Examples/DataGridWithSortConfig.tsx?raw';
import DataGridWithScrollbar from './Examples/DataGridWithScrollbar';
import DataGridWithScrollbarSource from './Examples/DataGridWithScrollbar.tsx?raw';
import DataGridWithPaginationConfig from './Examples/DataGridWithPaginationConfig';
import DataGridWithPaginationConfigSource from './Examples/DataGridWithPaginationConfig.tsx?raw';
import DataGridWithExpandRow from './Examples/DataGridWithExpandRow';
import DataGridWithExpandRowSource from './Examples/DataGridWithExpandRow.tsx?raw';
import DataGridWithFixedColumn from './Examples/DataGridWithFixedColumn';
import DataGridWithFixedColumnSource from './Examples/DataGridWithFixedColumn.tsx?raw';
import DataGridWithCellEditing from './Examples/DataGridWithCellEditing';
import DataGridWithCellEditingSource from './Examples/DataGridWithCellEditing.tsx?raw';
import DataGridWithRowEditing from './Examples/DataGridWithRowEditing';
import DataGridWithRowEditingSource from './Examples/DataGridWithRowEditing.tsx?raw';
import DataGridWithEventHandler from './Examples/DataGridWithEventHandler';
import DataGridWithEventHandlerSource from './Examples/DataGridWithEventHandler.tsx?raw';
import DataGridControlled from './Examples/DataGridControlled';
import DataGridControlledSource from './Examples/DataGridControlled.tsx?raw';
import DataGridWithServerSideData from './Examples/DataGridWithServerSideData';
import DataGridWithServerSideDataSource from './Examples/DataGridWithServerSideData.tsx?raw';

<Meta title="@hxnova/react-components/DataGrid/Examples" />
 
## Basic DataGrid

This example demonstrates a straightforward implementation of the `DataGrid` component, showcasing a simple table structure with `fixed column width`.

<div className="sb-unstyled">
  <BasicDataGrid />
</div>
<CodeExpand code={ BasicDataGridSource }  style={{marginTop: 16}}/>

## Responsive flex columns

In this example, the DataGrid is enhanced with flexible column widths, allowing the columns to adjust based on the available space. Each column is defined with a `flex` property and a `minWidth`, ensuring that the layout remains responsive. 
* The `flex` property must be a positive number reflecting the growth factor of the column, determining how much the column can grow relative to other columns. Otherwise, it is treated as `undefined`.

<div className="sb-unstyled" >
  <DataGridWithFlexCell />
</div>
<CodeExpand code={ DataGridWithFlexCellSource }  style={{marginTop: 16}}/>

## Row Selection

This example demonstrates the row selection feature.

* **Enable Selection**: Set `rowSelectionMode="checkboxSelection"` or `rowSelectionMode="radioSelection"` to allow users to select rows using checkbox or radio.
* **Initial Selection State**: Manage the initial selection with the `initialState.selectedRows` property.
* **Callback For Selection State**: Use the `onSelectedRowsChange` callback function to receive `selectedRows` state changes
* **Enable/Disable Special Rows Selection**: Enable/disable the special rows selection via `isRowSelectable` function.
* **Disable Row Click Selection**: Set `disableRowSelectionOnClick` to disable row click selection behavior.

### Row Checkbox Selection
This example demonstrates the checkbox selection feature, enhancing interactivity by allowing users to select multiple entries from the dataset, which is particularly useful for bulk actions.

<div className="sb-unstyled" >
  <DataGridWithCheckboxSelection />
</div>
<CodeExpand code={ DataGridWithCheckboxSelectionSource }  style={{marginTop: 16}}/>

### Row Radio Selection
This example illustrates the radio selection feature, enabling users to select a single entry from the dataset, which is particularly useful for scenarios requiring exclusive choices.

<div className="sb-unstyled" >
  <DataGridWithRadioSelection />
</div>
<CodeExpand code={ DataGridWithRadioSelectionSource }  style={{marginTop: 16}}/>

## Cell Selection
This example illustrates the cell selection feature, enabling users to select a single cell from the dataset.

* **Enable Cell Selection**: Set `cellSelection=true` to allow users to select a single cell via clicking cell.
* **Initial Cell Selection State**: Manage the initial cell selection using the `initialState.selectedCells` property.
* **Callback For Selection State**: Use the `onSelectedCellsChange` callback function to receive `selectedCells` state changes
<div className="sb-unstyled" >
  <DataGridWithCellSelection />
</div>
<CodeExpand code={ DataGridWithCellSelectionSource }  style={{marginTop: 16}}/>


## Custom cell

This example demonstrates customized cell rendering in the `DataGrid`. A new `status` property is added to the `DataType`, and the `avatar` column displays user avatars alongside their names. The status column features custom rendering to show a colored tag based on the user's status, enhancing the visual appeal and informativeness of the data.

* **Custom Cell Function**: By default, cell values correspond to the row's data. Use the `cell` property to customize the cell content as needed.

<div className="sb-unstyled" >
  <DataGridWithCustomCell />
</div>
<CodeExpand code={ DataGridWithCustomCellSource }  style={{marginTop: 16}}/>

## Sort
This example demonstrates the sorting functionality of the DataGrid, enabling users to arrange data by specific columns. The columns configuration features custom sorting logic for the avatar and status columns.

* **Custom Sort Functions**: You can customize the sorting logic using the `sortFn` property. The `avatar` column sorts users alphabetically by their full names using locale comparison, while the `status` column sorts according to a predefined order: `active, pending, and deactivated`. 
* **Initial Sorting State**: The grid is initialized to sort by the `Status` column in ascending order, managed by the `initialState.sortedColumns` property.
* **Disabled Column Sort**: You can pass `sortable: false` to single column to disable the sort function, like the `First Name` and `Last Name` columns.

<div className="sb-unstyled">
  <DataGridWithSortConfig />
</div>
<CodeExpand code={ DataGridWithSortConfigSource }  style={{marginTop: 16}}/>

## Scrollbar
The `DataGrid` can be placed inside a flex container with `flex-direction: column`. If `maxHeight` is not set, the grid will expand to display all rows. This example demonstrates a DataGrid with fixed dimensions and scrolling functionality for overflow content.

The layout is constrained to a **maximum height of 400px** and a **maximum width of 600px**, ensuring the grid remains visually contained while allowing access to all data via a scrollbar. The grid showcases a diverse dataset with various statuses, combining fixed dimensions with responsive content handling, making it ideal for scenarios where space is limited yet data visibility is essential.

<div className="sb-unstyled" >
  <DataGridWithScrollbar />
</div>
<CodeExpand code={ DataGridWithScrollbarSource }  style={{marginTop: 16}}/>

## Pagination
This example demonstrates the pagination feature of the `DataGrid`, enabling users to navigate large datasets efficiently. 
* **Pagination Configuration**: Pagination can be configured using the `paginationConfig` prop, which allows customization of labels, pagination info rendering, and rows per page.
* **Pagination Initial State**: The initial pagination state is set via `initialState.pagination`, specifying the number of rows per page (25 as below) and the starting page (zero-based index). 

<div className="sb-unstyled" >
  <DataGridWithPaginationConfig />
</div>
<CodeExpand code={ DataGridWithPaginationConfigSource }  style={{marginTop: 16}}/>

## Row Expansion
This example showcases the row expansion feature of the `DataGrid`, allowing users to display additional information within an expanded row. This functionality enhances data visibility and user interaction by providing context or details without cluttering the main grid.
* **Enable Row Expansion**: Activate the row expansion feature using the `expandedRowPanelRender` prop, which defines the content displayed in the expanded section for each row.
* **Control Expandability**: Use the `isRowExpandable` prop to determine which rows can be expanded. This allows for conditional expansion, such as disabling expansion for specific rows based on custom criteria.
* **Custom Row Height**: By default, the expanded row height is set to `200px`. You can customize the height of each expanded row using the `getExpandedRowHeight` prop, providing flexibility based on the content displayed.
* **Initial Row Expanded State**: Manage the initial row expanded state using the `initialState.expandedRows` property.

<div className="sb-unstyled" >
  <DataGridWithExpandRow />
</div>
<CodeExpand code={ DataGridWithExpandRowSource }  style={{marginTop: 16}}/>

## Fixed(Pinned) columns

This example showcases the fixed column feature of the `DataGrid` component, allowing certain columns to remain in view while scrolling through the data. This functionality enhances usability by ensuring key information is always accessible.
* The `id` column is fixed to the left, providing a consistent reference point for each row.
* The `actions` column is fixed to the right, containing an IconButton for user interactions, such as displaying more options related to each row.

<div className="sb-unstyled" >
  <DataGridWithFixedColumn />
</div>
<CodeExpand code={ DataGridWithFixedColumnSource }  style={{marginTop: 16}}/>


## Editing

The `DataGrid` component supports both cell-level and row-level editing, enabling users to update data effectively.

### Cell Editing
In this example, the `editable` property enables editing for specific columns, such as `firstName` and `lastName`. User can easily modify the contents of these cells by double-clicking on them. After making edits, they can press the `Enter` key to submit their changes. If they wish to discard their modifications, they can simply press the `Escape` key to revert back to the original content. (To keep better performance and data security, only one cell can be edited at a time for this mode).
* **Cell Editing State Control**: The editing state of cells can be managed through `editingCells` and `onEditingCellsChange`, providing additional control over the editing experience.
* **Lifecycle Events**: Event handlers such as `onCellEditStart` and `onCellEditStop` offer insights into the cell editing lifecycle, allowing for custom actions and user feedback during these events.
* **Data Update Via API**: Utilize `processRowUpdate` to handle data updates via api and utilize `onProcessRowUpdateError` to manage any potential errors during the update process.
* **Custom Editing Cell**: Currently only text editing is supported. If you need to customize the edit cell, please use `renderEditCell`, refer to the `status` column.

<div className="sb-unstyled">
  <DataGridWithCellEditing />
</div>
<CodeExpand code={ DataGridWithCellEditingSource } showBorderTop style={{marginTop: 16}}/>

### Row Editing
This example demonstrates row editing by setting the `editMode` prop to `"row"`, enabling users to edit an entire row simultaneously. Similar to cell editing, users can double-click on a row to initiate the editing process. (To keep better performance and data security, only one row can be edited at a time for this mode).
* **Row Editing State Control**: The editing state for rows can be controlled via `editingRows` and `onEditingRowsChange`, facilitating a seamless editing process.
* **Lifecycle Events**: Event handlers such as `onRowEditStart` and `onRowEditStop` offer insights into the row editing lifecycle, allowing for custom actions and user feedback during these events.
* **Data Update Via API**: Utilize `processRowUpdate` to handle data updates via api and utilize `onProcessRowUpdateError` to manage any potential errors during the update process.
* **Custom Editing Cell**: Currently only text editing is supported. If you need to customize the edit cell, please use `renderEditCell`, refer to the `status` column.

<div className="sb-unstyled">
  <DataGridWithRowEditing />
</div>
<CodeExpand code={ DataGridWithRowEditingSource } showBorderTop style={{marginTop: 16}}/>

## DataGrid Cell/Row Events

This example demonstrates the event handling capabilities of the `DataGrid`, allowing users to interact with both cells and rows through various event handlers like `onClick`, `onDoubleClick` and etc.

<div className="sb-unstyled" >
  <DataGridWithEventHandler />
</div>
<CodeExpand code={ DataGridWithEventHandlerSource }  style={{marginTop: 16}}/>

## Controlled DataGrid

This example demonstrates a controlled DataGrid component, which allows for dynamic management of its state through its callback functions. The component maintains the following states:

* **Pagination State**: The current page and the number of rows per page are managed using `page` and `rowsPerPage`.
* **Sorting State**: Column sorting is controlled via the `sortedColumns` state.
* **Selection State**: Selected rows are tracked using the `selectedRows` state.

<div className="sb-unstyled" >
  <DataGridControlled />
</div>
<CodeExpand code={ DataGridControlledSource }  style={{marginTop: 16}}/>

## Server side data

This example illustrates a `DataGrid` configured for server-side pagination and sorting, enabling efficient handling of large datasets. 
* **Server Side Pagination**: You can enable server side pagination by setting `paginationMode = "server"` and provide the `onPageChange`, `onRowsPerPageChange` event handlers to fetch the data from the server based on the updated variables.
* **Server Side Sorting**:You can enable server side pagination by setting `sortMode = "server"` and provide the `onSortedColumnsChange` event handlers to fetch the data from the server based on the updated variables.

<div className="sb-unstyled" >
  <DataGridWithServerSideData />
</div>
<CodeExpand code={ DataGridWithServerSideDataSource }  style={{marginTop: 16}}/>