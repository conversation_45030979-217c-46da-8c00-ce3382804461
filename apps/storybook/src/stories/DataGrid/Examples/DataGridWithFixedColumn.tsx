import Icon from '@hxnova/icons/Icon';
import { ColumnType, DataGrid } from '@hxnova/react-components/DataGrid';
import { IconButton } from '@hxnova/react-components/IconButton';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', minWidth: 100, fixed: 'start' },
  { field: 'firstName', header: 'First Name', flex: 1, minWidth: 300 },
  { field: 'lastName', header: 'Last Name', flex: 1, minWidth: 300 },
  {
    field: 'fullName',
    header: 'Full Name',
    flex: 2,
    minWidth: 600,
    cell: (row) => `${row.firstName} ${row.lastName}`,
    sortable: false,
  },
  {
    field: 'actions',
    minWidth: 100,
    align: 'end',
    fixed: 'end',
    cell: (row: DataType) => (
      <IconButton
        variant="neutral"
        onClick={() => {
          console.log(row.id);
        }}
      >
        <Icon family="material" name="more_vert" size={24} />
      </IconButton>
    ),
    sortable: false,
  },
];

const data: DataType[] = [
  { id: 1, firstName: '<PERSON>', lastName: 'Smith' },
  { id: 2, firstName: 'Isla', lastName: 'Fletcher' },
  { id: 3, firstName: 'Evie', lastName: 'Easton' },
  { id: 4, firstName: 'Liam', lastName: 'Johnson' },
  { id: 5, firstName: 'Ava', lastName: 'Brown' },
  { id: 6, firstName: 'Noah', lastName: 'Williams' },
];

export default function DataGridDemo() {
  return (
    <div style={{ width: '100%' }}>
      <DataGrid columns={columns} data={data} />
    </div>
  );
}
