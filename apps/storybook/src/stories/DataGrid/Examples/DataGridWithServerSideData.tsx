import React, { useEffect, useState } from 'react';
import { DataGrid, ColumnType, SortItem } from '@hxnova/react-components/DataGrid';
import { Tag } from '@hxnova/react-components/Tag';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
  status: 'active' | 'pending' | 'deactivated';
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  {
    field: 'status',
    header: 'Status',
    width: 200,
    cell: (paras: DataType) => (
      <Tag
        intensity="subtle"
        variant={paras.status === 'active' ? 'success' : paras.status === 'pending' ? 'warning' : 'error'}
        label={`${paras.status.charAt(0).toUpperCase()}${paras.status.substring(1, paras.status.length)}`}
      />
    ),
  },
];

const fetchData = async (
  page: number,
  rowsPerPage: number,
  sortedColumns: SortItem[],
): Promise<{ data: DataType[]; total: number }> => {
  // The possible API call
  // const response = await fetch(`/api/data?page=${page}&limit=${rowsPerPage}&sort=${JSON.stringify(sortedColumns)}`);
  // const data = await response.json();
  // return data;

  // Some mock data here:
  const mockData = Array.from({ length: 100 }, (_, index) => ({
    id: index + 1,
    firstName: `First${index + 1}`,
    lastName: `Last${index + 1}`,
    status: ['active', 'pending', 'deactivated'][Math.floor(Math.random() * 3)],
  })) as DataType[];

  // Calculate the start and end index for the current page
  const start = page * rowsPerPage;
  const end = start + rowsPerPage;

  // Sort the data if sortedColumns is provided
  const sortedData = [...mockData];
  if (sortedColumns.length > 0) {
    sortedData.sort((a, b) => {
      for (const { field, sort } of sortedColumns) {
        const dataField = field as keyof DataType;
        const comparison = a[dataField] > b[dataField] ? 1 : -1;
        return sort === 'asc' ? comparison : -comparison;
      }
      return 0;
    });
  }

  // Slice the data for the current page
  const paginatedData = sortedData.slice(start, end);

  // Simulate an API response
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: paginatedData,
        total: mockData.length,
      });
    }, 100); // Simulate network latency
  });
};

export default function DataGridDemo() {
  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [sortedColumns, setSortedColumns] = useState<SortItem[]>([]);
  const [selectedRows, setSelectedRows] = useState<Array<string | number>>([]);
  const [data, setData] = useState<DataType[]>([]);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const loadData = async () => {
      const result = await fetchData(page, rowsPerPage, sortedColumns);
      setData(result.data);
      setTotal(result.total); // Assuming the API returns total row count
    };
    loadData();
  }, [page, rowsPerPage, sortedColumns]);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', maxHeight: '400px' }}>
      <DataGrid
        columns={columns}
        data={data}
        rowSelectionMode="checkboxSelection"
        total={total}
        paginationMode="server"
        sortMode="server"
        page={page}
        onPageChange={(newPage) => {
          setPage(newPage);
        }}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={(newRowsPerPage) => {
          setRowsPerPage(newRowsPerPage);
          setPage(0); // Reset to first page on rows per page change
        }}
        sortedColumns={sortedColumns}
        onSortedColumnsChange={(newSortedColumns) => {
          setSortedColumns(newSortedColumns);
          setPage(0); // Reset to first page on sort change
        }}
        selectedRows={selectedRows}
        onSelectedRowsChange={(newSelectedRows) => {
          setSelectedRows(newSelectedRows);
        }}
      />
    </div>
  );
}
