import { ColumnType, DataGrid } from '@hxnova/react-components/DataGrid';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  {
    field: 'fullName',
    header: 'Full Name',
    width: 300,
    cell: (row) => `${row.firstName} ${row.lastName}`,
    sortable: false,
  },
];

const data: DataType[] = [
  { id: 1, firstName: '<PERSON>', lastName: '<PERSON>' },
  { id: 2, firstName: '<PERSON><PERSON>', lastName: '<PERSON>' },
  { id: 3, firstName: '<PERSON>vie', lastName: '<PERSON>' },
  { id: 4, firstName: '<PERSON>', lastName: '<PERSON>' },
  { id: 5, firstName: 'Ava', lastName: '<PERSON>' },
  { id: 6, firstName: '<PERSON>', lastName: '<PERSON>' },
];

export default function DataGridDemo() {
  return <DataGrid columns={columns} data={data} />;
}
