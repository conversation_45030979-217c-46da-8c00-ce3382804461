import { useState } from 'react';
import type { Meta } from '@storybook/react';
import dayjs, { Dayjs } from 'dayjs/esm';
import { DateRangePickerProps, DockedDatePicker, DockedDatePickerProps } from '@hxnova/react-components/DatePickers';
import { ModalDatePicker, ModalDatePickerProps } from '@hxnova/react-components/DatePickers';
import { DateField } from '@hxnova/react-components/DatePickers';
import { DockedDateRangePicker } from '@hxnova/react-components/DatePickers';
import { ModalDateRangePicker } from '@hxnova/react-components/DatePickers';

const meta = {
  title: '@hxnova/react-components/DatePicker',
  component: DockedDatePicker,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/tZgUauJZLgk0pX7rHCfxKt/NOVA-Components?node-id=4594-15410&t=YgCNlN2eT16cMyQ9-0',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof DockedDatePicker>;

export default meta;

// Template for basic DockedDatePicker
const DockedDatePickerTemplate = (args: DockedDatePickerProps) => {
  const [value, setValue] = useState<Dayjs | null>(null);

  return (
    <DockedDatePicker
      {...args}
      value={value}
      onChange={(newValue) => {
        setValue(newValue);
        console.log('Date changed:', newValue);
      }}
    />
  );
};

// Template for basic ModalDatePicker
const ModalDatePickerTemplate = (args: ModalDatePickerProps) => {
  const [value, setValue] = useState<Dayjs | null>(null);

  return (
    <ModalDatePicker
      {...args}
      value={value}
      onChange={(newValue) => {
        setValue(newValue);
        console.log('Date changed:', newValue);
      }}
    />
  );
};

// Basic stories
export const DockedDatePickerBasic = {
  render: DockedDatePickerTemplate,
  args: {
    label: 'Select Date',
    format: 'DD/MM/YYYY',
    disableFuture: true,
    disablePast: false,
    readOnly: false,
    disabled: false,
    helperText: '',
  },
  argTypes: {
    disableFuture: {
      control: { type: 'boolean' },
      options: [true, false],
    },
    disablePast: {
      control: { type: 'boolean' },
      options: [true, false],
    },
  },
  parameters: {
    controls: {
      include: ['label', 'format', 'disableFuture', 'disablePast', 'readOnly', 'disabled', 'helperText'],
    },
  },
};

export const ModalDatePickerBasic = {
  render: ModalDatePickerTemplate,
  args: {
    ...DockedDatePickerBasic.args,
  },
  parameters: {
    ...DockedDatePickerBasic.parameters,
  },
  argTypes: {
    ...DockedDatePickerBasic.argTypes,
  },
};

// Template for DateField with empty initial value
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const DateFieldTemplate = (args: any) => {
  const [value, setValue] = useState<Dayjs | null>(null);

  return (
    <DateField
      {...args}
      value={value}
      onChange={(newValue: Dayjs) => {
        setValue(newValue);
        console.log('Date changed:', newValue);
      }}
    />
  );
};

// Template for DateField with preset value
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const DateFieldWithValueTemplate = (args: any) => {
  const [value, setValue] = useState<Dayjs | null>(dayjs());

  return (
    <DateField
      {...args}
      value={value}
      onChange={(newValue: Dayjs) => {
        setValue(newValue);
        console.log('Date changed:', newValue);
      }}
    />
  );
};

// Basic story with empty initial value to show placeholder
export const EmptyDateField = {
  render: DateFieldTemplate,
  args: {
    label: 'Date Field',
    format: 'MM/DD/YYYY',
    helperText: 'MM/DD/YYYY',
    clearable: false,
  },
  parameters: {
    controls: {
      include: ['label', 'format', 'helperText', 'clearable'],
    },
  },
};

// Basic story with value
export const FilledDateField = {
  render: DateFieldWithValueTemplate,
  args: {
    label: 'Date Field',
    format: 'MM/DD/YYYY',
    helperText: 'MM/DD/YYYY',
    clearable: false,
  },
  parameters: {
    controls: {
      include: ['label', 'format', 'helperText', 'clearable'],
    },
  },
};

// DateRangePicker examples
// Template for basic DateRangePicker
const DockedDateRangePickerTemplate = (args: DateRangePickerProps) => {
  const [value, setValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  return (
    <DockedDateRangePicker
      {...args}
      value={value}
      onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
        setValue(newValue);
        console.log('Date range changed:', newValue);
      }}
    />
  );
};

export const DockedDateRangePickerBasic = {
  render: DockedDateRangePickerTemplate,
  args: {
    startLabel: 'Check-in',
    endLabel: 'Check-out',
    format: 'DD/MM/YYYY',
    calendars: 2,
    disableFuture: false,
    disablePast: false,
    readOnly: false,
    disabled: false,
    label: 'Depart - return dates',
  },
  argTypes: {
    disableFuture: {
      control: { type: 'boolean' },
      options: [true, false],
    },
    disablePast: {
      control: { type: 'boolean' },
      options: [true, false],
    },
  },
  parameters: {
    controls: {
      include: [
        'startLabel',
        'endLabel',
        'format',
        'calendars',
        'disableFuture',
        'disablePast',
        'readOnly',
        'disabled',
        'label',
      ],
    },
  },
};

const ModalDateRangePickerTemplate = (args: DateRangePickerProps) => {
  const [value, setValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  return (
    <ModalDateRangePicker
      {...args}
      value={value}
      onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
        setValue(newValue);
        console.log('Date range changed:', newValue);
      }}
    />
  );
};

export const ModalDateRangePickerBasic = {
  render: ModalDateRangePickerTemplate,
  args: {
    ...DockedDateRangePickerBasic.args,
  },
  parameters: {
    ...DockedDateRangePickerBasic.parameters,
  },
  argTypes: {
    ...DockedDateRangePickerBasic.argTypes,
  },
};
