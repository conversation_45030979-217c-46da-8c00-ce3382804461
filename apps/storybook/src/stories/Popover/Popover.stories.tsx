import { StoryFn, Meta } from '@storybook/react';
import { Box } from '@hxnova/react-components/Box';
import { Button } from '@hxnova/react-components/Button';
import { Typography } from '@hxnova/react-components/Typography';
import { Popover, PopoverProps } from '@hxnova/react-components/Popover';

const meta = {
  title: '@hxnova/react-components/Popover',
  component: Popover,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Components?m=auto&node-id=10971-181',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof Popover>;

export default meta;

const Template: StoryFn<(props: PopoverProps) => JSX.Element> = (args) => {
  const { title, description } = args;
  return (
    <Popover
      {...args}
      content={
        <Box
          style={{
            display: 'flex',
            width: '280px',
            height: '140px',
            flexDirection: 'column',
            justifyContent: 'space-between',
            gap: 'var(--spaceBetween-vertical-lg, 24px)',
          }}
        >
          <Typography variant="titleSmall" style={{ color: 'var(--palette-onSurfaceVariant)' }}>
            {title}
          </Typography>
          <Typography variant="bodySmall" style={{ color: 'var(--palette-onSurfaceVariant)' }}>
            {description}
          </Typography>
          <Box style={{ display: 'flex', justifyContent: 'flex-end', gap: 'var(--spaceBetween-horizontal-lg, 16px)' }}>
            <Button variant="text">Button</Button>
            <Button variant="filled">Button</Button>
          </Box>
        </Box>
      }
    >
      <Button variant="filled">Open Popover</Button>
    </Popover>
  );
};

export const Basic = {
  render: Template,
  args: {
    title: 'Title',
    description: 'Supporting line text',
    open: true,
    placement: 'bottom',
    showArrow: true,
    showBackdrop: false,
    showClose: false,
  },
  argTypes: {
    placement: {
      control: { type: 'radio' },
      options: ['top', 'left', 'right', 'bottom'],
    },
    showArrow: {
      control: { type: 'boolean' },
    },
    showBackdrop: {
      control: { type: 'boolean' },
    },
    showClose: {
      control: { type: 'boolean' },
    },
  },
  parameters: {
    controls: {
      include: ['title', 'description', 'open', 'placement', 'showArrow', 'showBackdrop', 'showClose'],
    },
  },
};
