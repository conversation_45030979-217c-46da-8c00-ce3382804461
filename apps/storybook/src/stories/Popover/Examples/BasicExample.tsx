import { Popover } from '@hxnova/react-components/Popover';
import { Button } from '@hxnova/react-components/Button';

export default function BasicExample() {
  return (
    <div style={{ display: 'flex', gap: '24px', alignItems: 'center' }}>
      <Popover title="Basic Popover" description="This is a basic popover with title and description">
        <Button variant="outlined">Basic Popover</Button>
      </Popover>

      <Popover
        title="Popover with Arrow"
        description="This popover has an arrow pointing to the trigger element"
        showArrow
      >
        <Button variant="outlined">With Arrow</Button>
      </Popover>

      <Popover
        title="Popover with Arrow"
        description="This popover has an arrow pointing to the trigger element"
        showClose
      >
        <Button variant="outlined">With Close Button</Button>
      </Popover>
    </div>
  );
}
