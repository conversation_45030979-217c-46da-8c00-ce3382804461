import { Popover } from '@hxnova/react-components/Popover';
import { Button } from '@hxnova/react-components/Button';

export default function PlacementExample() {
  return (
    <div style={{ display: 'flex', gap: '24px', flexWrap: 'wrap' }}>
      <Popover title="Top Placement" showArrow description="This popover is placed on top" placement="top">
        <Button variant="outlined">Top</Button>
      </Popover>

      <Popover title="Bottom Placement" showArrow description="This popover is placed on the bottom" placement="bottom">
        <Button variant="outlined">Bottom</Button>
      </Popover>

      <Popover title="Left Placement" showArrow description="This popover is placed on the left" placement="left">
        <Button variant="outlined">Left</Button>
      </Popover>

      <Popover title="Right Placement" showArrow description="This popover is placed on the right" placement="right">
        <Button variant="outlined">Right</Button>
      </Popover>
    </div>
  );
}
