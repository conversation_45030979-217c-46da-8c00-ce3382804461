# API Documentation

- [Popover](#popover)

# Popover

API reference docs for the React Popover component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { Popover } from '@hxnova/react-components/Popover';
// or
import { Popover } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children*** | `ReactNode` | - | The trigger element that will open the popover when clicked. |
| **title** | `ReactNode` | - | The title to be displayed in the popover. |
| **description** | `ReactNode` | - | The description content to be displayed in the popover. |
| **content** | `ReactNode` | - | Custom content to be displayed in the popover. If provided, it will replace the default title and description layout. |
| **className** | `string` | - | Class name applied to the root element. |
| **defaultOpen** | `boolean` | `false` | If `true`, the popover will be open by default. |
| **open** | `boolean` | - | If provided, the popover will be controlled. The popover will be open if `true`. |
| **onOpenChange** | `(open: boolean, event?: Event) => void` | - | Callback fired when the popover open state changes. |
| **placement** | `'top' \| 'right' \| 'bottom' \| 'left'` | `'bottom'` | The placement of the popover relative to the trigger element. |
| **showArrow** | `boolean` | `true` | Whether to show the arrow pointing to the trigger element. |
| **showBackdrop** | `boolean` | `false` | Whether to show a backdrop behind the popover. |
| **showClose** | `boolean` | `false` | Whether to show a close button in the top-right corner of the popover. |
| **modal** | `boolean` | `false` | Whether the popover should be modal (trap focus). |
| **slotProps** | `object` | - | Props passed to the sub-components. |

## CSS

| Rule name | Global class | Description |
| --------- | ------------ | ----------- |
| root | .NovaPopover-root | Styles applied to the root element. |
| trigger | .NovaPopover-trigger | Styles applied to the trigger element. |
| portal | .NovaPopover-portal | Styles applied to the portal element. |
| positioner | .NovaPopover-positioner | Styles applied to the positioner element. |
| popup | .NovaPopover-popup | Styles applied to the popup element. |
| arrow | .NovaPopover-arrow | Styles applied to the arrow element. |
| backdrop | .NovaPopover-backdrop | Styles applied to the backdrop element. |
| title | .NovaPopover-title | Styles applied to the title element. |
| description | .NovaPopover-description | Styles applied to the description element. |
| close | .NovaPopover-close | Styles applied to the close button element. |
| open | .NovaPopover-open | Styles applied to the root element when the popover is open. |
| closed | .NovaPopover-closed | Styles applied to the root element when the popover is closed. |
