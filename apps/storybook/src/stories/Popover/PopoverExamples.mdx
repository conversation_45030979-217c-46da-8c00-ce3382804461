import { <PERSON>vas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import * as PopoverStories from './Popover.stories';
import { Popover } from '@hxnova/react-components/Popover';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';

import PlacementExample from './Examples/PlacementExample';
import PlacementExampleSource from './Examples/PlacementExample.tsx?raw';

import CustomContentExample from './Examples/CustomContentExample';
import CustomContentExampleSource from './Examples/CustomContentExample.tsx?raw';

import BackdropExample from './Examples/BackdropExample';
import BackdropExampleSource from './Examples/BackdropExample.tsx?raw';

<Meta title="@hxnova/react-components/Popover/Examples" />

## Basic Usage

The basic Popover component provides a popup that appears when users click on a trigger element. The `title` prop sets the popover title, and the `description` prop adds supporting text. The `showArrow` prop adds a visual pointer to the trigger element.

<div className="doc-story sb-story sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Popover Placement

The `placement` prop determines the popover's position relative to its trigger element. Valid values are "top", "bottom", "left", and "right". Click on the buttons to see the popovers in different positions.

<div className="doc-story sb-story sb-unstyled">
  <PlacementExample />
</div>
<CodeExpand code={PlacementExampleSource} showBorderTop style={{marginTop: 16}}/>

## Custom Content

You can provide custom content to the popover using the `content` prop. This allows you to create more complex popovers with custom layouts and interactive elements like buttons.

<div className="doc-story sb-story sb-unstyled">
  <CustomContentExample />
</div>
<CodeExpand code={CustomContentExampleSource} showBorderTop style={{marginTop: 16}}/>

## Backdrop

You can add a backdrop to the popover using the `showBackdrop` prop. This creates a semi-transparent overlay behind the popover that blocks interaction with the page.

<div className="doc-story sb-story sb-unstyled">
  <BackdropExample />
</div>
<CodeExpand code={BackdropExampleSource} showBorderTop style={{marginTop: 16}}/>
