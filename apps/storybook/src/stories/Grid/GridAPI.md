# API Documentation

- [Grid](#grid)

# Grid

API reference docs for the React Grid component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Grid from '@hxnova/react-components/Grid';
// or
import { Grid } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **columns** | ``number | (number)[] | { xs?: number | undefined; sm?: number | undefined; md?: number | undefined; lg?: number | undefined; xl?: number | undefined; }`` | `12` | The number of columns. |
| **columnSpacing** | ``string | number | (GridSpacing)[] | { xs?: GridSpacing | undefined; sm?: GridSpacing | undefined; md?: GridSpacing | undefined; lg?: GridSpacing | undefined; xl?: GridSpacing | ... 1 more ... | undefined; }`` | - | Defines the horizontal space between the type `item` components.<br>It overrides the value of the `spacing` prop. |
| **container** | ``false | true`` | `false` | If `true`, the component will have the flex *container* behavior.<br>You should be wrapping *items* with a *container*. |
| **direction** | ``"row" | "row-reverse" | "column" | "column-reverse" | (GridDirection)[] | { xs?: GridDirection | undefined; sm?: GridDirection | undefined; md?: GridDirection | undefined; lg?: GridDirection | undefined; xl?: GridDirection | ... 1 more ... | undefined; }`` | `'row'` | Defines the `flex-direction` style property.<br>It is applied for all screen sizes. |
| **offset** | ``number | (number)[] | { xs?: number | undefined; sm?: number | undefined; md?: number | undefined; lg?: number | undefined; xl?: number | undefined; }`` | - | Defines the offset of the grid. |
| **rowSpacing** | ``string | number | (GridSpacing)[] | { xs?: GridSpacing | undefined; sm?: GridSpacing | undefined; md?: GridSpacing | undefined; lg?: GridSpacing | undefined; xl?: GridSpacing | ... 1 more ... | undefined; }`` | - | Defines the vertical space between the type `item` components.<br>It overrides the value of the `spacing` prop. |
| **size** | ``number | "auto" | "grow" | (GridSize)[] | { xs?: GridSize | undefined; sm?: GridSize | undefined; md?: GridSize | undefined; lg?: GridSize | undefined; xl?: GridSize | ... 1 more ... | undefined; }`` | - | Defines the column size of the grid. |
| **spacing** | ``string | number | (GridSpacing)[] | { xs?: GridSpacing | undefined; sm?: GridSpacing | undefined; md?: GridSpacing | undefined; lg?: GridSpacing | undefined; xl?: GridSpacing | ... 1 more ... | undefined; }`` | `0` | Defines the space between the type `item` components.<br>It can only be used on a type `container` component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **wrap** | ``"nowrap" | "wrap" | "wrap-reverse"`` | `'wrap'` | Defines the `flex-wrap` style property.<br>It's applied for all screen sizes. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaGrid-root | `root` | Class name applied to the root element. |
| .NovaGrid-container | `container` | Class name applied to the root element if 'container={true}'. |

