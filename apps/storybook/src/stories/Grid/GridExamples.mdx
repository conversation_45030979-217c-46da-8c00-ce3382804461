import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import GridBasicExample from './Examples/GridBasicExample';
import GridBasicExampleSource from './Examples/GridBasicExample.tsx?raw';
import GridSpacingExample from './Examples/GridSpacingExample';
import GridSpacingExampleSource from './Examples/GridSpacingExample.tsx?raw';
import GridResponsiveExample from './Examples/GridResponsiveExample';
import GridResponsiveExampleSource from './Examples/GridResponsiveExample.tsx?raw';

<Meta title="@hxnova/react-components/Layout/Grid/Examples" />

## Basic Grid
This demo illustrates a fluid grid layout that utilizes columns to scale and resize content dynamically. 
<div className="sb-unstyled">
    <GridBasicExample />
</div>
<CodeExpand code={GridBasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Grid Spacing
This demo highlights the use of the `rowSpacing` and `columnSpacing` props, allowing for independent control of row and column gaps within the grid. 

<div className="sb-unstyled">
  <GridSpacingExample />
</div>
<CodeExpand code={ GridSpacingExampleSource } showBorderTop style={{marginTop: 16}}/>

## Grid Responsive

This demo showcases the responsive capabilities of the `Grid` component, allowing prop values to adapt based on active `breakpoints`. Key properties such as `size`, `columns`, `columnSpacing`, `direction`, `rowSpacing`, and `spacing` can be set to different values for various screen sizes.

<div className="sb-unstyled">
  <GridResponsiveExample />
</div>
<CodeExpand code={ GridResponsiveExampleSource } showBorderTop style={{marginTop: 16}}/>

