import React from 'react';
import { Grid } from '@hxnova/react-components/Grid';
import { styled } from '@pigment-css/react';

const Item = styled('div')(({ theme }) => ({
  border: `1px solid ${theme.vars.palette.outlineDisabled}`,
  borderRadius: '4px',
  padding: '4px',
  textAlign: 'center',
}));

export default function GridDemo() {
  return (
    <Grid container spacing={{ xs: 2, md: 3 }} columns={{ xs: 4, sm: 8, md: 12 }}>
      {Array.from(Array(6)).map((_, index) => (
        <Grid key={index} size={{ xs: 2, sm: 4, md: 4 }}>
          <Item>{index + 1}</Item>
        </Grid>
      ))}
    </Grid>
  );
}
