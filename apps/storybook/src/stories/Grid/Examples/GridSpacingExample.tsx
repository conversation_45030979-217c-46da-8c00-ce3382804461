import React from 'react';
import { Grid } from '@hxnova/react-components/Grid';
import { styled } from '@pigment-css/react';

const Item = styled('div')(({ theme }) => ({
  border: `1px solid ${theme.vars.palette.outlineDisabled}`,
  borderRadius: '4px',
  padding: '4px',
  textAlign: 'center',
}));

export default function GridDemo() {
  return (
    <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 3 }}>
      <Grid size={6}>
        <Item>1</Item>
      </Grid>
      <Grid size={6}>
        <Item>2</Item>
      </Grid>
      <Grid size={6}>
        <Item>3</Item>
      </Grid>
      <Grid size={6}>
        <Item>4</Item>
      </Grid>
    </Grid>
  );
}
