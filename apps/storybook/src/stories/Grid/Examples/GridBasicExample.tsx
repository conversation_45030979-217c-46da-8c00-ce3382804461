import React from 'react';
import { Grid } from '@hxnova/react-components/Grid';
import { styled } from '@pigment-css/react';

const Item = styled('div')(({ theme }) => ({
  border: `1px solid ${theme.vars.palette.outlineDisabled}`,
  borderRadius: '4px',
  padding: '4px',
  textAlign: 'center',
}));

export default function GridDemo() {
  return (
    <Grid container spacing={2}>
      <Grid size={8}>
        <Item>size=8</Item>
      </Grid>
      <Grid size={4}>
        <Item>size=4</Item>
      </Grid>
      <Grid size={4}>
        <Item>size=4</Item>
      </Grid>
      <Grid size={8}>
        <Item>size=8</Item>
      </Grid>
    </Grid>
  );
}
