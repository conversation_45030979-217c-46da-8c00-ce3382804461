import { <PERSON>vas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';  
import * as ChipStories from './Chip.stories';
import { Button } from '@hxnova/react-components/Button';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import SelectedExample from './Examples/SelectedExample';
import SelectedExampleSource from './Examples/SelectedExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import IconsExample from './Examples/IconsExample';
import IconsExampleSource from './Examples/IconsExample.tsx?raw';


<Meta title="@hxnova/react-components/Chip/Examples" />
 
## Basic Chip

A basic Chip component displays a text `label` inside a pill-shaped container. If you provide an `onClick` handler, the chip will be interactive.

<div className="sb-unstyled">
    <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Selected Chip

You can indicate that a chip is selected / active by setting the `selected` prop to `true`.

<div className="sb-unstyled">
    <SelectedExample />
</div>
<CodeExpand code={SelectedExampleSource} showBorderTop style={{marginTop: 16}}/>

## Chip Sizes

Chips come in three sizes: small, medium (default), and large. These can be adjusted using the `size` prop.

<div className="sb-unstyled">
    <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Chip Icons

You can configure your button to show an icon on the left or right side of the button text. These can be configured with the `startIcon` or `endIcon` props.

<div className="sb-unstyled">
  <IconsExample />
</div>
<CodeExpand code={IconsExampleSource} showBorderTop style={{marginTop: 16}}/> 