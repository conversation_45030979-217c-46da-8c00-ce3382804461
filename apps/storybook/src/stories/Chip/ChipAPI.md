# API Documentation

- [Chip](#chip)

# Chip

API reference docs for the React Chip component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Chip from '@hxnova/react-components/Chip';
// or
import { Chip } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **label*** | `string` | - | The text label to show in the chip |
| **disabled** | ``false \| true`` | `false` | Whether or not the chip is disabled |
| **endIcon** | ``ReactElement<any, string \| JSXElementConstructor<any>>`` | - | Optional icon to show after the label |
| **selected** | ``false \| true`` | - | Whether or not the chip is selected/active |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | The size of the chip |
| **startIcon** | ``ReactElement<any, string \| JSXElementConstructor<any>>`` | - | Optional icon to show before the label |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaChip-root | `root` | Styles applied to the root element. |
| .NovaChip-sizeSmall | `sizeSmall` | Styles applied to the Button if `size="small"`. |
| .NovaChip-sizeMedium | `sizeMedium` | Styles applied to the Button if `size="medium"`. |
| .NovaChip-sizeLarge | `sizeLarge` | Styles applied to the Button if `size="large"`. |
| .NovaChip-iconStart | `iconStart` | Styles applied to the Button startIcon element. |
| .NovaChip-iconEnd | `iconEnd` | Styles applied to the Button endIcon element. |
| .NovaChip-disabled | `disabled` | Styles applied to the Button if `disabled={true}` |
| .NovaChip-selected | `selected` | Styles applied to the Button if `selected={true}` |

