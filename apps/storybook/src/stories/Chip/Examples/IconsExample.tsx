import { Chip } from '@hxnova/react-components/Chip';
import Icon from '@hxnova/icons/Icon';

export default function Demo() {
  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <Chip label={'Important'} startIcon={<Icon family="material" name="flag" size={24} />} onClick={() => {}} />
      <Chip label={'Removable'} endIcon={<Icon family="material" name="clear" size={24} />} onClick={() => {}} />
    </div>
  );
}
