import * as React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { Pagination as NovaPagination, PaginationProps } from '@hxnova/react-components/Pagination';
import { TablePagination as NovaTablePagination, TablePaginationProps } from '@hxnova/react-components/TablePagination';
import { action } from '@storybook/addon-actions';

export default {
  title: '@hxnova/react-components/Pagination',
  component: NovaPagination,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=3354-1148&p=f&t=kXkdQu06bCG99EaT-0',
    },
  },
  tags: ['!autodocs'],
} as Meta<typeof NovaPagination>;

const Template: StoryFn<(props: PaginationProps) => JSX.Element> = (args) => {
  const [page, setPage] = React.useState(1);

  const handleChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
    action('Page Changed:')(value);
  };

  return <NovaPagination {...args} page={page} onChange={handleChange} />;
};

const TablePaginationTemplate: StoryFn<(props: TablePaginationProps) => JSX.Element> = (args) => {
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(5);

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage);
    action('Page Changed:')(newPage);
  };

  const handleChangeRowsPerPage = (event: React.SyntheticEvent | null, newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0);
    action('Items Per Page Changed:')(newRowsPerPage);
  };

  return (
    <NovaTablePagination
      {...args}
      page={page}
      rowsPerPage={rowsPerPage}
      onPageChange={handleChangePage}
      onRowsPerPageChange={handleChangeRowsPerPage}
      style={{ minWidth: '400px' }}
    />
  );
};

export const Pagination = {
  render: Template,
  args: {
    count: 10,
    size: 'medium',
    disabled: false,
    showFirstButton: false,
    showLastButton: false,
    hidePrevButton: false,
    hideNextButton: false,
    boundaryCount: 1,
    siblingCount: 1,
  },
  argTypes: {
    size: {
      control: { type: 'radio' },
      options: ['small', 'medium', 'large'],
    },
    boundaryCount: {
      control: { type: 'number', min: 0, max: 5 },
    },
    siblingCount: {
      control: { type: 'number', min: 0, max: 5 },
    },
  },
  parameters: {
    controls: {
      include: [
        'size',
        'disabled',
        'showFirstButton',
        'showLastButton',
        'hidePrevButton',
        'hideNextButton',
        'boundaryCount',
        'siblingCount',
        'count',
      ],
    },
  },
};

export const TablePagination = {
  render: TablePaginationTemplate,
  args: {
    component: 'div',
    size: 'medium',
    count: 100,
    rowsPerPageOptions: [5, 10, 25, 50, 100],
    showFirstButton: false,
    showLastButton: false,
    disabled: false,
  },
  argTypes: {
    size: {
      control: { type: 'radio' },
      options: ['small', 'medium', 'large'],
    },
    count: {
      type: 'number',
    },
    disabled: {
      type: 'boolean',
    },
    showFirstButton: {
      type: 'boolean',
    },
    showLastButton: {
      type: 'boolean',
    },
  },
  parameters: {
    controls: {
      include: ['size', 'count', 'rowsPerPageOptions', 'disabled', 'showFirstButton', 'showLastButton'],
    },
  },
};
