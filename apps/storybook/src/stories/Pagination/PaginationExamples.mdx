import { <PERSON>vas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import * as PaginationStories from './Pagination.stories';
import { Pagination, TablePagination } from '@hxnova/react-components';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import ControlledExample from './Examples/ControlledExample';
import ControlledExampleSource from './Examples/ControlledExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import ButtonsExample from './Examples/ButtonsExample';
import ButtonsExampleSource from './Examples/ButtonsExample.tsx?raw';
import RangesExample from './Examples/RangesExample';
import RangesExampleSource from './Examples/RangesExample.tsx?raw';
import CustomIconsExample from './Examples/CustomIconsExample';
import CustomIconsExampleSource from './Examples/CustomIconsExample.tsx?raw';
import TablePaginationExample from './Examples/TablePaginationExample';
import TablePaginationExampleSource from './Examples/TablePaginationExample.tsx?raw';

<Meta title="@hxnova/react-components/Pagination/Examples" />

## Basic Usage

The Pagination component provides a way to navigate through paginated content. The `count` prop specifies the total number of pages.

<div className="doc-story sb-story sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Controlled Pagination

Use the `page` and `onChange` props to control the current page and handle page changes.

<div className="doc-story sb-story sb-unstyled">
  <ControlledExample />
</div>
<CodeExpand code={ControlledExampleSource} showBorderTop style={{marginTop: 16}}/>

## Pagination Sizes

The `size` prop accepts "small", "medium" (default), or "large" to control the Pagination component's dimensions.

<div className="doc-story sb-story sb-unstyled">
  <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Navigation Buttons

The `showFirstButton`, `showLastButton`, `hidePrevButton`, and `hideNextButton` props allow you to customize which navigation buttons are displayed.

<div className="doc-story sb-story sb-unstyled">
  <ButtonsExample />
</div>
<CodeExpand code={ButtonsExampleSource} showBorderTop style={{marginTop: 16}}/>

## Pagination Ranges

The `siblingCount` and `boundaryCount` props control how many page numbers are displayed around the current page and at the boundaries.

<div className="doc-story sb-story sb-unstyled">
  <RangesExample />
</div>
<CodeExpand code={RangesExampleSource} showBorderTop style={{marginTop: 16}}/>

## Custom Icons

The `renderItem` prop allows you to customize the appearance of pagination items, including using custom icons for navigation buttons.

<div className="doc-story sb-story sb-unstyled">
  <CustomIconsExample />
</div>
<CodeExpand code={CustomIconsExampleSource} showBorderTop style={{marginTop: 16}}/>

## Table Pagination

The TablePagination component provides pagination controls specifically designed for tables, including items per page selection.

<div className="doc-story sb-story sb-unstyled">
  <TablePaginationExample />
</div>
<CodeExpand code={TablePaginationExampleSource} showBorderTop style={{marginTop: 16}}/>
