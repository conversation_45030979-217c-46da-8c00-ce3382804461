import { Pagination } from '@hxnova/react-components/Pagination';

export default function RangesExample() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <div>
        <h4>No siblings (siblingCount=0)</h4>
        <Pagination count={11} defaultPage={6} siblingCount={0} />
      </div>

      <div>
        <h4>Default (siblingCount=1, boundaryCount=1)</h4>
        <Pagination count={11} defaultPage={6} />
      </div>

      <div>
        <h4>Extended boundaries (boundaryCount=2)</h4>
        <Pagination count={11} defaultPage={6} boundaryCount={2} />
      </div>

      <div>
        <h4>No siblings with extended boundaries</h4>
        <Pagination count={11} defaultPage={6} siblingCount={0} boundaryCount={2} />
      </div>
    </div>
  );
}
