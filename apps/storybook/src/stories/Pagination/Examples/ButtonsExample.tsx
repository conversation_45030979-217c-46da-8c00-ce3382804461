import { Pagination } from '@hxnova/react-components/Pagination';

export default function ButtonsExample() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <div>
        <h4>With previous and next buttons (default)</h4>
        <Pagination count={10} />
      </div>

      <div>
        <h4>Without previous and next buttons</h4>
        <Pagination count={10} hidePrevButton hideNextButton />
      </div>

      <div>
        <h4>With first and last buttons</h4>
        <Pagination count={10} showFirstButton showLastButton />
      </div>
    </div>
  );
}
