# API Documentation

- [Link](#link)

# Link

API reference docs for the React Link component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Link from '@hxnova/react-components/Link';
// or
import { Link } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - |  |
| **disabled** | ``false \| true`` | `false` | If `true`, the component is disabled. |
| **endDecorator** | `ReactNode` | - | Element placed after the children. |
| **focusVisible** | ``false \| true`` | - |  |
| **startDecorator** | `ReactNode` | - | Element placed before the children. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **underline** | ``"none" \| "hover" \| "always"`` | `'always'` | Controls when the link should have an underline. |
| **variant** | ``"displayLarge" \| "displayMedium" \| "displaySmall" \| "headlineLarge" \| "headlineMedium" \| "headlineSmall" \| "titleLarge" \| "titleMedium" \| "titleSmall" \| "bodyLarge" \| "bodyMedium" \| "bodySmall" \| "labelLarge" \| "labelMedium" \| "labelSmall" \| "inherit"`` | `'labelMedium'` | Applies the theme typography styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaLink-root | `'a'` | The component that renders the root. |
| startDecorator | .NovaLink-startDecorator | `'span'` | The component that renders the start decorator. |
| endDecorator | .NovaLink-endDecorator | `'span'` | The component that renders the end decorator. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaLink-underlineNone | `underlineNone` | Styles applied to the root element if `underline="none"`. |
| .NovaLink-underlineHover | `underlineHover` | Styles applied to the root element if `underline="hover"`. |
| .NovaLink-underlineAlways | `underlineAlways` | Styles applied to the root element if `underline="always"`. |
| .NovaLink-button | `button` | Styles applied to the root element if `component="button"`. |
| .NovaLink-disabled | `disabled` | Styles applied to the root element if `disabled={true}`. |
| .NovaLink-focusVisible | `focusVisible` | Styles applied to the root element if the link is keyboard focused. |

