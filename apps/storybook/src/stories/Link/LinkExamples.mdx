import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import * as LinkStories from './Link.stories';
import VariantExample from './Examples/VariantExample';
import VariantExampleSource from './Examples/VariantExample.tsx?raw';
import UnderlineExample from './Examples/UnderlineExample';
import UnderlineExampleSource from './Examples/UnderlineExample.tsx?raw';
import DecoratorExample from './Examples/DecoratorExample';
import DecoratorExampleSource from './Examples/DecoratorExample.tsx?raw';

<Meta title="@hxnova/react-components/Link/Examples" />
 
## Link Variants

The Link component is a text element representing a hyperlink. It supports all the same `variant` options as the Typography component. 

<div className="sb-unstyled">
    <VariantExample />
</div>
<CodeExpand code={VariantExampleSource} showBorderTop style={{marginTop: 16}}/>

## Underline Behavior

By default the Link component shows an underline to indicate that it is a hyperlink. You can change this behavior to only show the underline on hover or never using the `underline` prop.

<div className="sb-unstyled">
    <UnderlineExample />
</div>
<CodeExpand code={UnderlineExampleSource} showBorderTop style={{marginTop: 16}}/>

## Link Decorators

You can configure your button to show an decorator on the left or right side of the button text. These can be configured with the `startDecorator` or `endDecorator` props.

<div className="sb-unstyled">
  <DecoratorExample />
</div>
<CodeExpand code={DecoratorExampleSource} showBorderTop style={{marginTop: 16}}/>