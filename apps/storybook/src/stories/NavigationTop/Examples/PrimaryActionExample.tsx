import { NavigationTop } from '@hxnova/react-components/NavigationTop';
import MetrologyReportingIcon from '@nexusui/branding/MetrologyReporting';

export default function Demo() {
  return (
    <NavigationTop
      productLogo={<MetrologyReportingIcon height={40} width={40} />}
      pageTitle={'Metrology Reporting'}
      primaryActions={[
        {
          children: 'Primary Action',
          onClick: () => console.log('primary action clicked'),
        },
        {
          children: 'Other Action',
          variant: 'outlined',
          onClick: () => console.log('other action clicked'),
        },
      ]}
    />
  );
}
