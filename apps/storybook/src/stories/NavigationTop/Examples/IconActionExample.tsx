import { NavigationTop } from '@hxnova/react-components/NavigationTop';
import Icon from '@hxnova/icons/Icon';
import MetrologyReportingIcon from '@nexusui/branding/MetrologyReporting';

export default function Demo() {
  return (
    <NavigationTop
      productLogo={<MetrologyReportingIcon height={40} width={40} />}
      pageTitle={'Metrology Reporting'}
      iconActions={[
        {
          icon: <Icon family="material" name="invert_colors" size={24} />,
          label: 'Menu',
          onClick: () => console.log('icon action clicked'),
        },
        {
          icon: <Icon family="material" name="translate" size={24} />,
          label: 'Menu2',
          onClick: () => console.log('icon action clicked'),
        },
        {
          icon: <Icon family="material" name="more_vert" size={24} />,
          label: 'Menu3',
          onClick: () => console.log('icon action clicked'),
        },
      ]}
      primaryActions={[
        {
          children: 'Primary Action',
          onClick: () => console.log('primary action clicked'),
        },
      ]}
    />
  );
}
