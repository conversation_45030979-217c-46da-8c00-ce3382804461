import { NavigationTop } from '@hxnova/react-components/NavigationTop';
import { Avatar } from '@hxnova/react-components/Avatar';
import MetrologyReportingIcon from '@nexusui/branding/MetrologyReporting';

export default function Demo() {
  return (
    <NavigationTop
      productLogo={<MetrologyReportingIcon height={40} width={40} />}
      pageTitle={'Metrology Reporting'}
      userAvatar={<Avatar onClick={() => console.log('avatar clicked')}>AX</Avatar>}
    />
  );
}
