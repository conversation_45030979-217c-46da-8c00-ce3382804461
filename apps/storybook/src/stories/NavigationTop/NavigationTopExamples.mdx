import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import * as NavigationTopStories from './NavigationTop.stories';
import ProductInfoExample from './Examples/ProductInfoExample';
import ProductInfoExampleSource from './Examples/ProductInfoExample.tsx?raw';
import PrimaryActionExample from './Examples/PrimaryActionExample';
import PrimaryActionExampleSource from './Examples/PrimaryActionExample.tsx?raw';
import IconActionExample from './Examples/IconActionExample';
import IconActionExampleSource from './Examples/IconActionExample.tsx?raw';
import UserAvatarExample from './Examples/UserAvatarExample';
import UserAvatarExampleSource from './Examples/UserAvatarExample.tsx?raw';
import SearchExample from './Examples/SearchExample';
import SearchExampleSource from './Examples/SearchExample.tsx?raw';

<Meta title="@hxnova/react-components/NavigationTop/Examples" />
 
## Showing Product Information

The NavigationTop is a stylized container for navigation elements. In its simplest form, it shows the title of the page you are on. You can also provide a `productLogo` to show beside the title.

<div className="sb-unstyled">
    <ProductInfoExample />
</div>
<CodeExpand code={ProductInfoExampleSource} showBorderTop style={{marginTop: 16}}/>

## Primary Actions

Primary actions are rendered as CommonButton components. You can configure the buttons through the `primaryActions` prop.

<div className="sb-unstyled">
    <PrimaryActionExample />
</div>
<CodeExpand code={PrimaryActionExampleSource} showBorderTop style={{marginTop: 16}}/>

## Secondary Actions

Less important actions can be rendered as IconButton components. You can configure the actions through the `iconActions` prop.

<div className="sb-unstyled">
    <IconActionExample />
</div>
<CodeExpand code={IconActionExampleSource} showBorderTop style={{marginTop: 16}}/>


## User Menu

It's common to show a user's avatar / user menu in the NavigationTop. You can pass a component for this through the `userAvatar` prop.

<div className="sb-unstyled">
    <UserAvatarExample />
</div>
<CodeExpand code={UserAvatarExampleSource} showBorderTop style={{marginTop: 16}}/>


## Search Bar

The NavigationTop has a built in search bar. If you provide a callback function for `onSearchChange` the search bar will be displayed for you automatically.

<div className="sb-unstyled">
    <SearchExample />
</div>
<CodeExpand code={SearchExampleSource} showBorderTop style={{marginTop: 16}}/>

