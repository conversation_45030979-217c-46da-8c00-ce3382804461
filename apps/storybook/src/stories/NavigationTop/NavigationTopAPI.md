# API Documentation

- [NavigationTop](#navigationtop)

# NavigationTop

API reference docs for the React NavigationTop component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import NavigationTop from '@hxnova/react-components/NavigationTop';
// or
import { NavigationTop } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **divider** | ``false \| true`` | `true` | Whether or not to show a divider below the bar |
| **iconActions** | ``{ icon: ReactNode; onClick: () => void; label: string; }[]`` | - | Secondary actions to display in the toolbar. These will be presented as icon buttons. |
| **onSearchChange** | ``(value: string) => void`` | - | Callback function executed when the search value changes. If provided, a search bar will be displayed in the toolbar<br>@param value the current text in the search field |
| **pageTitle** | `ReactNode` | - | Title to display in the toolbar |
| **primaryActions** | `ButtonProps[]` | - | Primary actions to display in the toolbar. These will be presented as buttons. |
| **productLogo** | `ReactNode` | - | Product Logo to display in the toolbar |
| **userAvatar** | `ReactNode` | - | User avatar to display at the far right of the toolbar |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| pageTitle | .NovaNavigationTop-pageTitle | `'Typography'` | The component that renders the page title. |
| searchBar | .NovaNavigationTop-searchBar | `'TextField'` | The component that renders the SearchBar. |
| iconAction | .NovaNavigationTop-iconAction | `'IconButton'` | The component that renders the icon action. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaNavigationTop-root | `root` | Styles applied to the root element. |
| .NovaNavigationTop-productLogo | `productLogo` | Styles applied to the logo element. |
| .NovaNavigationTop-navigationContent | `navigationContent` | Styles applied to the children. |
| .NovaNavigationTop-iconActions | `iconActions` | Styles applied to the icon actions element. |
| .NovaNavigationTop-primaryActions | `primaryActions` | Styles applied to the primary actions element. |
| .NovaNavigationTop-userAvatar | `userAvatar` | Styles applied to the user avatar element. |

