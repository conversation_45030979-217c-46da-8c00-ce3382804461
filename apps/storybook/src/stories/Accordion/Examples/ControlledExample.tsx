import { useState } from 'react';
import { Accordion } from '@hxnova/react-components/Accordion';

export default function ControlledExample() {
  const [expanded, setExpanded] = useState<string | false>(false);

  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <Accordion.Group>
      <Accordion.Item expanded={expanded === 'panel1'} onChange={handleChange('panel1')}>
        <Accordion.Summary>Controlled Panel 1</Accordion.Summary>
        <Accordion.Details>
          This is a controlled accordion panel. It can be expanded or collapsed programmatically.
        </Accordion.Details>
      </Accordion.Item>
      <Accordion.Item expanded={expanded === 'panel2'} onChange={handleChange('panel2')}>
        <Accordion.Summary>Controlled Panel 2</Accordion.Summary>
        <Accordion.Details>Only one panel can be expanded at a time in this controlled example.</Accordion.Details>
      </Accordion.Item>
      <Accordion.Item expanded={expanded === 'panel3'} onChange={handleChange('panel3')}>
        <Accordion.Summary>Controlled Panel 3</Accordion.Summary>
        <Accordion.Details>Try clicking different panels to see how they interact with each other.</Accordion.Details>
      </Accordion.Item>
    </Accordion.Group>
  );
}
