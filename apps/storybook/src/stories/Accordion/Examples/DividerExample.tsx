import { Accordion } from '@hxnova/react-components/Accordion';

export default function DividerExample() {
  return (
    <Accordion.Group disableDivider>
      <Accordion.Item>
        <Accordion.Summary>No divider 1</Accordion.Summary>
        <Accordion.Details>This accordion group has dividers disabled.</Accordion.Details>
      </Accordion.Item>
      <Accordion.Item>
        <Accordion.Summary>No divider 2</Accordion.Summary>
        <Accordion.Details>Notice how there are no lines between the accordion items.</Accordion.Details>
      </Accordion.Item>
      <Accordion.Item>
        <Accordion.Summary>No divider 3</Accordion.Summary>
        <Accordion.Details>The seamless look can be preferred in some design contexts.</Accordion.Details>
      </Accordion.Item>
    </Accordion.Group>
  );
}
