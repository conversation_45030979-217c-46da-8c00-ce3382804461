import { Accordion } from '@hxnova/react-components/Accordion';

export default function DisabledExample() {
  return (
    <Accordion.Group>
      <Accordion.Item>
        <Accordion.Summary>Enabled accordion</Accordion.Summary>
        <Accordion.Details>This accordion can be expanded and collapsed normally.</Accordion.Details>
      </Accordion.Item>
      <Accordion.Item disabled>
        <Accordion.Summary>Disabled accordion</Accordion.Summary>
        <Accordion.Details>This accordion is disabled and cannot be interacted with.</Accordion.Details>
      </Accordion.Item>
      <Accordion.Item>
        <Accordion.Summary>Another enabled accordion</Accordion.Summary>
        <Accordion.Details>
          This accordion demonstrates the contrast between enabled and disabled states.
        </Accordion.Details>
      </Accordion.Item>
    </Accordion.Group>
  );
}
