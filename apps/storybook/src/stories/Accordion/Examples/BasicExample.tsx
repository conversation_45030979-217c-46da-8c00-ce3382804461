import { Accordion } from '@hxnova/react-components/Accordion';

export default function BasicExample() {
  return (
    <Accordion.Group>
      <Accordion.Item>
        <Accordion.Summary>First accordion</Accordion.Summary>
        <Accordion.Details>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
          magna aliqua.
        </Accordion.Details>
      </Accordion.Item>
      <Accordion.Item>
        <Accordion.Summary>Second accordion</Accordion.Summary>
        <Accordion.Details>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
          magna aliqua.
        </Accordion.Details>
      </Accordion.Item>
      <Accordion.Item>
        <Accordion.Summary>Third accordion</Accordion.Summary>
        <Accordion.Details>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
          magna aliqua.
        </Accordion.Details>
      </Accordion.Item>
    </Accordion.Group>
  );
}
