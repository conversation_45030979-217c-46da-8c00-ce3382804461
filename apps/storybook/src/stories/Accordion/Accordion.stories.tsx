import { StoryFn, Meta } from '@storybook/react';
import {
  Accordion as NovaAccordion,
  AccordionGroupProps,
  AccordionItemProps,
} from '@hxnova/react-components/Accordion';

const meta = {
  title: '@hxnova/react-components/Accordion',
  component: NovaAccordion.Group,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1348-485&p=f&t=m3joCs3QTEvzU2a0-0',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof NovaAccordion>;

export default meta;

const Template: StoryFn<(props: AccordionItemProps) => JSX.Element> = (args) => {
  return (
    <NovaAccordion.Item {...args}>
      <NovaAccordion.Summary>Header</NovaAccordion.Summary>
      <NovaAccordion.Details>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
        magna aliqua.
      </NovaAccordion.Details>
    </NovaAccordion.Item>
  );
};

const AccordionGroupTemplate: StoryFn<(props: AccordionGroupProps) => JSX.Element> = (args) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <NovaAccordion.Group {...args}>
        <NovaAccordion.Item>
          <NovaAccordion.Summary>Accordion 1</NovaAccordion.Summary>
          <NovaAccordion.Details>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
            dolore magna aliqua.
          </NovaAccordion.Details>
        </NovaAccordion.Item>
        <NovaAccordion.Item>
          <NovaAccordion.Summary>Accordion 2</NovaAccordion.Summary>
          <NovaAccordion.Details>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
            dolore magna aliqua.
          </NovaAccordion.Details>
        </NovaAccordion.Item>
      </NovaAccordion.Group>
    </div>
  );
};

export const Accordion = {
  render: Template,
  args: {
    expanded: false,
    disabled: false,
  },
  argTypes: {
    expanded: {
      control: { type: 'radio' },
      options: [true, false],
    },
    disabled: {
      control: { type: 'radio' },
      options: [true, false],
    },
  },
  parameters: {
    controls: {
      include: ['expanded', 'disabled'],
    },
  },
};

export const AccordionGroup = {
  render: AccordionGroupTemplate,
  args: {
    density: 'standard',
    disableDivider: false,
  },
  argTypes: {
    density: {
      control: { type: 'radio' },
      options: ['standard', 'compact', 'comfortable'],
    },
    disableDivider: {
      control: { type: 'radio' },
      options: [true, false],
    },
  },
  parameters: {
    controls: {
      include: ['density', 'disableDivider'],
    },
  },
};
