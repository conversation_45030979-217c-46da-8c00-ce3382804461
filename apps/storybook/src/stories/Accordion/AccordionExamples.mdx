import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import ControlledExample from './Examples/ControlledExample';
import ControlledExampleSource from './Examples/ControlledExample.tsx?raw';
import DensityExample from './Examples/DensityExample';
import DensityExampleSource from './Examples/DensityExample.tsx?raw';
import DisabledExample from './Examples/DisabledExample';
import DisabledExampleSource from './Examples/DisabledExample.tsx?raw';
import DividerExample from './Examples/DividerExample';
import DividerExampleSource from './Examples/DividerExample.tsx?raw';

<Meta title="@hxnova/react-components/Accordion/Examples" />

## Basic Usage

The basic Accordion component provides expandable/collapsible sections for organizing content.

<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Controlled Accordion

Use the `expanded` prop along with `onChange` to control the accordion state programmatically. This allows you to manage which panel is open and handle state changes.

<div className="sb-unstyled">
  <ControlledExample />
</div>
<CodeExpand code={ControlledExampleSource} showBorderTop style={{marginTop: 16}}/>

## Disabled State

Add the `disabled` prop to an `Accordion` component to prevent user interaction with specific panels while keeping others active.

<div className="sb-unstyled">
  <DisabledExample />
</div>
<CodeExpand code={DisabledExampleSource} showBorderTop style={{marginTop: 16}}/>

## Density Variants

The `density` prop on `Accordion.Group` lets you adjust the spacing. Options include `"default"`, `"comfortable"`, and `"compact"` to suit different layout needs.

<div className="sb-unstyled">
  <DensityExample />
</div>
<CodeExpand code={DensityExampleSource} showBorderTop style={{marginTop: 16}}/>

## Without Dividers

Set `disableDivider` on the `Accordion.Group` to remove the separating lines between accordion items for a more seamless appearance.

<div className="sb-unstyled">
  <DividerExample />
</div>
<CodeExpand code={DividerExampleSource} showBorderTop style={{marginTop: 16}}/>
