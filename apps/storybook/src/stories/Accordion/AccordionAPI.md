# API Documentation

- [AccordionDetails](#accordiondetails)
- [AccordionGroup](#accordiongroup)
- [AccordionItem](#accordionitem)
- [AccordionSummary](#accordionsummary)

# AccordionDetails

API reference docs for the React AccordionDetails component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import AccordionDetails from '@hxnova/react-components/AccordionDetails';
// or
import { AccordionDetails } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | Used to render icon or text elements inside the AccordionDetails if `src` is not set.<br>This can be an element, or just a string. |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component.<br>The component used for the Root slot.<br>Either a string to use a HTML element or a component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAccordionDetails-root | `'div'` | The component that renders the root. |
| content | .NovaAccordionDetails-content | `'div'` | The component that renders the content. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaAccordionDetails-disabled | `disabled` | Class name applied when the accordion is disabled. |
| .NovaAccordionDetails-expanded | `expanded` | Class name applied to the root element when expanded. |

<br><br>

# AccordionGroup

API reference docs for the React AccordionGroup component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import AccordionGroup from '@hxnova/react-components/AccordionGroup';
// or
import { AccordionGroup } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | Used to render icon or text elements inside the AccordionGroup if `src` is not set.<br>This can be an element, or just a string. |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component.<br>The component used for the Root slot.<br>Either a string to use a HTML element or a component. |
| **density** | ``"standard" \| "compact" \| "comfortable"`` | `'standard'` | The size of the component. |
| **disableDivider** | ``false \| true`` | `false` | If `true`, the divider between accordions will be hidden. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **transition** | ``string \| { initial: string; expanded: string; }`` | `'0.2s ease'` | The CSS transition for the Accordion details. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAccordionGroup-root | `'div'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaAccordionGroup-densityStandard | `densityStandard` | Class name applied to the root element if `density="standard"`. |
| .NovaAccordionGroup-densityCompact | `densityCompact` | Class name applied to the root element if `density="compact"`. |
| .NovaAccordionGroup-densityComfortable | `densityComfortable` | Class name applied to the root element if `density="comfortable"`. |

<br><br>

# AccordionItem

API reference docs for the React AccordionItem component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import AccordionItem from '@hxnova/react-components/AccordionItem';
// or
import { AccordionItem } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **accordionId** | `string` | - | The id to be used in the AccordionDetails which is controlled by the AccordionSummary.<br>If not provided, the id is autogenerated. |
| **children** | `ReactNode` | - | Used to render icon or text elements inside the AccordionItem if `src` is not set.<br>This can be an element, or just a string. |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component.<br>The component used for the Root slot.<br>Either a string to use a HTML element or a component. |
| **defaultExpanded** | ``false \| true`` | `false` | If `true`, expands the accordion by default. |
| **disabled** | ``false \| true`` | `false` | If `true`, the component is disabled. |
| **expanded** | ``false \| true`` | - | If `true`, expands the accordion, otherwise collapse it.<br>Setting this prop enables control over the accordion. |
| **onChange** | ``(event: SyntheticEvent<Element, Event>, expanded: boolean) => void`` | - | Callback fired when the expand/collapse state is changed.<br>@param event The event source of the callback. **Warning**: This is a generic event not a change event.<br>@param expanded The `expanded` state of the accordion. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAccordionItem-root | `'div'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaAccordionItem-expanded | `expanded` | Class name applied to the root element if `expanded` is true. |
| .NovaAccordionItem-disabled | `disabled` | Class name applied to the root element if `disabled` is true. |

<br><br>

# AccordionSummary

API reference docs for the React AccordionSummary component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import AccordionSummary from '@hxnova/react-components/AccordionSummary';
// or
import { AccordionSummary } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | Used to render icon or text elements inside the AccordionSummary if `src` is not set.<br>This can be an element, or just a string. |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component.<br>The component used for the Root slot.<br>Either a string to use a HTML element or a component. |
| **indicator** | `ReactNode` | `<KeyboardArrowDown />` | The indicator element to display. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAccordionSummary-root | `'div'` | The component that renders the root. |
| button | .NovaAccordionSummary-button | `'button'` | The component that renders the button. |
| indicator | .NovaAccordionSummary-indicator | `'span'` | The component that renders the indicator. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaAccordionSummary-disabled | `disabled` | Class name applied when the accordion is disabled. |
| .NovaAccordionSummary-expanded | `expanded` | Class name applied when the accordion is expanded. |

