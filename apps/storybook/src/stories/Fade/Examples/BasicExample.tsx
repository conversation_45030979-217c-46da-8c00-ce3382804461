import { useState } from 'react';
import { Fade } from '@hxnova/react-components/Fade';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function BasicExample() {
  const [checked, setChecked] = useState(false);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <Typography variant="titleSmall" style={{ marginBottom: '16px' }}>
          Basic Fade Transition
        </Typography>
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
          <Switch checked={checked} onChange={() => setChecked((prev) => !prev)} endDecorator="Toggle Content" />
          <Fade in={checked} timeout={300}>
            <Box
              style={{
                padding: 3,
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 'var(--radius-xs)',
                backgroundColor: 'var(--palette-surfaceContainer)',
                width: 300,
                textAlign: 'center',
              }}
            >
              <Typography variant="bodyMedium">
                This content fades in and out smoothly using the Fade transition component.
              </Typography>
            </Box>
          </Fade>
        </div>
      </div>

      <div>
        <Typography variant="titleSmall" style={{ marginBottom: '16px' }}>
          Different Timing Options
        </Typography>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', gap: '16px' }}>
            <div style={{ flex: 1, textAlign: 'center' }}>
              <Typography variant="bodySmall" style={{ marginBottom: '8px' }}>
                Fast (150ms)
              </Typography>
              <Fade in={checked} timeout={150}>
                <Box
                  style={{
                    padding: 2,
                    backgroundColor: 'var(--palette-inversePrimary)',
                    border: '1px solid var(--palette-outline)',
                    borderRadius: 'var(--radius-2xs)',
                    height: 60,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="bodySmall">Fast</Typography>
                </Box>
              </Fade>
            </div>
            <div style={{ flex: 1, textAlign: 'center' }}>
              <Typography variant="bodySmall" style={{ marginBottom: '8px' }}>
                Normal (300ms)
              </Typography>
              <Fade in={checked} timeout={300}>
                <Box
                  style={{
                    padding: 2,
                    backgroundColor: 'var(--palette-secondaryContainer)',
                    border: '1px solid var(--palette-outline)',
                    borderRadius: 'var(--radius-2xs)',
                    height: 60,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="bodySmall">Normal</Typography>
                </Box>
              </Fade>
            </div>
            <div style={{ flex: 1, textAlign: 'center' }}>
              <Typography variant="bodySmall" style={{ marginBottom: '8px' }}>
                Slow (600ms)
              </Typography>
              <Fade in={checked} timeout={600}>
                <Box
                  style={{
                    padding: 2,
                    backgroundColor: 'var(--palette-surfaceContainer)',
                    border: '1px solid var(--palette-outline)',
                    borderRadius: 'var(--radius-2xs)',
                    height: 60,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="bodySmall">Slow</Typography>
                </Box>
              </Fade>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
