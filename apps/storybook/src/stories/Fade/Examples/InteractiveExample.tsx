import { useState } from 'react';
import { Fade } from '@hxnova/react-components/Fade';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function InteractiveExample() {
  const [activeCard, setActiveCard] = useState<number | null>(null);

  const cardData = [
    {
      id: 1,
      title: 'Card 1',
      content: 'This is the first card with important information.',
      backgroundColor: 'var(--palette-primaryContainer)',
      borderColor: 'var(--palette-outline)',
      textColor: 'var(--palette-onPrimaryContainer)',
    },
    {
      id: 2,
      title: 'Card 2',
      content: 'This is the second card with different content.',
      backgroundColor: 'var(--palette-secondaryContainer)',
      borderColor: 'var(--palette-outline)',
      textColor: 'var(--palette-onSecondaryContainer)',
    },
    {
      id: 3,
      title: 'Card 3',
      content: 'This is the third card with unique details.',
      backgroundColor: 'var(--palette-surfaceContainer)',
      borderColor: 'var(--palette-outline)',
      textColor: 'var(--palette-onSurface)',
    },
  ];

  // Extract individual card data to avoid function parameters in JSX
  const card1 = cardData[0];
  const card2 = cardData[1];
  const card3 = cardData[2];

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '24px' }}>
      <Typography variant="titleMedium">Interactive Card Transitions</Typography>

      <div style={{ display: 'flex', gap: '12px' }}>
        <Button
          variant={activeCard === card1.id ? 'filled' : 'outlined'}
          onClick={() => setActiveCard(activeCard === card1.id ? null : card1.id)}
        >
          {activeCard === card1.id ? `Hide ${card1.title}` : `Show ${card1.title}`}
        </Button>
        <Button
          variant={activeCard === card2.id ? 'filled' : 'outlined'}
          onClick={() => setActiveCard(activeCard === card2.id ? null : card2.id)}
        >
          {activeCard === card2.id ? `Hide ${card2.title}` : `Show ${card2.title}`}
        </Button>
        <Button
          variant={activeCard === card3.id ? 'filled' : 'outlined'}
          onClick={() => setActiveCard(activeCard === card3.id ? null : card3.id)}
        >
          {activeCard === card3.id ? `Hide ${card3.title}` : `Show ${card3.title}`}
        </Button>
      </div>

      <div style={{ position: 'relative', width: '350px', height: '150px' }}>
        <Fade in={activeCard === card1.id} timeout={300} easing="ease-in-out">
          <Box
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              padding: 3,
              backgroundColor: card1.backgroundColor,
              border: `1px solid ${card1.borderColor}`,
              borderRadius: 'var(--radius-xs)',
              textAlign: 'center',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              gap: 1,
            }}
          >
            <Typography variant="titleSmall" style={{ color: card1.textColor }}>
              {card1.title}
            </Typography>
            <Typography variant="bodyMedium" style={{ color: card1.textColor }}>
              {card1.content}
            </Typography>
          </Box>
        </Fade>

        <Fade in={activeCard === card2.id} timeout={300} easing="ease-in-out">
          <Box
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              padding: 3,
              backgroundColor: card2.backgroundColor,
              border: `1px solid ${card2.borderColor}`,
              borderRadius: 'var(--radius-xs)',
              textAlign: 'center',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              gap: 1,
            }}
          >
            <Typography variant="titleSmall" style={{ color: card2.textColor }}>
              {card2.title}
            </Typography>
            <Typography variant="bodyMedium" style={{ color: card2.textColor }}>
              {card2.content}
            </Typography>
          </Box>
        </Fade>

        <Fade in={activeCard === card3.id} timeout={300} easing="ease-in-out">
          <Box
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              padding: 3,
              backgroundColor: card3.backgroundColor,
              border: `1px solid ${card3.borderColor}`,
              borderRadius: 'var(--radius-xs)',
              textAlign: 'center',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              gap: 1,
            }}
          >
            <Typography variant="titleSmall" style={{ color: card3.textColor }}>
              {card3.title}
            </Typography>
            <Typography variant="bodyMedium" style={{ color: card3.textColor }}>
              {card3.content}
            </Typography>
          </Box>
        </Fade>
      </div>

      {activeCard === null && (
        <Typography variant="bodySmall" style={{ color: 'text.secondary', textAlign: 'center' }}>
          Click any button above to see the card fade in
        </Typography>
      )}
    </div>
  );
}
