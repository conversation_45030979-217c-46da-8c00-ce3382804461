# Fade API

API reference docs for the React Fade component. Learn about the props, CSS, and other APIs of this exported module.

## Demos

For examples and details on the usage of this React component, visit the component demo pages:

* Transitions

## Import

```jsx
import Fade from '@hxnova/react-components/Fade';
// or
import { Fade } from '@hxnova/react-components';
```

Learn about the difference by reading this guide on minimizing bundle size.

The Fade transition is used by the Modal component. It uses react-transition-group internally.

## Props

Props of the Transition component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children*** | `element` | - | A single child content element. This needs to be able to hold a ref. |
| **addEndListener** | `func` | - | Add a custom transition end trigger. Called with the transitioning DOM node and a done callback. Allows for more fine grained transition end logic. Note: Timeouts are still used as a fallback if provided. |
| **appear** | `bool` | `true` | Perform the enter transition when it first mounts if `in` is also `true`. Set this to `false` to disable this behavior. |
| **easing** | `{ enter?: string, exit?: string } \| string` | `'cubic-bezier(0.4, 0, 0.2, 1)'` | The transition timing function. You may specify a single easing or a object containing enter and exit values. |
| **in** | `bool` | `false` | If `true`, the component will transition in. |
| **timeout** | `number \| { appear?: number, enter?: number, exit?: number }` | `{ enter: 225, exit: 195 }` | The duration for the transition, in milliseconds. You may specify a single timeout for all transitions, or individually with an object. |
| **onEnter** | `func` | - | Callback fired before the "entering" status is applied. An extra parameter `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount. |
| **onEntering** | `func` | - | Callback fired after the "entering" status is applied. An extra parameter `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount. |
| **onEntered** | `func` | - | Callback fired after the "entered" status is applied. An extra parameter `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount. |
| **onExit** | `func` | - | Callback fired before the "exiting" status is applied. |
| **onExiting** | `func` | - | Callback fired after the "exiting" status is applied. |
| **onExited** | `func` | - | Callback fired after the "exited" status is applied. |
| **TransitionComponent** | `React.ComponentType` | `Transition` | The component used for the transition. Follows the same pattern as other transition components. |

The `ref` is forwarded to the root element.

### Inheritance

While not explicitly documented above, the props of the Transition component from react-transition-group are also available in Fade. You can take advantage of this to target nested components.
