# API Documentation

- [But<PERSON>](#button)

# Button

API reference docs for the React Button component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Button from '@hxnova/react-components/Button';
// or
import { Button } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<ButtonActions>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **className** | `string` | - |  |
| **color** | ``"primary" \| "error"`` | `'primary'` | The color scheme used by the Button |
| **disabled** | ``false \| true`` | `false` | If `true`, the component is disabled. |
| **endIcon** | `ReactNode` | - | An optional icon to show at the end of the button |
| **focusableWhenDisabled** | ``false \| true`` | `false` | If `true`, allows a disabled button to receive focus. |
| **fullWidth** | ``false \| true`` | `false` | If `true`, the button will take up the full width of its container. |
| **href** | `string` | - |  |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **ref** | ``((instance: HTMLButtonElement \| null) => void) \| RefObject<HTMLButtonElement> \| null`` | - |  |
| **rootElementName** | `keyof HTMLElementTagNameMap` | `'button'` | The HTML element that is ultimately rendered, for example 'button' or 'a' |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | The overall size of the button. |
| **startIcon** | `ReactNode` | - | An optional icon to show at the start of the button |
| **tabIndex** | `number` | - |  |
| **to** | `string` | - |  |
| **type** | ``"button" \| "submit" \| "reset"`` | `'button'` | Type attribute applied when the `component` is `button`. |
| **variant** | ``"text" \| "filled" \| "outlined"`` | `'filled'` | The style of button to use |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaButton-root | ``props.href \|\| props.to ? 'a' : 'button'`` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaButton-colorPrimary | `colorPrimary` | Styles applied to the Button if `color="primary"`. |
| .NovaButton-colorError | `colorError` | Styles applied to the Button if `color="error"`. |
| .NovaButton-filled | `filled` | Styles applied to the Button if `variant="filled"`. |
| .NovaButton-outlined | `outlined` | Styles applied to the Button if `variant="outlined"`. |
| .NovaButton-text | `text` | Styles applied to the Button if `variant="text"`. |

