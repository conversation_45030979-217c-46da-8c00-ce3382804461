import type { Meta, StoryFn } from '@storybook/react';

import { Button as NovaButton, ButtonProps } from '@hxnova/react-components/Button';
import Icon from '@hxnova/icons/Icon';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: '@hxnova/react-components/Button',
  component: NovaButton,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['!autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // argTypes: {
  //   backgroundColor: { control: 'color' },
  // },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  // args: { onClick: fn() },
} satisfies Meta<typeof NovaButton>;

export default meta;

const IconSize = {
  small: 20,
  medium: 24,
  large: 28,
};

const ButtonTemplate: StoryFn<
  (
    props: Omit<ButtonProps, 'color' | 'startIcon' | 'endIcon'> & {
      showStartIcon?: boolean;
      showEndIcon?: boolean;
      clickable?: boolean;
      colorX?: ButtonProps['color'];
    },
  ) => JSX.Element
> = ({ showStartIcon, showEndIcon, colorX, clickable, size, ...other }) => {
  return (
    <NovaButton
      {...other}
      size={size}
      color={colorX}
      onClick={clickable ? () => {} : undefined}
      endIcon={showEndIcon ? <Icon family="material" name={'download'} size={IconSize[size || 'medium']} /> : undefined}
      startIcon={
        showStartIcon ? <Icon family="material" name={'arrow_back'} size={IconSize[size || 'medium']} /> : undefined
      }
    />
  );
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const UniversalStory = {
  render: ButtonTemplate,
  args: {
    variant: 'filled',
    colorX: 'primary',
    size: 'medium',
    children: 'Button',
    disabled: false,
    showStartIcon: true,
    showEndIcon: false,
    clickable: true,
  },
  argTypes: {
    colorX: {
      control: { type: 'radio' },
      options: ['primary', 'error'],
    },
  },
  parameters: {
    controls: {
      include: ['variant', 'colorX', 'size', 'disabled', 'showStartIcon', 'showEndIcon', 'clickable'],
    },
  },
};
