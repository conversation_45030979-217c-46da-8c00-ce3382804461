import { Button } from '@hxnova/react-components/Button';
import Icon from '@hxnova/icons/Icon';

export default function IconsDemo() {
  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <Button startIcon={<Icon family="material" name={'arrow_back'} size={24} />}>Go back</Button>
      <Button endIcon={<Icon family="material" name={'download'} size={24} />}>Go forward</Button>
    </div>
  );
}
