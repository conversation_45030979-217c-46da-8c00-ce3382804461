import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';  
import * as ButtonStories from './Button.stories';
import { Button } from '@hxnova/react-components/Button';
import VariantExample from './Examples/VariantExample';
import VariantExampleSource from './Examples/VariantExample.tsx?raw';
import ColorExample from './Examples/ColorExample';
import ColorExampleSource from './Examples/ColorExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import IconsExample from './Examples/IconsExample';
import IconsExampleSource from './Examples/IconsExample.tsx?raw';

 <Meta title="@hxnova/react-components/Button/Examples" />
 
## Button Variants

Nova buttons come in three style variations, which can be configured using the `variant` prop. The default variant is 'filled'.

<div className="sb-unstyled">
    <VariantExample />
</div>
<CodeExpand code={VariantExampleSource} showBorderTop style={{marginTop: 16}}/>

## Button Colors

Nova buttons come in a variety of colors as well. These can be adjusted using the `color` prop. The default color is 'primary'. 

<div className="sb-unstyled">
  <ColorExample />
</div>
<CodeExpand code={ColorExampleSource} showBorderTop style={{marginTop: 16}}/>

## Button Sizes

Buttons come in three sizes: small, medium (default), and large. These can be adjusted using the `size` prop.

<div className="sb-unstyled">
  <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Button Icons

You can configure your button to show an icon on the left or right side of the button text. These can be configured with the `startIcon` or `endIcon` props.

<div className="sb-unstyled">
  <IconsExample />
</div>
<CodeExpand code={IconsExampleSource} showBorderTop style={{marginTop: 16}}/>
