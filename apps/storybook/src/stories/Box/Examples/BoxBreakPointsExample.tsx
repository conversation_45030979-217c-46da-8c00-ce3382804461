import React from 'react';
import { Box } from '@hxnova/react-components/Box';

export default function BoxExample() {
  return (
    <Box
      sx={(theme) => ({
        width: '200px',
        height: '200px',
        [theme.breakpoints.down('md')]: {
          backgroundColor: theme.vars.palette.error,
        },
        [theme.breakpoints.up('md')]: {
          backgroundColor: theme.vars.palette.primary,
        },
      })}
    />
  );
}
