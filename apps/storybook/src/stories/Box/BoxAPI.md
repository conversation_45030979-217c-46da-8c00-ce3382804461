# API Documentation

- [Box](#box)

# Box

API reference docs for the React Box component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Box from '@hxnova/react-components/Box';
// or
import { Box } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaBox-root | `root` | Class name applied to the root element. |

