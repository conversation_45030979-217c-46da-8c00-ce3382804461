import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BoxBasicExample from './Examples/BoxBasicExample';
import BoxBasicExampleSource from './Examples/BoxBasicExample.tsx?raw';
import BoxBreakPointsExample from './Examples/BoxBreakPointsExample';
import BoxBreakPointsExampleSource from './Examples/BoxBreakPointsExample.tsx?raw';

<Meta title="@hxnova/react-components/Layout/Box/Examples" />

## Basic Box

The Box component is primarily a `div`, but it allows for customization to render any valid HTML tag or React component via the `component` prop. The demo illustrates how to replace the default `div` with a `section` element, showcasing the flexibility of the `Box` component.

<div className="sb-unstyled">
  <BoxBasicExample />
</div>
<CodeExpand code={ BoxBasicExampleSource } showBorderTop style={{marginTop: 16}}/>

## Box BreakPoints

This demo demonstrates the responsive capabilities of the `Box` component using breakpoints. It changes the box background color based on the different screen sizes.

<div className="sb-unstyled">
  <BoxBreakPointsExample />
</div>
<CodeExpand code={ BoxBreakPointsExampleSource } showBorderTop style={{marginTop: 16}}/>

