import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { DrawerRootProps, Drawer } from '@hxnova/react-components/Drawer';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Typography } from '@hxnova/react-components/Typography';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { List } from '@hxnova/react-components/List';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Button } from '@hxnova/react-components/Button';
import { Divider } from '@hxnova/react-components';
import NexusCoreIcon from '@nexusui/branding/NexusCore';
import Icon from '@hxnova/icons/Icon';

export default {
  title: '@hxnova/react-components/Drawer',
  component: Drawer.Root,
  parameters: {
    layout: 'fullscreen',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1273-8486&p=f&t=GJkTjGS5iCl1uASK-0',
    },
  },
  tags: ['!autodocs'],
} as Meta<DrawerRootProps>;

interface ContentProps {
  variant?: 'temporary' | 'persistent' | 'permanent';
  expanded?: boolean;
  anchor: 'top' | 'bottom' | 'left' | 'right';
  width?: number | string;
  railWidth?: number | string;
  open: boolean;
  toggleDrawer: () => void;
}
const Content = ({ variant, expanded, anchor, width, railWidth = 80, open, toggleDrawer }: ContentProps) => {
  const margin = expanded ? width : railWidth;
  const hasMargin = (open || !expanded || variant === 'permanent') && variant !== 'temporary';

  return (
    <div
      sx={[
        {
          gap: '1rem',
          flexGrow: 1,
          padding: '32px',
          transitionProperty: 'margin',
          transitionDuration: '225ms',
          transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
        },
        anchor === 'top' && hasMargin ? { marginTop: margin } : {},
        anchor === 'bottom' && hasMargin ? { marginBottom: margin } : {},
        anchor === 'left' && hasMargin ? { marginLeft: margin } : {},
        anchor === 'right' && hasMargin ? { marginRight: margin } : {},
      ]}
    >
      {variant !== 'permanent' && <Button onClick={toggleDrawer}>{open ? 'CLOSE' : 'OPEN'}</Button>}
      <Typography variant="bodySmall">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
        magna aliqua. Rhoncus dolor purus non enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
        imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus. Convallis convallis tellus id interdum
        velit laoreet id donec ultrices. Odio morbi quis commodo odio aenean sed adipiscing. Amet nisl suscipit
        adipiscing bibendum est ultricies integer quis. Cursus euismod quis viverra nibh cras. Metus vulputate eu
        scelerisque felis imperdiet proin fermentum leo. Mauris commodo quis imperdiet massa tincidunt. Cras tincidunt
        lobortis feugiat vivamus at augue. At augue eget arcu dictum varius duis at consectetur lorem. Velit sed
        ullamcorper morbi tincidunt. Lorem donec massa sapien faucibus et molestie ac.
      </Typography>
    </div>
  );
};

const Template: StoryFn<(props: DrawerRootProps) => JSX.Element> = ({ width, variant, anchor, ...args }) => {
  const [open, setOpen] = React.useState(false);
  const toggleDrawer = () => setOpen(!open);
  const handleClose = () => setOpen(false);

  const [activeItem, setActiveItem] = React.useState('1');
  const handleItemSelect = (itemId: string) => setActiveItem(itemId);

  return (
    <div sx={{ display: 'flex', flexDirection: anchor === 'top' || anchor === 'bottom' ? 'column' : 'row' }}>
      {(anchor === 'right' || anchor === 'bottom') && (
        <Content expanded variant={variant} width={width} open={open} anchor={anchor} toggleDrawer={toggleDrawer} />
      )}
      <Drawer.Root
        {...args}
        variant={variant}
        onClose={handleClose}
        open={open}
        width={width}
        anchor={anchor}
        activeItem={activeItem}
        onItemSelect={handleItemSelect}
      >
        <Drawer.Body>
          <Drawer.NavGroup>
            <Drawer.NavItem itemId="1" label="Label" startDecorator={<Icon family="material" name="home" />} />
            <Drawer.NavItem itemId="2" label="Label" startDecorator={<Icon family="material" name="inbox" />} />
            <Drawer.NavItem itemId="3" label="Label" startDecorator={<Icon family="material" name="group" />} />
          </Drawer.NavGroup>
        </Drawer.Body>
      </Drawer.Root>
      {(anchor === 'left' || anchor === 'top') && (
        <Content expanded variant={variant} width={width} open={open} anchor={anchor} toggleDrawer={toggleDrawer} />
      )}
    </div>
  );
};

const BasicNavDrawerTemplate: StoryFn<(props: DrawerRootProps) => JSX.Element> = ({
  width,
  variant,
  anchor,
  ...args
}) => {
  const [open, setOpen] = React.useState(true);
  const toggleDrawer = () => setOpen(!open);
  const handleClose = () => setOpen(false);

  const [activeItem, setActiveItem] = React.useState('3-1');
  const handleItemSelect = (itemId: string) => setActiveItem(itemId);

  const [railWidth, setRailWidth] = React.useState(80);

  const handleToggleSubNavDrawer = (isOpen: boolean) => {
    setRailWidth(isOpen ? 357 : 80);
  };

  return (
    <div sx={{ display: 'flex', flexDirection: anchor === 'top' || anchor === 'bottom' ? 'column' : 'row' }}>
      {(anchor === 'right' || anchor === 'bottom') && (
        <Content
          variant={variant}
          width={width}
          railWidth={railWidth}
          open={open}
          expanded={args.expanded}
          anchor={anchor}
          toggleDrawer={toggleDrawer}
        />
      )}
      <Drawer.Root
        {...args}
        variant={variant}
        onClose={handleClose}
        open={open}
        width={width}
        anchor={anchor}
        activeItem={activeItem}
        onItemSelect={handleItemSelect}
        onToggleSubNavDrawer={handleToggleSubNavDrawer}
      >
        <Drawer.Header
          productLogo={<NexusCoreIcon height={40} width={40} />}
          pageTitle={'Product name'}
          endDecorator={
            <IconButton
              sx={(theme) => ({
                color: theme.vars.palette.onSurfaceVariant,
              })}
              variant={'standard'}
              color={'primary'}
            >
              {args.expanded ? (
                <Icon family="material" name="left_panel_close" />
              ) : (
                <Icon family="material" name="left_panel_open" />
              )}
            </IconButton>
          }
        />
        <Drawer.Body>
          <Drawer.NavGroup>
            <Drawer.NavItem itemId="1-1" label="Dashboard" startDecorator={<Icon family="material" name="home" />} />
            <Drawer.NavItem itemId="2-1" label="Calendar" startDecorator={<Icon family="material" name="inbox" />} />
            <Drawer.NavItem
              itemId="3-1"
              label="Label"
              startDecorator={<Icon family="material" name="group" />}
              trailingLabel={'100+'}
            />
            <Drawer.NavItem
              itemId="4-1"
              label="Label"
              startDecorator={<Icon family="material" name="outbox" />}
              trailingBadge={{
                badgeContent: '3',
                color: 'error',
              }}
            />
          </Drawer.NavGroup>
          <Divider />
          <Drawer.NavGroup>
            <Drawer.NavItem itemId="5-1" label="Label" startDecorator={<Icon family="material" name="folder" />} />
          </Drawer.NavGroup>
        </Drawer.Body>
        <Drawer.Footer>
          <Divider />
          <Drawer.NavGroup>
            <Drawer.NavItem
              itemId="6-1"
              label="Label"
              startDecorator={<Icon family="material" name="folder" />}
              trailingLabel={'100+'}
            />
            <Drawer.NavItem
              itemId="6-2"
              label="Label"
              startDecorator={<Icon family="material" name="folder" />}
              trailingLabel={'100+'}
            />
          </Drawer.NavGroup>
          <List sx={{ height: '72px', justifyContent: 'center' }}>
            <ListItem sx={{ paddingInline: '0.75rem' }}>
              <ListItemButton>
                <ListItemDecorator>
                  <Avatar color={'error'}>AA</Avatar>
                </ListItemDecorator>
                {args.expanded && (
                  <>
                    <ListItemContent primary="John Doe" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </>
                )}
              </ListItemButton>
            </ListItem>
          </List>
        </Drawer.Footer>
      </Drawer.Root>
      {(anchor === 'left' || anchor === 'top') && (
        <Content
          variant={variant}
          width={width}
          railWidth={railWidth}
          open={open}
          expanded={args.expanded}
          anchor={anchor}
          toggleDrawer={toggleDrawer}
        />
      )}
    </div>
  );
};

const SecondLevelNavDrawerTemplate: StoryFn<(props: DrawerRootProps) => JSX.Element> = ({
  width,
  variant,
  anchor,
  ...args
}) => {
  const [open, setOpen] = React.useState(true);
  const toggleDrawer = () => setOpen(!open);
  const handleClose = () => setOpen(false);

  const [activeItem, setActiveItem] = React.useState('3-1');
  const handleItemSelect = (itemId: string) => setActiveItem(itemId);

  const [railWidth, setRailWidth] = React.useState(80);

  const handleToggleSubNavDrawer = (isOpen: boolean) => {
    setRailWidth(isOpen ? 357 : 80);
  };

  return (
    <div sx={{ display: 'flex', flexDirection: anchor === 'top' || anchor === 'bottom' ? 'column' : 'row' }}>
      {(anchor === 'right' || anchor === 'bottom') && (
        <Content
          variant={variant}
          width={width}
          railWidth={railWidth}
          open={open}
          expanded={false}
          anchor={anchor}
          toggleDrawer={toggleDrawer}
        />
      )}
      <Drawer.Root
        {...args}
        variant={variant}
        onClose={handleClose}
        open={open}
        width={width}
        anchor={anchor}
        activeItem={activeItem}
        onItemSelect={handleItemSelect}
        onToggleSubNavDrawer={handleToggleSubNavDrawer}
      >
        <Drawer.Header productLogo={<NexusCoreIcon height={40} width={40} />} pageTitle={'Product name'} />
        <Drawer.Body>
          <Drawer.NavGroup>
            <Drawer.NavItem label="Dashboard" startDecorator={<Icon family="material" name="home" />}>
              <Drawer.NavItem itemId="1-1" label="Label1" startDecorator={<Icon family="material" name="star" />} />
              <Drawer.NavItem label="Label2" startDecorator={<Icon family="material" name="star" />}>
                <Drawer.NavItem itemId="1-2" label="Label3" startDecorator={<Icon family="material" name="star" />} />
                <Drawer.NavItem itemId="1-3" label="Label4" startDecorator={<Icon family="material" name="star" />} />
              </Drawer.NavItem>
            </Drawer.NavItem>
            <Drawer.NavItem label="Calendar" startDecorator={<Icon family="material" name="inbox" />}>
              <Drawer.NavItem itemId="2-1" label="Label5" startDecorator={<Icon family="material" name="star" />} />
              <Drawer.NavItem itemId="2-2" label="Label6" startDecorator={<Icon family="material" name="star" />} />
            </Drawer.NavItem>
            <Drawer.NavItem
              itemId="3-1"
              label="Label"
              startDecorator={<Icon family="material" name="group" />}
              trailingLabel={'100+'}
            ></Drawer.NavItem>
            <Drawer.NavItem
              itemId="4-1"
              label="Label"
              startDecorator={<Icon family="material" name="outbox" />}
              trailingBadge={{
                badgeContent: '3',
                color: 'error',
              }}
            ></Drawer.NavItem>
          </Drawer.NavGroup>
          <Divider />
          <Drawer.NavGroup>
            <Drawer.NavItem label="Label" startDecorator={<Icon family="material" name="folder" />}>
              <Drawer.NavItem itemId="5-1" label="Label7" startDecorator={<Icon family="material" name="star" />} />
              <Drawer.NavItem itemId="5-2" label="Label8" startDecorator={<Icon family="material" name="star" />} />
            </Drawer.NavItem>
          </Drawer.NavGroup>
        </Drawer.Body>
        <Drawer.Footer>
          <Divider />
          <Drawer.NavGroup>
            <Drawer.NavItem
              itemId="6-1"
              label="Label"
              startDecorator={<Icon family="material" name="folder" />}
              trailingLabel={'100+'}
            />
            <Drawer.NavItem
              itemId="6-2"
              label="Label"
              startDecorator={<Icon family="material" name="folder" />}
              trailingLabel={'100+'}
            />
          </Drawer.NavGroup>
          <List sx={{ height: '72px', justifyContent: 'center' }}>
            <ListItem sx={{ paddingInline: '0.75rem' }}>
              <ListItemButton>
                <ListItemDecorator>
                  <Avatar color={'error'}>AA</Avatar>
                </ListItemDecorator>
                {args.expanded && (
                  <>
                    <ListItemContent primary="John Doe" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </>
                )}
              </ListItemButton>
            </ListItem>
          </List>
        </Drawer.Footer>
      </Drawer.Root>
      {(anchor === 'left' || anchor === 'top') && (
        <Content
          variant={variant}
          width={width}
          railWidth={railWidth}
          open={open}
          expanded={args.expanded}
          anchor={anchor}
          toggleDrawer={toggleDrawer}
        />
      )}
    </div>
  );
};

export const BasicDrawer = {
  render: Template,
  args: {
    width: 360,
    anchor: 'left',
    variant: 'temporary',
  },
  argTypes: {
    anchor: {
      control: { type: 'radio' },
      options: ['left', 'right', 'top', 'bottom'],
    },
    variant: {
      control: { type: 'radio' },
      options: ['temporary', 'persistent', 'permanent'],
    },
  },
};

export const BasicNavDrawer = {
  render: BasicNavDrawerTemplate,
  args: {
    width: 360,
    anchor: 'left',
    expanded: true,
    variant: 'permanent',
    showRailLabel: true,
    showSubNavDrawerHeader: true,
  },
  argTypes: {
    anchor: {
      control: { type: 'radio' },
      options: ['left', 'right'],
    },
    expanded: {
      control: { type: 'boolean' },
    },
    variant: {
      control: { type: 'radio' },
      options: ['temporary', 'persistent', 'permanent'],
    },

    showRailLabel: {
      control: { type: 'boolean' },
    },
    showSubNavDrawerHeader: {
      control: { type: 'boolean' },
    },
  },
  parameters: {
    controls: {
      include: ['width', 'anchor', 'variant', 'expanded', 'showRailLabel'],
    },
  },
};

export const SecondLevelNavDrawer = {
  render: SecondLevelNavDrawerTemplate,
  args: {
    ...BasicNavDrawer.args,
    expanded: false,
  },
  argTypes: {
    ...BasicNavDrawer.argTypes,
  },
  parameters: {
    controls: {
      include: ['width', 'anchor', 'showRailLabel', 'showSubNavDrawerHeader'],
    },
  },
};
