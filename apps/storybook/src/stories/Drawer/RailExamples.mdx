import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import * as DrawerStories from './Drawer.stories';

import BasicCollapsedExample from './Examples/Collapsed/BasicCollapsedExample';
import BasicCollapsedExampleSource from './Examples/Collapsed/BasicCollapsedExampleSource.tsx?raw';
import SecondLevelCollapsedExample from './Examples/Collapsed/SecondLevelCollapsedExample';
import SecondLevelCollapsedExampleSource from './Examples/Collapsed/SecondLevelCollapsedExampleSource.tsx?raw';


<Meta title="@hxnova/react-components/Drawer/RailExamples" />
 
## Navigation Rail

All three variants of the Drawer provide a condensed version (rail) that can be toggled using the `expanded` prop. When `expanded={false}`, the Drawer is narrower and the extra adornments are hidden.

Nova Drawer supports two navigation patterns: a basic rail-to-drawer pattern (recommended for most cases) and a second level navigation pattern for complex hierarchical needs - these patterns should not be mixed in the same application.

### Basic rail-to-drawer pattern
A standard navigation pattern where rail expands to full drawer.

<div className="sb-unstyled">
    <BasicCollapsedExample />
</div>
<CodeExpand code={BasicCollapsedExampleSource} showBorderTop style={{marginTop: 16}}/>

### Second level navigation pattern
For applications requiring complex hierarchical navigation structure.

<div className="sb-unstyled">
    <SecondLevelCollapsedExample />
</div>
<CodeExpand code={SecondLevelCollapsedExampleSource} showBorderTop style={{marginTop: 16}}/>
