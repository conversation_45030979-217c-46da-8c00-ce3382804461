import React from 'react';
import { Drawer } from '@hxnova/react-components/Drawer';
import Icon from '@hxnova/icons/Icon';

export default function Demo() {
  return (
    <div
      sx={{
        minHeight: 200,
        position: 'relative',
        border: '1px solid rgba(0,0,0,0.35)',
      }}
    >
      <Drawer.Root
        variant={'permanent'}
        slotProps={{
          container: {
            style: {
              position: 'absolute',
            },
          },
        }}
        expanded
      >
        <Drawer.Body>
          <Drawer.NavGroup>
            <Drawer.NavItem label="Label" startDecorator={<Icon family="material" name="group" />}>
              <Drawer.NavItem itemId="3" label="Label" startDecorator={<Icon family="material" name="star" />} />
              <Drawer.NavItem itemId="4" label="Label" startDecorator={<Icon family="material" name="star" />} />
            </Drawer.NavItem>
          </Drawer.NavGroup>
        </Drawer.Body>
      </Drawer.Root>
    </div>
  );
}
