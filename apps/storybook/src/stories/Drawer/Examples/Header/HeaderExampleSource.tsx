import React from 'react';
import { Drawer } from '@hxnova/react-components/Drawer';
import { IconButton } from '@hxnova/react-components/IconButton';
import Icon from '@hxnova/icons/Icon';
import NexusCoreIcon from '@nexusui/branding/NexusCore';

export default function Demo() {
  return (
    <Drawer.Header
      productLogo={<NexusCoreIcon height={40} width={40} />}
      pageTitle={'Product name'}
      endDecorator={
        <IconButton
          style={{
            color: 'var(--palette-onSurfaceVariant)',
          }}
          variant={'standard'}
          color={'primary'}
        >
          {<Icon family="material" name="menu_open" size={24} onClick={() => {}} />}
        </IconButton>
      }
    />
  );
}
