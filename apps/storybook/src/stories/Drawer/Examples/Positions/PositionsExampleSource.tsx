import React from 'react';

import { Drawer } from '@hxnova/react-components/Drawer';
import { Button } from '@hxnova/react-components/Button';

export default function Demo() {
  const [openLeft, setOpenLeft] = React.useState(false);
  const [openRight, setOpenRight] = React.useState(false);
  const [openTop, setOpenTop] = React.useState(false);
  const [openBottom, setOpenBottom] = React.useState(false);

  return (
    <div
      sx={{
        display: 'flex',
        flexDirection: 'row',
      }}
    >
      <Button
        style={{ margin: 16 }}
        onClick={() => {
          setOpenLeft(true);
        }}
      >
        Open Left Drawer
      </Button>
      <Button
        style={{ margin: 16 }}
        onClick={() => {
          setOpenRight(true);
        }}
      >
        Open Right Drawer
      </Button>
      <Button
        style={{ margin: 16 }}
        onClick={() => {
          setOpenTop(true);
        }}
      >
        Open Top Drawer
      </Button>
      <Button
        style={{ margin: 16 }}
        onClick={() => {
          setOpenBottom(true);
        }}
      >
        Open Bottom Drawer
      </Button>
      <Drawer.Root anchor={'left'} open={openLeft} onClose={() => setOpenLeft(false)}>
        <span>Drawer Contents</span>
      </Drawer.Root>
      <Drawer.Root anchor={'right'} open={openRight} onClose={() => setOpenRight(false)}>
        <span>Drawer Contents</span>
      </Drawer.Root>
      <Drawer.Root anchor={'top'} open={openTop} onClose={() => setOpenTop(false)} width={100}>
        <span>Drawer Contents</span>
      </Drawer.Root>
      <Drawer.Root anchor={'bottom'} open={openBottom} onClose={() => setOpenBottom(false)} width={100}>
        <span>Drawer Contents</span>
      </Drawer.Root>
    </div>
  );
}
