import React from 'react';

import { Drawer } from '@hxnova/react-components/Drawer';
import { Button } from '@hxnova/react-components/Button';

export default function Demo() {
  const [openPersistent, setOpenPersistent] = React.useState(false);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
      }}
    >
      <Drawer.Root variant={'persistent'} open={openPersistent}>
        <span>Persistent Drawer</span>
      </Drawer.Root>
      <div style={{ marginLeft: openPersistent ? 360 : 0, transition: 'margin-left 225ms ease-in-out' }}>
        <Button style={{ margin: 16 }} onClick={() => setOpenPersistent((p) => !p)}>
          Toggle Persistent Drawer
        </Button>
      </div>
    </div>
  );
}
