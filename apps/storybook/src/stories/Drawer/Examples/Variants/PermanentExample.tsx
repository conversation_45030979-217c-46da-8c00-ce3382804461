import React from 'react';
import { Drawer } from '@hxnova/react-components/Drawer';

export default function Demo() {
  return (
    <div
      sx={{
        minHeight: 150,
        display: 'flex',
        flexDirection: 'row',
        position: 'relative',
        border: '1px solid rgba(0,0,0,0.35)',
      }}
    >
      <Drawer.Root
        width={360}
        variant={'permanent'}
        sx={{
          width: 360,
          '& > .NovaDrawer-container': { position: 'absolute' },
        }}
      >
        <span>Permanent Drawer</span>
      </Drawer.Root>
      <div sx={{ padding: 16 }}>
        <span>Application Content</span>
      </div>
    </div>
  );
}
