import React from 'react';

import { Drawer } from '@hxnova/react-components/Drawer';
import { Button } from '@hxnova/react-components/Button';

export default function Demo() {
  const [openPersistent, setOpenPersistent] = React.useState(false);

  return (
    <div
      sx={{
        minHeight: 150,
        display: 'flex',
        flexDirection: 'row',
        position: 'relative',
        border: '1px solid rgba(0,0,0,0.35)',
        overflow: 'hidden',
      }}
    >
      <Drawer.Root
        variant={'persistent'}
        open={openPersistent}
        sx={{
          '& > .NovaDrawer-container': { position: 'absolute' },
        }}
      >
        <span>Persistent Drawer</span>
      </Drawer.Root>
      <div sx={{ marginLeft: openPersistent ? 360 : 0, transition: 'margin-left 225ms ease-in-out' }}>
        <Button sx={{ margin: 16 }} onClick={() => setOpenPersistent((p) => !p)}>
          Toggle Persistent Drawer
        </Button>
      </div>
    </div>
  );
}
