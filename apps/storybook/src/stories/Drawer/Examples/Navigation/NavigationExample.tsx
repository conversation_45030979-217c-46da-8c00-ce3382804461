import React from 'react';

import { Drawer } from '@hxnova/react-components/Drawer';

import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Typography } from '@hxnova/react-components/Typography';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { List } from '@hxnova/react-components/List';
import Icon from '@hxnova/icons/Icon';

import avatarSrc from '../../../../assets/avatar.jpeg';

export default function Demo() {
  return (
    <div
      sx={{
        minHeight: 400,
        display: 'flex',
        flexDirection: 'row',
        position: 'relative',
        border: '1px solid rgba(0,0,0,0.35)',
        overflow: 'hidden',
      }}
    >
      <Drawer.Root variant={'permanent'} open sx={{ '& > .NovaDrawer-container': { position: 'absolute' } }}>
        <Drawer.Header pageTitle={'Product name'} />
        <Drawer.Body>
          <Drawer.NavGroup
            sx={(theme) => ({
              borderBottom: `1px solid ${theme.vars.palette.outlineVariant}`,
            })}
          >
            <Drawer.NavItem label="Item 1" />
            <Drawer.NavItem label="Item 2" />
            <Drawer.NavItem label="Item 3" />
          </Drawer.NavGroup>
        </Drawer.Body>
        <Drawer.Footer>
          <List>
            <ListItem sx={{ paddingInline: '0.75rem' }}>
              <ListItemDecorator>
                <Avatar src={avatarSrc} color={'error'}></Avatar>
              </ListItemDecorator>
              <>
                <ListItemContent primary="Headline" secondary="Supporting text" />
                <ListItemDecorator>
                  <Typography variant="bodySmall">100+</Typography>
                  <Icon family="material" name="keyboard_arrow_right" size={24} />
                </ListItemDecorator>
              </>
            </ListItem>
          </List>
        </Drawer.Footer>
      </Drawer.Root>
    </div>
  );
}
