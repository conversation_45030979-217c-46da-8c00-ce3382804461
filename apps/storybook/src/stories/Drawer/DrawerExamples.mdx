import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import * as DrawerStories from './Drawer.stories';
import TemporaryExample from './Examples/Variants/TemporaryExample';
import TemporaryExampleSource from './Examples/Variants/TemporaryExampleSource.tsx?raw';
import PersistentExample from './Examples/Variants/PersistentExample';
import PersistentExampleSource from './Examples/Variants/PersistentExampleSource.tsx?raw';
import PermanentExample from './Examples/Variants/PermanentExample';
import PermanentExampleSource from './Examples/Variants/PermanentExampleSource.tsx?raw';

import PositionsExample from './Examples/Positions/PositionsExample';
import PositionsExampleSource from './Examples/Positions/PositionsExampleSource.tsx?raw';
import NavigationExample from './Examples/Navigation/NavigationExample';
import NavigationExampleSource from './Examples/Navigation/NavigationExampleSource.tsx?raw';
import HeaderExample from './Examples/Header/HeaderExample';
import HeaderExampleSource from './Examples/Header/HeaderExampleSource.tsx?raw';
import NavGroupExample from './Examples/NavGroup/NavGroupExample';
import NavGroupExampleSource from './Examples/NavGroup/NavGroupExampleSource.tsx?raw';

import NavItemBasicExample from './Examples/NavItem/BasicExample';
import NavItemBasicExampleSource from './Examples/NavItem/BasicExampleSource.tsx?raw';
import NavItemIconExample from './Examples/NavItem/IconExample';
import NavItemIconExampleSource from './Examples/NavItem/IconExampleSource.tsx?raw';
import NavItemDecoratorsExample from './Examples/NavItem/DecoratorsExample';
import NavItemDecoratorsExampleSource from './Examples/NavItem/DecoratorsExampleSource.tsx?raw';
import NavItemSelectedExample from './Examples/NavItem/SelectedExample';
import NavItemSelectedExampleSource from './Examples/NavItem/SelectedExampleSource.tsx?raw';
import NavItemNestedExample from './Examples/NavItem/NestedExample';
import NavItemNestedExampleSource from './Examples/NavItem/NestedExampleSource.tsx?raw';

import BasicCollapsedExample from './Examples/Collapsed/BasicCollapsedExample';
import BasicCollapsedExampleSource from './Examples/Collapsed/BasicCollapsedExampleSource.tsx?raw';
import SecondLevelCollapsedExample from './Examples/Collapsed/SecondLevelCollapsedExample';
import SecondLevelCollapsedExampleSource from './Examples/Collapsed/SecondLevelCollapsedExampleSource.tsx?raw';


<Meta title="@hxnova/react-components/Drawer/Examples" />
 
## Drawer Variants

The Drawer component is a container that slides in from the side of the screen. It comes in three variations that can be adjusted with the `variant` prop: temporary (default), persistent, and permanent.

- **temporary**: when the Drawer is open/visible, it appears on top of your application content. A background overlay appears behind the drawer and blocks interaction with the rest of the application.
- **persistent**: when the Drawer is open/visible, it pushes the application content to the side.
- **permanent**: the Drawer is always open/visible and appears beside other application content.

<div className="sb-unstyled">
    <TemporaryExample />
</div>
<CodeExpand code={TemporaryExampleSource} showBorderTop style={{marginTop: 16}}/>

<div className="sb-unstyled">
    <PersistentExample />
</div>
<CodeExpand code={PersistentExampleSource} showBorderTop style={{marginTop: 16}}/>

<div className="sb-unstyled">
    <PermanentExample />
</div>
<CodeExpand code={PermanentExampleSource} showBorderTop style={{marginTop: 16}}/>

## Drawer Positions

You can adjust the position of the Drawer with the `anchor` prop. The Drawer can be anchored to the top, bottom, left (default), or right of the screen.

<div className="sb-unstyled">
    <PositionsExample />
</div>
<CodeExpand code={PositionsExampleSource} showBorderTop style={{marginTop: 16}}/>


## Drawer Contents

By default, the Drawer component is a blank canvas and you can put any content you wish in it. If you intend to use the Drawer for navigation, Nova provides several utility components that can be used to populate the Drawer.

<div className="sb-unstyled">
    <NavigationExample />
</div>
<CodeExpand code={NavigationExampleSource} showBorderTop style={{marginTop: 16}}/>


### Drawer Header

The DrawerHeader appears at the top of the Drawer and can be used to display a `pageTitle` or `productLogo` as well as an action button (via `endDecorator`), typically used for toggling the expand/collapse behavior.

<div className="sb-unstyled">
    <HeaderExample />
</div>
<CodeExpand code={HeaderExampleSource} showBorderTop style={{marginTop: 16}}/>


### Drawer Nav Group

The DrawerNavGroup is a container for DrawerNavItems. It can be used to group related navigation items together. You can provide a custom header for the group using the `header` prop.

<div className="sb-unstyled">
    <NavGroupExample />
</div>
<CodeExpand code={NavGroupExampleSource} showBorderTop style={{marginTop: 16}}/>


### Drawer Nav Item

The DrawerNavItem represents a single navigation item in the Drawer. It can be used to navigate to a different page or section of the application. The  basic DrawerNavItem show a simple text `label`.

<div className="sb-unstyled">
    <NavItemBasicExample />
</div>
<CodeExpand code={NavItemBasicExampleSource} showBorderTop style={{marginTop: 16}}/>


You can add an icon before the label using the `startDecorator` prop.

<div className="sb-unstyled">
    <NavItemIconExample />
</div>
<CodeExpand code={NavItemIconExampleSource} showBorderTop style={{marginTop: 16}}/>


You can also add custom content after the label. Nova provides three dedicated sections for visual enhancement: `trailingLabel` (for a secondary text label), `trailingBadge` (to show a badge or notification indicator), and `trailingAction` (for a custom action button).
<div className="sb-unstyled">
    <NavItemDecoratorsExample />
</div>
<CodeExpand code={NavItemDecoratorsExampleSource} showBorderTop style={{marginTop: 16}}/>


DrawerNavItems can also display a selected state. This can be controlled at the Drawer level (by setting `activeItem` to the `itemId` of the DrawerNavItem you want to select) or directly on the DrawerNavItem using the `selected` prop.

<div className="sb-unstyled">
    <NavItemSelectedExample />
</div>
<CodeExpand code={NavItemSelectedExampleSource} showBorderTop style={{marginTop: 16}}/>


You can created a tree structure in your Drawer by nesting DrawerNavItems.

<div className="sb-unstyled">
    <NavItemNestedExample />
</div>
<CodeExpand code={NavItemNestedExampleSource} showBorderTop style={{marginTop: 16}}/>

## DrawerBody and DrawerFooter

The DrawerBody and DrawerFooter components are simple containers used for positioning content. Whatever you pass to them as children will be rendered in the appropriate place in the Drawer.

## Expanding / Collapsing the Drawer

All three variants of the Drawer provide a condensed version that can be toggled using the `expanded` prop. When `expanded={false}`, the Drawer is narrower and the extra adornments are hidden.

Nova Drawer supports two navigation patterns: a basic rail-to-drawer pattern (recommended for most cases) and a second level navigation pattern for complex hierarchical needs - these patterns should not be mixed in the same application.

### Basic rail-to-drawer pattern
A standard navigation pattern where rail expands to full drawer.

<div className="sb-unstyled">
    <BasicCollapsedExample />
</div>
<CodeExpand code={BasicCollapsedExampleSource} showBorderTop style={{marginTop: 16}}/>

### Second level navigation pattern
For applications requiring complex hierarchical navigation structure.

<div className="sb-unstyled">
    <SecondLevelCollapsedExample />
</div>
<CodeExpand code={SecondLevelCollapsedExampleSource} showBorderTop style={{marginTop: 16}}/>
