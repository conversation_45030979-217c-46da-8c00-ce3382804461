import * as React from 'react';
import { SegmentedButtonGroup } from '@hxnova/react-components/SegmentedButtonGroup';
import { SegmentedButton } from '@hxnova/react-components/SegmentedButton';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  const [value, setValue] = React.useState('1');
  const handleChange = (_event: React.ChangeEvent<unknown>, newValue: string) => {
    setValue(newValue);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <SegmentedButtonGroup exclusive value={value} onChange={handleChange}>
        <SegmentedButton value="1" startIcon={<Icon family="material" name="check" />} />
        <SegmentedButton value="2" startIcon={<Icon family="material" name="check" />} />
        <SegmentedButton value="3" startIcon={<Icon family="material" name="check" />} />
      </SegmentedButtonGroup>
    </div>
  );
}
