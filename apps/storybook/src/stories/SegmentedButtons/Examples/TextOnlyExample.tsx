import * as React from 'react';
import { SegmentedButtonGroup } from '@hxnova/react-components/SegmentedButtonGroup';
import { SegmentedButton } from '@hxnova/react-components/SegmentedButton';

export default function Demo() {
  const [value, setValue] = React.useState('1');
  const handleChange = (_event: React.ChangeEvent<unknown>, newValue: string) => {
    if (newValue) {
      setValue(newValue);
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <SegmentedButtonGroup exclusive value={value} onChange={handleChange}>
        <SegmentedButton value="1">Button 1</SegmentedButton>
        <SegmentedButton value="2">Button 2</SegmentedButton>
        <SegmentedButton value="3">Button 3</SegmentedButton>
      </SegmentedButtonGroup>
    </div>
  );
}
