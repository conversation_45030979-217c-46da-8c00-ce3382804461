import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import * as SegmentedButtonsStories from './SegmentedButtons.stories';
import { SegmentedButton } from '@hxnova/react-components/SegmentedButton';
import SingleSelectExample from './Examples/SingleSelectExample';
import SingleSelectExampleSource from './Examples/SingleSelectExample.tsx?raw';
import MultiSelectExample from './Examples/MultiSelectExample';
import MultiSelectExampleSource from './Examples/MultiSelectExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import TextWithIconsExample from './Examples/TextWithIconsExample';
import TextWithIconsExampleSource from './Examples/TextWithIconsExample.tsx?raw';
import IconsOnlyExample from './Examples/IconsOnlyExample';
import IconsOnlyExampleSource from './Examples/IconsOnlyExample.tsx?raw';
import TextOnlyExample from './Examples/TextOnlyExample';
import TextOnlyExampleSource from './Examples/TextOnlyExample.tsx?raw';
import VerticalExample from './Examples/VerticalExample';
import VerticalExampleSource from './Examples/VerticalExample.tsx?raw';

<Meta title="@hxnova/react-components/Segmented Buttons/Examples" />

## Single-select mode

In single-select mode, selecting one option automatically deselects and previously selected option.
The behavior is enabled by setting the `exclusive` prop to `true`.

<div className="sb-unstyled">
    <SingleSelectExample />
</div>
<CodeExpand code={SingleSelectExampleSource} showBorderTop style={{marginTop: 16}}/>

## Multi-select mode

In multi-select mode, multiple options can be selected simultaneously. This is the default behavior when the `exclusive` prop is not set or set to false.

<div className="sb-unstyled">
    <MultiSelectExample />
</div>
<CodeExpand code={MultiSelectExampleSource} showBorderTop style={{marginTop: 16}}/>

## SegmentedButton Sizes

Segmented buttons come in three sizes: small, medium (default), and large. These can be adjusted using the size prop.

<div className="sb-unstyled">
    <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## SegmentedButton with Icons

You can configure your segmented button to show an icon on the left or right side of the segmented button text. These can be configured with the `startIcon` or `endIcon` props.

- Text and icons
<div className="sb-unstyled">
    <TextWithIconsExample />
</div>
<CodeExpand code={TextWithIconsExampleSource} showBorderTop style={{marginTop: 16}}/>

- Icons only
<div className="sb-unstyled">
    <IconsOnlyExample />
</div>
<CodeExpand code={IconsOnlyExampleSource} showBorderTop style={{marginTop: 16}}/>

- Text only

<div className="sb-unstyled">
    <TextOnlyExample />
</div>
<CodeExpand code={TextOnlyExampleSource} showBorderTop style={{marginTop: 16}}/>


## Vertical SegmentedButtons

To render segmented buttons vertically, set the `orientation` prop to `vertical` on the `<SegmentedButtonGroup>` component.

<div className="sb-unstyled">
    <VerticalExample />
</div>
<CodeExpand code={VerticalExampleSource} showBorderTop style={{marginTop: 16}}/>
