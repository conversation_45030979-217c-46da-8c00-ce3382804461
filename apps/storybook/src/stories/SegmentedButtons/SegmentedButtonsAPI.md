# API Documentation

- [SegmentedButton](#segmentedbutton)
- [SegmentedButtonGroup](#segmentedbuttongroup)

# SegmentedButton

API reference docs for the React SegmentedButton component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import SegmentedButton from '@hxnova/react-components/SegmentedButton';
// or
import { SegmentedButton } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<ButtonActions>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **className** | `string` | - |  |
| **disabled** | ``false \| true`` | `false` | If `true`, the component is disabled. |
| **endIcon** | `ReactNode` | - | An optional icon to show at the end of the button |
| **focusableWhenDisabled** | ``false \| true`` | `false` | If `true`, allows a disabled button to receive focus. |
| **fullWidth** | ``false \| true`` | `false` | If `true`, the button will take up the full width of its container. |
| **href** | `string` | - |  |
| **onChange** | ``(event: MouseEvent<HTMLElement, MouseEvent>, value: any) => void`` | - | Callback fired when the state changes.<br>@param event The event source of the callback.<br>@param value of the selected button. |
| **onClick** | ``(event: MouseEvent<HTMLElement, MouseEvent>, value: any) => void`` | - | Callback fired when the button is clicked.<br>@param event The event source of the callback.<br>@param value of the selected button. |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **ref** | ``((instance: HTMLButtonElement \| null) => void) \| RefObject<HTMLButtonElement> \| null`` | - |  |
| **rootElementName** | `keyof HTMLElementTagNameMap` | `'button'` | The HTML element that is ultimately rendered, for example 'button' or 'a' |
| **selected** | ``false \| true`` | `false` | If `true`, the button is rendered in an active state. |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | The overall size of the button. |
| **startIcon** | `ReactNode` | - | An optional icon to show at the start of the button |
| **tabIndex** | `number` | - |  |
| **to** | `string` | - |  |
| **type** | ``"button" \| "submit" \| "reset"`` | `'button'` | Type attribute applied when the `component` is `button`. |
| **value** | `any` | - | The value to associate with the button when selected in a SegmentedButtonGroup. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaSegmentedButton-root | `root` | Styles applied to the root element. |
| .NovaSegmentedButton-selected | `selected` | Styles applied to the SegmentedButton if `selected={true}`. |

<br><br>

# SegmentedButtonGroup

API reference docs for the React SegmentedButtonGroup component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import SegmentedButtonGroup from '@hxnova/react-components/SegmentedButtonGroup';
// or
import { SegmentedButtonGroup } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - |  |
| **disabled** | ``false \| true`` | `false` | If `true`, the component is disabled. This implies that all SegmentedButton children will be disabled. |
| **exclusive** | ``false \| true`` | `false` | If `true`, only allow one of the child SegmentedButton values to be selected. |
| **onChange** | ``(event: MouseEvent<HTMLElement, MouseEvent>, value: any) => void`` | - | Callback fired when the value changes.<br>@param event The event source of the callback.<br>@param value of the selected buttons. When `exclusive` is true<br>this is a single value; when false an array of selected values. If no value<br>is selected and `exclusive` is true the value is null; when false an empty array. |
| **orientation** | ``"horizontal" \| "vertical"`` | `'horizontal'` | The component orientation. |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | How large the SegmentedButtonGroup contents should be. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | `any` | - | The currently selected value within the group or an array of selected<br>values when `exclusive` is false.<br>The value must have reference equality with the option in order to be selected. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaSegmentedButtonGroup-root | `'div'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaSegmentedButtonGroup-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaSegmentedButtonGroup-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaSegmentedButtonGroup-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |
| .NovaSegmentedButtonGroup-horizontal | `horizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaSegmentedButtonGroup-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |
| .NovaSegmentedButtonGroup-grouped | `grouped` | Styles applied to the children. |
| .NovaSegmentedButtonGroup-firstButton | `firstButton` | Styles applied to the first button in the segmented button group. |
| .NovaSegmentedButtonGroup-lastButton | `lastButton` | Styles applied to the last button in the segmented button group. |
| .NovaSegmentedButtonGroup-middleButton | `middleButton` | Styles applied to buttons in the middle of the segmented button group. |

