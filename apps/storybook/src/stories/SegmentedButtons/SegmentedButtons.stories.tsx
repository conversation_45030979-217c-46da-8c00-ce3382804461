import { StoryFn, Meta } from '@storybook/react';
import { SegmentedButton } from '@hxnova/react-components/SegmentedButton';
import {
  SegmentedButtonGroup as NovaSegmentedButtonGroup,
  SegmentedButtonGroupProps,
} from '@hxnova/react-components/SegmentedButtonGroup';
import { Icon } from '@hxnova/icons';
import { useEffect, useState } from 'react';

const meta = {
  title: '@hxnova/react-components/Segmented Buttons',
  component: NovaSegmentedButtonGroup,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=808-10353&p=f&t=MIPhrdmof8yuaelW-0',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof NovaSegmentedButtonGroup>;

export default meta;

type GroupSegmentedButtonControls = { showStartIcon: boolean };

const SegmentedButtonsTemplate: StoryFn<
  (props: SegmentedButtonGroupProps & GroupSegmentedButtonControls) => JSX.Element
> = ({ showStartIcon, ...args }) => {
  const [value, setValue] = useState<'1' | '2' | '3' | Array<'1' | '2' | '3'>>(['1']);

  useEffect(() => {
    if (args.exclusive) {
      // check if value is an array
      if (Array.isArray(value)) {
        // if it is, set it to the first value
        setValue(value[0]);
      }
    } else {
      if (!Array.isArray(value)) {
        // if it is not, set it to an array with the first value
        setValue([value]);
      }
    }
  }, [args.exclusive]);
  return (
    <div style={{ display: 'flex', flexDirection: 'row' }}>
      <NovaSegmentedButtonGroup
        {...args}
        value={value}
        onChange={(e, val) => {
          console.log(val);
          setValue(val);
        }}
      >
        <SegmentedButton value={'1'} startIcon={showStartIcon && <Icon family="material" name="check" />}>
          Enabled
        </SegmentedButton>
        <SegmentedButton value={'2'} startIcon={showStartIcon && <Icon family="material" name="check" />}>
          Enabled
        </SegmentedButton>
        <SegmentedButton value={'3'} startIcon={showStartIcon && <Icon family="material" name="check" />}>
          Enabled
        </SegmentedButton>
      </NovaSegmentedButtonGroup>
    </div>
  );
};

export const SegmentedButtons = {
  render: SegmentedButtonsTemplate,
  args: {
    exclusive: true,
    size: 'medium',
    disabled: false,
    orientation: 'horizontal',
    showStartIcon: true,
  },
  argTypes: {
    exclusive: {
      control: { type: 'boolean' },
    },
    size: {
      control: { type: 'radio' },
      options: ['small', 'medium', 'large'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
    orientation: {
      control: { type: 'radio' },
      options: ['horizontal', 'vertical'],
    },
  },
  parameters: {
    controls: {
      include: ['exclusive', 'size', 'disabled', 'orientation', 'showStartIcon'],
    },
  },
};
