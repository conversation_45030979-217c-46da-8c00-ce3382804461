# API Documentation

- [Container](#container)

# Container

API reference docs for the React Container component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Container from '@hxnova/react-components/Container';
// or
import { Container } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **disableGutters** | ``false | true`` | `false` | If `true`, the left and right padding is removed. |
| **fixed** | ``false | true`` | `false` | Set the max-width to match the min-width of the current breakpoint.<br>This is useful if you'd prefer to design for a fixed set of sizes<br>instead of trying to accommodate a fully fluid viewport.<br>It's fluid by default. |
| **maxWidth** | ``false | "xs" | "sm" | "md" | "lg" | "xl"`` | `'lg'` | Determine the max-width of the container.<br>The container width grows with the size of the screen.<br>Set to `false` to disable `maxWidth`. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaContainer-root | `root` | Class name applied to the root element. |
| .NovaContainer-maxWidthXs | `maxWidthXs` | Class name applied to the root element if `maxWidth = 'xs'`. |
| .NovaContainer-maxWidthSm | `maxWidthSm` | Class name applied to the root element if `maxWidth = 'sm'`. |
| .NovaContainer-maxWidthMd | `maxWidthMd` | Class name applied to the root element if `maxWidth = 'md'`. |
| .NovaContainer-maxWidthLg | `maxWidthLg` | Class name applied to the root element if `maxWidth = 'lg'`. |
| .NovaContainer-maxWidthXl | `maxWidthXl` | Class name applied to the root element if `maxWidth = 'xl'`. |
| .NovaContainer-fixed | `fixed` | Class name applied to the root element if `fixed = {true}`. |
| .NovaContainer-disableGutters | `disableGutters` | Class name applied to the root element if `disableGutters = {true}`. |

