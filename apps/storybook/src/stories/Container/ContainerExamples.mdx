import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import ContainerMaxWidthExample from './Examples/ContainerMaxWidthExample';
import ContainerMaxWidthExampleSource from './Examples/ContainerMaxWidthExample.tsx?raw';
import ContainerFixedExample from './Examples/ContainerFixedExample';
import ContainerFixedExampleSource from './Examples/ContainerFixedExample.tsx?raw';

<Meta title="@hxnova/react-components/Layout/Container/Examples" />

## Container with maxWith

The fluid container width is limited by the `maxWidth` prop value, ensuring that it adapts to the screen size while maintaining a maximum constraint.

<div className="sb-unstyled">
  <ContainerMaxWidthExample />
</div>
<CodeExpand code={ ContainerMaxWidthExampleSource } showBorderTop style={{marginTop: 16}}/>


## Container fixed

To design with specific sizes instead of a fully fluid layout, use the `fixed` prop. This sets the max-width to match the min-width of the current breakpoint, providing a structured and consistent layout across different devices.

<div className="sb-unstyled">
  <ContainerFixedExample />
</div>
<CodeExpand code={ ContainerFixedExampleSource } showBorderTop style={{marginTop: 16}}/>

