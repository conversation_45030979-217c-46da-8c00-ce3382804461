# API Documentation

- [Checkbox](#checkbox)

# Checkbox

API reference docs for the React Checkbox component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Checkbox from '@hxnova/react-components/Checkbox';
// or
import { Checkbox } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **checked** | ``false \| true`` | - | If `true`, the component is checked. |
| **color** | ``"primary" \| "error"`` | - | The color of the Checkbox |
| **component** | `ElementType` | - |  |
| **defaultChecked** | ``false \| true`` | - | The default checked state. Use when the component is not controlled. |
| **disabled** | ``false \| true`` | - | If `true`, the component is disabled. |
| **id** | `string` | - | The id of the `input` element. |
| **indeterminate** | ``false \| true`` | `false` | If `true`, the component appears indeterminate.<br>This does not set the native input element to indeterminate due<br>to inconsistent behavior across browsers.<br>However, we set a `data-indeterminate` attribute on the `input`. |
| **label** | `ReactNode` | - | The label element next to the checkbox. |
| **name** | `string` | - | The `name` attribute of the input. |
| **onBlur** | ``FocusEventHandler<Element>`` | - |  |
| **onChange** | ``ChangeEventHandler<HTMLInputElement>`` | - | Callback fired when the state is changed.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (string).<br>You can pull out the new checked state by accessing `event.target.checked` (boolean). |
| **onFocus** | ``FocusEventHandler<Element>`` | - |  |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **readOnly** | ``false \| true`` | - | If `true`, the component is read only. |
| **required** | ``false \| true`` | - | If `true`, the `input` element is required. |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | The size of the Checkbox. |
| **value** | ``string \| number \| readonly string[]`` | - | The value of the component. The DOM API casts this to a string.<br>The browser uses "on" as the default value. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCheckbox-root | `'span'` | The component that renders the root. |
| checkbox | .NovaCheckbox-checkbox | `'span'` | The component that renders the checkbox. |
| action | .NovaCheckbox-action | `'span'` | The component that renders the action. |
| input | .NovaCheckbox-input | `'input'` | The component that renders the input. |
| label | .NovaCheckbox-label | `'label'` | The component that renders the label. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCheckbox-checked | `checked` | State class applied to the root element if `checked={true}`. |
| .NovaCheckbox-disabled | `disabled` | State class applied to the root element if `disabled={true}`. |
| .NovaCheckbox-indeterminate | `indeterminate` | State class applied to the root element if `indeterminate={true}`. |
| .NovaCheckbox-colorPrimary | `colorPrimary` | State class applied to the root element if `color="primary"`. |
| .NovaCheckbox-colorError | `colorError` | State class applied to the root element if `color="secondary"`. |
| .NovaCheckbox-sizeMedium | `sizeMedium` | Styles applied to the root element if `size="medium"`. |
| .NovaCheckbox-sizeLarge | `sizeLarge` | Styles applied to the root element if `size="large"`. |
| .NovaCheckbox-sizeSmall | `sizeSmall` | Styles applied to the root element if `size="small"`. |
| .NovaCheckbox-focusVisible | `focusVisible` | Class name applied to the root element if the switch has visible focus |

