import React from 'react';
import { Checkbox } from '@hxnova/react-components/Checkbox';

export default function ControlledExample() {
  const [checked, setChecked] = React.useState(true);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
  };

  return <Checkbox checked={checked} onChange={handleChange} label={checked ? 'Checked' : 'Unchecked'} />;
}
