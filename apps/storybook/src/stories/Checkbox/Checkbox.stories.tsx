import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Checkbox as NovaCheckbox } from '@hxnova/react-components/Checkbox';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: '@hxnova/react-components/Checkbox',
  component: NovaCheckbox,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=870-9633&p=f&t=WnVuh2OQIurjyXbe-0',
    },
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['!autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // argTypes: {
  //   backgroundColor: { control: 'color' },
  // },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  // args: { onClick: fn() },
} satisfies Meta<typeof NovaCheckbox>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Checkbox: Story = {
  args: {
    color: 'primary',
    size: 'medium',
    disabled: false,
    indeterminate: false,
    label: 'Label',
  },
  argTypes: {
    color: {
      control: { type: 'radio' },
      options: ['primary', 'error'],
    },
    size: {
      control: { type: 'radio' },
      options: ['small', 'medium', 'large'],
    },
    disabled: {
      type: 'boolean',
    },
    indeterminate: {
      type: 'boolean',
    },
    label: {
      type: 'string',
    },
  },
};
