import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import BasicExample from './Examples/BasicExample';
import LabelExampleSource from './Examples/LabelExample.tsx?raw';
import LabelExample from './Examples/LabelExample';
import ColorExampleSource from './Examples/ColorExample.tsx?raw';
import ColorExample from './Examples/ColorExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import ControlledExampleSource from './Examples/ControlledExample.tsx?raw';
import ControlledExample from './Examples/ControlledExample';
import IndeterminateExampleSource from './Examples/IndeterminateExample.tsx?raw';
import IndeterminateExample from './Examples/IndeterminateExample';

<Meta title="@hxnova/react-components/Checkbox/Examples" />
 
## Basic Checkbox

Checkboxes can be used to turn an option on or off. It supports checked, unchecked and disabled state.

<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Label

You can customize the label content using the `label` prop.

<div className="sb-unstyled">
  <LabelExample />
</div>
<CodeExpand code={LabelExampleSource} showBorderTop style={{marginTop: 16}}/>

## Color

Checkbox comes with two colors: `primary`(default), `error`. These can be adjusted using the `color` prop.

<div className="sb-unstyled">
  <ColorExample />
</div>
<CodeExpand code={ColorExampleSource} showBorderTop style={{marginTop: 16}}/>


## Size

Checkbox comes with two sizes: `small`,  `medium` (default) and `large`. These can be adjusted using the `size` prop.
<div className="sb-unstyled">
  <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Controlled

This allows you to control its checked state programmatically, making it easier to integrate the checkbox with forms and other interactive components.

<div className="sb-unstyled">
  <ControlledExample />
</div>
<CodeExpand code={ ControlledExampleSource } showBorderTop style={{marginTop: 16}}/>


## Indeterminate

You can utilize the `indeterminate` prop to indicate a mixed state for the checkbox. 

<div className="sb-unstyled">
  <IndeterminateExample />
</div>
<CodeExpand code={ IndeterminateExampleSource } showBorderTop style={{marginTop: 16}}/>



