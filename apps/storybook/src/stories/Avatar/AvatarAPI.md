# API Documentation

- [Avatar](#avatar)
- [AvatarGroup](#avatargroup)

# Avatar

API reference docs for the React Avatar component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Avatar from '@hxnova/react-components/Avatar';
// or
import { Avatar } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **alt** | `string` | - | Used in combination with `src` or `srcSet` to<br>provide an alt attribute for the rendered `img` element. |
| **children** | `ReactNode` | - | Used to render icon or text elements inside the Avatar if `src` is not set.<br>This can be an element, or just a string. |
| **color** | ``"primary" \| "error" \| "info" \| "warning" \| "success"`` | - | The  status badge color of the component. |
| **disabled** | ``false \| true`` | - | The disable state of the avatar |
| **size** | ``"small" \| "large" \| "medium"`` | `'medium'` | The overall size of the button. |
| **src** | `string` | - | The `src` attribute for the `img` element. |
| **srcSet** | `string` | - | The `srcSet` attribute for the `img` element.<br>Use this attribute for responsive image display. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAvatar-root | `'div'` | The component that renders the root. |
| img | .NovaAvatar-img | `'img'` | The component that renders the img. |
| fallback | .NovaAvatar-fallback | `'svg'` | The component that renders the fallback. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaAvatar-sizeSmall | `sizeSmall` | Styles applied to the root element if `size="small"`. |
| .NovaAvatar-sizeMedium | `sizeMedium` | Styles applied to the root element if `size="medium"`. |
| .NovaAvatar-sizeLarge | `sizeLarge` | Styles applied to the root element if `size="large"`. |
| .NovaAvatar-focusVisible | `focusVisible` | Styles applied to the root element if the link is keyboard focused. |

<br><br>

# AvatarGroup

API reference docs for the React AvatarGroup component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import AvatarGroup from '@hxnova/react-components/AvatarGroup';
// or
import { AvatarGroup } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **children** | `ReactNode` | - | Used to render icon or text elements inside the AvatarGroup if `src` is not set.<br>This can be an element, or just a string. |
| **color** | ``"primary" \| "error" \| "info" \| "warning" \| "success"`` | - | The  status badge color of the component. |
| **disabled** | ``false \| true`` | - | The disable state of the avatar |
| **size** | ``"small" \| "medium" \| "large"`` | `'medium'` | The overall size of the button. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAvatarGroup-root | `'div'` | The component that renders the root. |

