import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';                                                              
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import BasicExample from './Examples/BasicExample';
import StatusExampleSource from './Examples/StatusExample.tsx?raw';
import StatusExample from './Examples/StatusExample';
import ImageExampleSource from './Examples/ImageExample.tsx?raw';
import ImageExample from './Examples/ImageExample';
import TextExampleSource from './Examples/TextExample.tsx?raw';
import TextExample from './Examples/TextExample';
import IconExampleSource from './Examples/IconExample.tsx?raw';
import IconExample from './Examples/IconExample';
import GroupExampleSource from './Examples/GroupExample.tsx?raw';
import GroupExample from './Examples/GroupExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import FallbackExampleSource from './Examples/FallbackExample.tsx?raw';
import FallbackExample from './Examples/FallbackExample';

<Meta title="@hxnova/react-components/Avatar/Examples" />
 
## Basic Avatar

The basic avatar component displays a generic person icon and doesn't have a status. You can replace this icon with a text string or an image and add a status.
<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>


## Avatar StatusColor

Avatar status color comes with five colors: `primary`(default), `error`, `warning`, `info`, `success`. These can be adjusted using the `color` prop.
<div className="sb-unstyled">
  <StatusExample />
</div>
<CodeExpand code={StatusExampleSource} showBorderTop style={{marginTop: 16}}/>


## Image Avatar

Image avatars can be created by passing standard img props `src` or `srcSet` to the component.
<div className="sb-unstyled"> 
    <ImageExample />
</div>
<CodeExpand code={ImageExampleSource} showBorderTop style={{marginTop: 16}}/>


## Text Avatar
Text avatar are created by passing a string as children.
<div className="sb-unstyled"> 
    <TextExample />
</div>
<CodeExpand code={TextExampleSource} showBorderTop style={{marginTop: 16}}/>


## Icon Avatar
Icon avatars are created by passing an icon as children.
<div className="sb-unstyled"> 
    <IconExample />
</div>
<CodeExpand code={IconExampleSource} showBorderTop style={{marginTop: 16}}/>


## Avatar Sizes
You can use the `size` prop to change the avatar size, we provide three sizes: `small`, `medium` and `large`.
<div className="sb-unstyled"> 
    <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>


## Avatar Fallbacks

If there is an error loading the avatar image, the component falls back to an alternative in the following order:

- the provided children
- the first letter of the alt text
- a generic avatar icon

<div className="sb-unstyled"> 
    <FallbackExample />
</div>
<CodeExpand code={FallbackExampleSource} showBorderTop style={{marginTop: 16}}/>


## Grouped Avatar

AvatarGroup renders its children as a stack. Use the max prop to limit the number of avatars.

<div className="sb-unstyled"> 
    <GroupExample />
</div>
<CodeExpand code={GroupExampleSource} showBorderTop style={{marginTop: 16}}/>
