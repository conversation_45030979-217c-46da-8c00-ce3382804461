import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import BasicExample from './Examples/BasicExample';
import SearchExampleSource from './Examples/SearchExample.tsx?raw';
import SearchExample from './Examples/SearchExample';
import ControlledExampleSource from './Examples/ControlledExample.tsx?raw';
import ControlledExample from './Examples/ControlledExample';
import GroupedExampleSource from './Examples/GroupedExample.tsx?raw';
import GroupedExample from './Examples/GroupedExample';
import LoadingExampleSource from './Examples/LoadingExample.tsx?raw';
import LoadingExample from './Examples/LoadingExample';
import MultipleExampleSource from './Examples/MultipleExample.tsx?raw';
import MultipleExample from './Examples/MultipleExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import HighlightExampleSource from './Examples/HighlightExample.tsx?raw';
import HighlightExample from './Examples/HighlightExample';

<Meta title="@hxnova/react-components/Autocomplete/Examples" />

## Combo box

The value must be chosen from a predefined set of allowed values.

By default, the options accepts an array of `string` or `{ label: string }`:

<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

However, you can use different structures by providing a `getOptionLabel` prop.
```tsx
const options = [
  { title: 'Pulp Fiction', id: 2 },
  // ...
];

<Autocomplete getOptionLabel={option => option.title}>
```

## Search input

Use `freeSolo` to create a search input with suggestions experience

<div className="sb-unstyled">
  <SearchExample />
</div>
<CodeExpand code={SearchExampleSource} showBorderTop style={{marginTop: 16}}/>


## Controlled states

The component has two states that can be controlled:

- the "value" state with the value/onChange props combination. This state represents the value selected by the user, for instance when pressing Enter.
- the "input value" state with the inputValue/onInputChange props combination. This state represents the value displayed in the textbox.
These two states are isolated, and should be controlled independently.

<div className="sb-unstyled">
  <ControlledExample />
</div>
<CodeExpand code={ControlledExampleSource} showBorderTop style={{marginTop: 16}}/>

## Grouped options

You can group the options with the `groupBy` prop. If you do so, make sure that the options are also sorted with the same dimension that they are grouped by, otherwise, you will notice duplicate headers.

<div className="sb-unstyled">
  <GroupedExample />
</div>
<CodeExpand code={GroupedExampleSource} showBorderTop style={{marginTop: 16}}/>

## Loading

It displays a progress state as long as the network request is pending.

<div className="sb-unstyled">
  <LoadingExample />
</div>
<CodeExpand code={LoadingExampleSource} showBorderTop style={{marginTop: 16}}/>


## Multiple selection

When `multiple={true}`, the user can select multiple values. 

<div className="sb-unstyled">
  <MultipleExample />
</div>
<CodeExpand code={MultipleExampleSource} showBorderTop style={{marginTop: 16}}/>

## Sizes

Autocomplete comes with three sizes: small, medium(default), large. These can be adjusted using the `size` prop.

<div className="sb-unstyled">
  <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Highlighting options

When using typed words, the option text will highlight when it matches with the user's typed words, it is used the autosuggest-highlight/parse package to implement this function.

<div className="sb-unstyled">
  <HighlightExample />
</div>
<CodeExpand code={HighlightExampleSource} showBorderTop style={{marginTop: 16}}/>
