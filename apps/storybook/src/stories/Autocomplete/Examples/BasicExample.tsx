import { Autocomplete } from '@hxnova/react-components/Autocomplete';
import { TextField } from '@hxnova/react-components/TextField';

const options = [
  { label: 'The Shawshank Redemption', year: 1994 },
  { label: 'The Godfather', year: 1972 },
  { label: 'The Godfather: Part II', year: 1974 },
  { label: 'The Dark Knight', year: 2008 },
  { label: '12 Angry Men', year: 1957 },
  { label: "Schindler's List", year: 1993 },
  { label: 'Pulp Fiction', year: 1994 },
];

// or const options = ['The Godfather', 'Pulp Fiction'];

export default function Demo() {
  return (
    <Autocomplete
      style={{ minWidth: '400px' }}
      disablePortal
      options={options}
      renderInput={(params) => <TextField {...params} label="Label" placeholder="Placeholder" />}
    />
  );
}
