import { StoryFn, Meta } from '@storybook/react';
import { Autocomplete } from '@hxnova/react-components/Autocomplete';
import { TextField } from '@hxnova/react-components/TextField';
import { MenuItem, MenuItemProps } from '@hxnova/react-components/MenuItem';
import parse from 'autosuggest-highlight/parse';
import match from 'autosuggest-highlight/match';
import { styled } from '@pigment-css/react';

const options = [
  { label: 'The Shawshank Redemption', year: 1994 },
  { label: 'The Godfather', year: 1972 },
  { label: 'The Godfather: Part II', year: 1974 },
  { label: 'The Dark Knight', year: 2008 },
  { label: '12 Angry Men', year: 1957 },
  { label: "Schindler's List", year: 1993 },
  { label: 'Pulp Fiction', year: 1994 },
];

const StyledMenuItem = styled(MenuItem)(() => ({
  '&.Mui-focused, &.Mui-focusVisible': {
    backgroundColor:
      'color-mix(in srgb, var(--palette-backgroundStates), var(--palette-onSurface) var(--palette-stateLayers-focusOnSurface))',
  },

  '&[aria-selected="true"]': {
    backgroundColor: 'var(--palette-secondaryContainer)',
    '&.Mui-focused, &.Mui-focusVisible': {
      backgroundColor:
        'color-mix(in srgb, var(--palette-surfaceContainerHighest), var(--palette-onSurface) var(--palette-stateLayers-focusOnSurface))',
    },
  },
}));

export default {
  title: '@hxnova/react-components/Autocomplete',
  component: Autocomplete,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Components?node-id=13185-4732&p=f',
    },
    layout: 'centered',
  },
  tags: ['!autodocs'],
} as Meta<typeof Autocomplete>;

const Template: StoryFn = (args) => {
  return (
    <Autocomplete
      {...args}
      sx={{ minWidth: '400px' }}
      options={options}
      getOptionLabel={(option) => option.label}
      renderInput={(params) => <TextField {...params} label="Basic" placeholder="Placeholder" />}
    />
  );
};

const GroupedTemplate: StoryFn = (args) => {
  const groupedOptions = options.map((option) => {
    const firstLetter = option.label[0].toUpperCase();
    return {
      firstLetter: /[0-9]/.test(firstLetter) ? '0-9' : firstLetter,
      ...option,
    };
  });
  return (
    <Autocomplete
      {...args}
      sx={{ minWidth: '400px' }}
      options={groupedOptions}
      groupBy={(option) => option.firstLetter}
      renderInput={(params) => <TextField {...params} label="Grouped" placeholder="Placeholder" />}
    />
  );
};

const HighlightTemplate: StoryFn = (args) => {
  return (
    <Autocomplete
      {...args}
      sx={{ minWidth: '400px' }}
      options={options}
      getOptionLabel={(option) => option.label}
      renderInput={(params) => <TextField {...params} label="Highlights" placeholder="Placeholder" />}
      renderOption={(props, option, { inputValue }) => {
        const { key, ...optionProps } = props;
        const matches = match(option.label, inputValue, { insideWords: true });
        const parts = parse(option.label, matches);
        return (
          <StyledMenuItem component={'li'} key={key} {...(optionProps as MenuItemProps)}>
            <div>
              {parts.map((part, index) => (
                <span
                  key={index}
                  style={{
                    fontWeight: part.highlight ? 700 : 400,
                  }}
                >
                  {part.text}
                </span>
              ))}
            </div>
          </StyledMenuItem>
        );
      }}
    />
  );
};

export const Basic = {
  render: Template,
  args: {
    size: 'medium',
    multiple: false,
    freeSolo: false,
    fullWidth: false,
    autoHighlight: false,
    autoSelect: false,
    blurOnSelect: false,
    clearOnBlur: true,
    clearOnEscape: false,
    clearText: 'Clear',
    closeText: 'Close',
    disableClearable: false,
    disableCloseOnSelect: false,
    disabled: false,
    disabledItemsFocusable: false,
    forcePopupIcon: true,
    limitTags: -1,
    noOptionsText: 'No options',
    openOnFocus: false,
    openText: 'Open',
    readOnly: false,
    selectOnFocus: false,
  },
  argTypes: {
    multiple: {
      control: { type: 'boolean' },
    },
    freeSolo: {
      control: { type: 'boolean' },
    },
    fullWidth: {
      control: { type: 'boolean' },
    },
    autoHighlight: {
      control: { type: 'boolean' },
    },
    autoSelect: {
      control: { type: 'boolean' },
    },
    blurOnSelect: {
      control: { type: 'boolean' },
    },
    clearOnBlur: {
      control: { type: 'boolean' },
    },
    clearOnEscape: {
      control: { type: 'boolean' },
    },
    disableClearable: {
      control: { type: 'boolean' },
    },
    disableCloseOnSelect: {
      control: { type: 'boolean' },
    },
    disabled: {
      control: { type: 'boolean' },
    },
    disabledItemsFocusable: {
      control: { type: 'boolean' },
    },
    forcePopupIcon: {
      control: { type: 'boolean' },
    },
    limitTags: {
      control: { type: 'number' },
    },
    openOnFocus: {
      control: { type: 'boolean' },
    },
    readOnly: {
      control: { type: 'boolean' },
    },
    selectOnFocus: {
      control: { type: 'boolean' },
    },
    size: {
      control: { type: 'radio' },
      options: ['small', 'medium', 'large'],
    },
  },
  parameters: {
    controls: {
      include: [
        'size',
        'multiple',
        'freeSolo',
        'fullWidth',
        'autoHighlight',
        'autoSelect',
        'blurOnSelect',
        'clearOnBlur',
        'clearOnEscape',
        'clearText',
        'closeText',
        'disableClearable',
        'disableCloseOnSelect',
        'disabled',
        'disabledItemsFocusable',
        'forcePopupIcon',
        'limitTags',
        'noOptionsText',
        'openOnFocus',
        'openText',
        'readOnly',
        'selectOnFocus',
      ],
    },
  },
};

export const Multiple = {
  render: Template,
  args: {
    ...Basic.args,
    multiple: true,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    controls: {
      include: [...Basic.parameters.controls.include],
    },
  },
};

export const Grouped = {
  render: GroupedTemplate,
  args: {
    ...Basic.args,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
};

export const Highlight = {
  render: HighlightTemplate,
  args: {
    ...Basic.args,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
};
