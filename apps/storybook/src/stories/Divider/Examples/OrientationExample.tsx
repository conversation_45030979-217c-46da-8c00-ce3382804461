import * as React from 'react';
import Icon from '@hxnova/icons/Icon';
import { Divider, dividerClasses } from '@hxnova/react-components/Divider';

export default function VerticalDividers() {
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        border: '1px solid',
        borderColor: 'var(--palette-outline)',
        borderRadius: '8px',
        backgroundColor: 'var(--palette-surfaceContainer)',
        color: 'var(--palette-onSurfaceVariant)',
        width: 'fit-content',

        [`& .${dividerClasses.root}`]: {
          marginInline: 0.5,
        },
      }}
    >
      <Icon family="material" name="format_align_left" style={{ padding: '16px' }} />
      <Divider orientation="vertical" />
      <Icon family="material" name="format_align_center" style={{ padding: '16px' }} />
      <Divider orientation="vertical" />
      <Icon family="material" name="format_align_right" style={{ padding: '16px' }} />
      <Divider orientation="vertical" />
      <Icon family="material" name="format_bold" style={{ padding: '16px' }} />
    </div>
  );
}
