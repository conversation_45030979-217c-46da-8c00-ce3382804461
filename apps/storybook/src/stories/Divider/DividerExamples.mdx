import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';                                                                                     
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import BasicExample from './Examples/BasicExample';
import OrientationExampleSource from './Examples/OrientationExample.tsx?raw';
import OrientationExample from './Examples/OrientationExample';
import VariantExampleSource from './Examples/VariantExample.tsx?raw';
import VariantExample from './Examples/VariantExample';
import ChildrenExampleSource from './Examples/ChildrenExample.tsx?raw';
import ChildrenExample from './Examples/ChildrenExample';

<Meta title="@hxnova/react-components/Divider/Examples" />
 
## Basic Divider

Dividers are thin lines that group content in lists or other containers
<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>


## Vertical Divider

You can use the Use the `orientation` prop to render a vertical divider.
<div className="sb-unstyled">
  <OrientationExample />
</div>
<CodeExpand code={OrientationExampleSource} showBorderTop style={{marginTop: 16}}/>


## Inset Divider

The Divider component supports two variants: `fullWidth` (default) and `inset`.

<div className="sb-unstyled"> 
    <VariantExample />
</div>
<CodeExpand code={VariantExampleSource} showBorderTop style={{marginTop: 16}}/>

## Divider with Children 

The Divider component can use with children.

<div className="sb-unstyled"> 
    <ChildrenExample />
</div>
<CodeExpand code={ChildrenExampleSource} showBorderTop style={{marginTop: 16}}/>
