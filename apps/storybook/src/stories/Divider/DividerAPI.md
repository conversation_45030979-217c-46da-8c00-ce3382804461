# API Documentation

- [Divider](#divider)

# Divider

API reference docs for the React Divider component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Divider from '@hxnova/react-components/Divider';
// or
import { Divider } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - |  |
| **orientation** | ``"horizontal" \| "vertical"`` | `'horizontal'` | The component orientation. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **variant** | ``"inset" \| "fullWidth"`` | `'fullWidth'` | The variant to use. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDivider-root | `'hr'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaDivider-horizontal | `horizontal` | Styles applied to the root element if `orientation="horizontal"`. |
| .NovaDivider-vertical | `vertical` | Styles applied to the root element if `orientation="vertical"`. |
| .NovaDivider-inset | `inset` | Styles applied to the root element if `variant="inset"`. |
| .NovaDivider-fullWidth | `fullWidth` | Styles applied to the root element if `variant="fullWidth"`. |

