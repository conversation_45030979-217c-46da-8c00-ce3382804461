import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { Divider as NovaDivider } from '@hxnova/react-components/Divider';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
export default {
  title: '@hxnova/react-components/Divider',
  component: NovaDivider,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=4594-11473&t=mFEWBqPmB6lXR4Sd-0',
    },
  },
  tags: ['!autodocs'],
} as Meta<typeof NovaDivider>;

const Template: StoryFn<typeof NovaDivider> = (args) => (
  <div style={{ display: 'flex', alignItems: 'center', width: 'fit-content' }}>
    <List orientation={args.orientation === 'vertical' ? 'horizontal' : 'vertical'}>
      <ListItem>
        <ListItemButton>
          <ListItemContent primary="ListItemContent" />
        </ListItemButton>
      </ListItem>
      <NovaDivider component="li" {...args} />
      <ListItem>
        <ListItemButton>
          <ListItemContent primary="ListItemContent" />
        </ListItemButton>
      </ListItem>
    </List>
  </div>
);

export const Divider = {
  render: Template,
  args: {
    orientation: 'horizontal',
    variant: 'fullWidth',
  },
  argTypes: {
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
    },
    variant: {
      control: { type: 'select' },
      options: ['fullWidth', 'inset'],
    },
  },
  parameters: {
    controls: {
      include: ['orientation', 'variant'],
    },
  },
};
