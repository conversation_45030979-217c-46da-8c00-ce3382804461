import type { StoryFn } from '@storybook/react';
import { Skeleton, SkeletonProps } from '@hxnova/react-components/Skeleton';

const meta = {
  title: '@hxnova/react-components/Skeleton',
  component: Skeleton,
  parameters: {
    layout: 'centered',
  },
  tags: ['!autodocs'],
};

export default meta;

const Template: StoryFn<(props: SkeletonProps) => JSX.Element> = (args) => {
  return <Skeleton {...args} />;
};

export const Basic = {
  render: Template,
  args: {
    animation: 'pulse',
    variant: 'rectangular',
    width: 300,
    height: 100,
  },
  argTypes: {
    animation: {
      control: {
        type: 'radio',
      },
      options: ['pulse', 'wave', false],
    },
    variant: {
      control: {
        type: 'radio',
      },
      options: ['text', 'rectangular', 'rounded', 'circular'],
    },
    width: {
      control: {
        type: 'number',
      },
    },
    height: {
      control: {
        type: 'number',
      },
    },
  },
  parameters: {
    controls: {
      include: ['variant', 'animation', 'width', 'height'],
    },
  },
};
