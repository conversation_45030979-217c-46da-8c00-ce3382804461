# API Documentation

- [Skeleton](#skeleton)

# Skeleton

API reference docs for the React Skeleton component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Skeleton from '@hxnova/react-components/Skeleton';
// or
import { Skeleton } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **animation** | ``false \| "pulse" \| "wave"`` | `'pulse'` | The animation.<br>If `false` the animation effect is disabled. |
| **children** | `ReactNode` | - | Optional children to infer width and height from. |
| **component** | `ElementType` | - |  |
| **height** | ``string \| number`` | - | Height of the skeleton.<br>Useful when you don't want to adapt the skeleton to a text element but for instance a card. |
| **variant** | ``"text" \| "rectangular" \| "rounded" \| "circular"`` | `'text'` | The type of content that will be rendered. |
| **width** | ``string \| number`` | - | Width of the skeleton.<br>Useful when the skeleton is inside an inline element with no width of its own. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaSkeleton-root | `root` | Styles applied to the root element. |
| .NovaSkeleton-text | `text` | Styles applied to the root element if `variant="text"`. |
| .NovaSkeleton-rectangular | `rectangular` | Styles applied to the root element if `variant="rectangular"`. |
| .NovaSkeleton-rounded | `rounded` | Styles applied to the root element if `variant="rounded"`. |
| .NovaSkeleton-circular | `circular` | Styles applied to the root element if `variant="circular"`. |
| .NovaSkeleton-pulse | `pulse` | Styles applied to the root element if `animation="pulse"`. |
| .NovaSkeleton-wave | `wave` | Styles applied to the root element if `animation="wave"`. |
| .NovaSkeleton-withChildren | `withChildren` | Styles applied when the component is passed children. |
| .NovaSkeleton-fitContent | `fitContent` | Styles applied when the component is passed children and no width. |
| .NovaSkeleton-heightAuto | `heightAuto` | Styles applied when the component is passed children and no height. |

