import { Skeleton } from '@hxnova/react-components/Skeleton';
import { Typography } from '@hxnova/react-components/Typography';
import { Grid } from '@hxnova/react-components/Grid';
import { Divider, Card } from '@hxnova/react-components';
import { Avatar } from '@hxnova/react-components/Avatar';
import avatarFirst from './images/avatar_first.png';

function TypographyDemo(props: { loading?: boolean }) {
  const { loading = false } = props;

  return (
    <div>
      <Typography variant="bodyLarge">{loading ? <Skeleton variant="text" /> : 'bodyLarge'}</Typography>
      <Typography variant="bodyMedium">{loading ? <Skeleton variant="text" /> : 'bodyMedium'}</Typography>
      <Typography variant="bodySmall">{loading ? <Skeleton variant="text" /> : 'bodySmall'}</Typography>
    </div>
  );
}

function SkeletonChildrenDemo(props: { loading?: boolean }) {
  const { loading = false } = props;
  return (
    <Card.Root>
      <Card.Header
        avatar={
          loading ? (
            <Skeleton variant="circular">
              <Avatar />
            </Skeleton>
          ) : (
            <Avatar alt="Remy Sharp" src={avatarFirst} />
          )
        }
        heading={
          loading ? (
            <Skeleton variant="text" width="100%">
              <Typography variant="titleSmall">.</Typography>
            </Skeleton>
          ) : (
            'Daniela Maas'
          )
        }
        subheading={
          loading ? (
            <Skeleton variant="text" width="100%">
              <Typography variant="bodyMedium">.</Typography>
            </Skeleton>
          ) : (
            <Typography>Yesterday</Typography>
          )
        }
      />

      {loading ? (
        <Skeleton variant="rectangular" width="100%">
          <div style={{ paddingTop: '57%' }} />
        </Skeleton>
      ) : (
        <Card.Media
          component="img"
          image={'https://cdn.sanity.io/images/eqlh3dcx/dev/c2ab4812a5485ba4a15aa8f4517b94edb5728c1d-1142x643.png'}
        />
      )}
    </Card.Root>
  );
}

export default function Demo() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 16, width: 600 }}>
      {/* It works well when it comes to typography as its height is set using `em` units. */}
      <Grid container spacing={8}>
        <Grid size={6}>
          <TypographyDemo loading />
        </Grid>
        <Grid size={6}>
          <TypographyDemo />
        </Grid>
      </Grid>
      <Divider />

      {/* You can pass children and it will infer its width and height from them. */}
      <Grid container spacing={8}>
        <Grid size={6}>
          <SkeletonChildrenDemo loading />
        </Grid>
        <Grid size={6}>
          <SkeletonChildrenDemo />
        </Grid>
      </Grid>
    </div>
  );
}
