import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';  
import VariantExample from './Examples/VariantExample';
import VariantExampleSource from './Examples/VariantExample.tsx?raw';
import AnimationExample from './Examples/AnimationExample';
import AnimationExampleSource from './Examples/AnimationExample.tsx?raw';
import InferringDimensionsExample from './Examples/InferringDimensionsExample';
import InferringDimensionsExampleSource from './Examples/InferringDimensionsExample.tsx?raw';

 <Meta title="@hxnova/react-components/Skeleton/Examples" />
 
## Variants

Nova Skeleton component come in four shape variations, which can be configured using the `variant` prop. The default variant is `text`.

<div className="sb-unstyled">
    <VariantExample />
</div>
<CodeExpand code={VariantExampleSource} showBorderTop style={{marginTop: 16}}/>

## Animations

By default, the skeleton pulsates, but you can change the animation to a wave or disable it entirely.

<div className="sb-unstyled">
    <AnimationExample />
</div>
<CodeExpand code={AnimationExampleSource} showBorderTop style={{marginTop: 16}}/>

## Inferring dimensions

In addition to accepting `width` and `height` props, the component can also infer the dimensions.

<div className="sb-unstyled">
    <InferringDimensionsExample />
</div>
<CodeExpand code={InferringDimensionsExampleSource} showBorderTop style={{marginTop: 16}}/>
