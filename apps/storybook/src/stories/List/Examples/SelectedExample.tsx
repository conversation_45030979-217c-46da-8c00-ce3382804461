import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import Icon from '@hxnova/icons/Icon';

export default function SelectedList() {
  return (
    <List style={{ maxWidth: 320 }}>
      <ListItem>
        <ListItemButton selected>
          <ListItemDecorator>
            <Icon family="material" name="home" size={24} />
          </ListItemDecorator>
          Home
        </ListItemButton>
      </ListItem>
      <ListItem>
        <ListItemButton>
          <ListItemDecorator>
            <Icon family="material" name="apps" size={24} />
          </ListItemDecorator>
          Apps
        </ListItemButton>
      </ListItem>
      <ListItem>
        <ListItemButton>
          <ListItemDecorator />
          Settings
        </ListItemButton>
      </ListItem>
    </List>
  );
}
