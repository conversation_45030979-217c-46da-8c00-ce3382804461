import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListSubheader } from '@hxnova/react-components/ListSubheader';
import { Checkbox } from '@hxnova/react-components/Checkbox';
import { Avatar } from '@hxnova/react-components/Avatar';
import { IconButton } from '@hxnova/react-components/IconButton';
import Icon from '@hxnova/icons/Icon';
import { Switch } from '@hxnova/react-components/Switch';

export default function ControlList() {
  const [checked, setChecked] = React.useState(['wifi']);

  const handleToggle = (value: string) => () => {
    const currentIndex = checked.indexOf(value);
    const newChecked = [...checked];

    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }

    setChecked(newChecked);
  };
  console.log(checked, checked.includes('checkbox'));
  return (
    <List style={{ width: '100%', maxWidth: 360 }}>
      <ListSubheader>Category 1</ListSubheader>
      <ListItem>
        <ListItemButton onClick={handleToggle('checkbox')}>
          <ListItemDecorator>
            <Checkbox checked={checked.includes('checkbox')} />
          </ListItemDecorator>
          <ListItemContent id={'checkbox-list-label'} primary={`Line item 2`} />
          <ListItemDecorator>
            <IconButton aria-label="comments" endIcon={<Icon family="material" name="comment" size={24} />} />
          </ListItemDecorator>
        </ListItemButton>
      </ListItem>
      <ListItem>
        <ListItemButton onClick={handleToggle('checkbox2')}>
          <ListItemDecorator>
            <Checkbox checked={checked.includes('checkbox2')} />
          </ListItemDecorator>
          <ListItemContent id={'checkbox-list-label-1'} primary={`Line item 3`} />
          <ListItemDecorator>
            <Avatar />
          </ListItemDecorator>
        </ListItemButton>
      </ListItem>
      <ListItem>
        <ListItemButton onClick={handleToggle('wifi')}>
          <ListItemDecorator>
            <Icon family="material" name="wifi" size={24} />
          </ListItemDecorator>
          <ListItemContent id="switch-list-label-wifi" primary="Wi-Fi" />
          <Switch checked={checked.includes('wifi')} />
        </ListItemButton>
      </ListItem>
    </List>
  );
}
