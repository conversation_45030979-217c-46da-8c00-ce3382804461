import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListSubheader } from '@hxnova/react-components/ListSubheader';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';

export default function StickyList() {
  return (
    <div style={{ width: 320, maxHeight: 300, overflow: 'auto', borderRadius: '8px' }}>
      <List>
        {[...Array(5)].map((_, categoryIndex) => (
          <ListItem nested key={categoryIndex}>
            <ListSubheader sticky>Category {categoryIndex + 1}</ListSubheader>
            <List>
              {[...Array(10)].map((__, index) => (
                <ListItem key={index}>
                  <ListItemButton>
                    <ListItemContent>Subitem {index + 1}</ListItemContent>
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </ListItem>
        ))}
      </List>
    </div>
  );
}
