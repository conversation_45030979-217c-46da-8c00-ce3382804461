import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Typography } from '@hxnova/react-components/Typography';

export default function DecoratedList() {
  return (
    <div>
      <Typography
        id="decorated-list-demo"
        variant="bodySmall"
        style={{ textTransform: 'uppercase', marginBottom: '4px' }}
      >
        Ingredients
      </Typography>
      <List aria-labelledby="decorated-list-demo" style={{ maxWidth: 360 }}>
        <ListItem>
          <ListItemButton>
            <ListItemDecorator>🧅</ListItemDecorator> 1 red onion
          </ListItemButton>
        </ListItem>
        <ListItem>
          <ListItemButton>
            <ListItemDecorator>🍤</ListItemDecorator> 2 Shrimps
          </ListItemButton>
        </ListItem>
        <ListItem>
          <ListItemButton>
            <ListItemDecorator>🥓</ListItemDecorator> 120g bacon
          </ListItemButton>
        </ListItem>
      </List>
    </div>
  );
}
