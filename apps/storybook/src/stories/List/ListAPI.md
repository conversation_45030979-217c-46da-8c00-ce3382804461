# API Documentation

- [List](#list)
- [ListDivider](#listdivider)
- [ListItem](#listitem)
- [ListItemButton](#listitembutton)
- [ListItemContent](#listitemcontent)
- [ListItemDecorator](#listitemdecorator)
- [ListSubheader](#listsubheader)

# List

API reference docs for the React List component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import List from '@hxnova/react-components/List';
// or
import { List } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - |  |
| **density** | ``"compact" \| "standard" \| "comfortable"`` | `'standard'` | The `density` attribute for the ListItem |
| **orientation** | ``"horizontal" \| "vertical"`` | `'vertical'` | The component orientation. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **wrap** | ``false \| true`` | `false` | Only for horizontal list.<br>If `true`, the list sets the flex-wrap to "wrap" and adjust margin to have gap-like behavior (will move to `gap` in the future). |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaList-root | `'ul'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaList-densityStandard | `densityStandard` | Styles applied to the root element if `density="standard"`. |
| .NovaList-densityCompact | `densityCompact` | Styles applied to the root element if `density="compact"`. |
| .NovaList-densityComfortable | `densityComfortable` | Styles applied to the root element if `density="comfortable"`. |

<br><br>

# ListDivider

API reference docs for the React ListDivider component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import ListDivider from '@hxnova/react-components/ListDivider';
// or
import { ListDivider } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - |  |
| **orientation** | ``"horizontal" \| "vertical"`` | `'horizontal'` | The component orientation. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **variant** | ``"context" \| "gutter" \| "startDecorator" \| "startContent"`` | `'context'` | The empty space on the side(s) of the divider in a vertical list.<br>For horizontal list (the nearest parent List has `row` prop set to `true`), only `inset="gutter"` affects the list divider. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListDivider-root | `'li'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaListDivider-gutter | `gutter` | Styles applied to the root element if `variant="gutter"`. |
| .NovaListDivider-startDecorator | `startDecorator` | Styles applied to the root element if `variant="startDecorator"`. |
| .NovaListDivider-startContent | `startContent` | Styles applied to the root element if `variant="startContent"`. |
| .NovaListDivider-horizontal | `horizontal` | Styles applied to the root element if `orientation="horizontal"`. |
| .NovaListDivider-vertical | `vertical` | Styles applied to the root element if `orientation="vertical"`. |

<br><br>

# ListItem

API reference docs for the React ListItem component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import ListItem from '@hxnova/react-components/ListItem';
// or
import { ListItem } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - |  |
| **disabled** | ``false \| true`` | `false` | Whether the component should ignore user interaction. |
| **nested** | ``false \| true`` | `false` | If `true`, the component can contain NestedList. |
| **sticky** | ``false \| true`` | `false` | If `true`, the component has sticky position (with top = 0). |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListItem-root | `'li'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaListItem-disabled | `disabled` | Styles applied to the inner `component` element if `disabled={true}`. |
| .NovaListItem-nested | `nested` | Styles applied to the root element, if `nested={true}`. |
| .NovaListItem-nesting | `nesting` | Styles applied to the root element, if it is under a nested list item. |
| .NovaListItem-sticky | `sticky` | Styles applied to the root element, if `sticky={true}`. |

<br><br>

# ListItemButton

API reference docs for the React ListItemButton component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import ListItemButton from '@hxnova/react-components/ListItemButton';
// or
import { ListItemButton } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<{ focusVisible(): void; }>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **autoFocus** | ``false \| true`` | `false` | If `true`, the list item is focused during the first mount.<br>Focus will also be triggered if the value changes from false to true. |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - | The component used for the Root slot.<br>Either a string to use a HTML element or a component. |
| **disabled** | ``false \| true`` | `false` | If `true`, the component is disabled. |
| **focusVisibleClassName** | `string` | - | This prop can help identify which element has keyboard focus.<br>The class name will be applied when the element gains the focus through keyboard interaction. |
| **orientation** | ``"horizontal" \| "vertical"`` | `'horizontal'` | The content direction flow. |
| **ref** | ``((instance: HTMLAnchorElement \| null) => void) \| RefObject<HTMLAnchorElement> \| null`` | - |  |
| **selected** | ``false \| true`` | `false` | If `true`, the component is selected. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **tabIndex** | `number` | `0` |  |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListItemButton-root | `'div'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaListItemButton-vertical | `vertical` | Class name applied to the root element, if `orientation="vertical"`. |
| .NovaListItemButton-horizontal | `horizontal` | Class name applied to the root element, if `orientation="horizontal"`. |
| .NovaListItemButton-focusVisible | `focusVisible` | State class applied to the `component`'s `focusVisibleClassName` prop. |
| .NovaListItemButton-disabled | `disabled` | State class applied to the inner `component` element if `disabled={true}`. |
| .NovaListItemButton-selected | `selected` | State class applied to the root element if `selected={true}`. |

<br><br>

# ListItemContent

API reference docs for the React ListItemContent component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import ListItemContent from '@hxnova/react-components/ListItemContent';
// or
import { ListItemContent } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - |  |
| **inset** | ``false \| true`` | `false` | If `true`, the children are indented.<br>This should be used if there is no left avatar or left icon. |
| **primary** | `ReactNode` | - | The main content element. |
| **secondary** | `ReactNode` | - | The secondary content element. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListItemContent-root | `'div'` | The component that renders the root. |
| primary | .NovaListItemContent-primary | `Typography` | The component that renders the primary slot. |
| secondary | .NovaListItemContent-secondary | `Typography` | The component that renders the secondary slot. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaListItemContent-dense | `dense` | Styles applied to the Typography component if dense. |

<br><br>

# ListItemDecorator

API reference docs for the React ListItemDecorator component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import ListItemDecorator from '@hxnova/react-components/ListItemDecorator';
// or
import { ListItemDecorator } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - |  |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListItemDecorator-root | `'span'` | The component that renders the root. |

<br><br>

# ListSubheader

API reference docs for the React ListSubheader component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import ListSubheader from '@hxnova/react-components/ListSubheader';
// or
import { ListSubheader } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - |  |
| **sticky** | ``false \| true`` | `false` | If `true`, the component has sticky position (with top = 0). |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListSubheader-root | `'div'` | The component that renders the root. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaListSubheader-sticky | `sticky` | Styles applied to the root element, if sticky={true}. |

