import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';                                                                                                                                                      
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import BasicExample from './Examples/BasicExample';
import ControlExampleSource from './Examples/ControlExample.tsx?raw';
import ControlExample from './Examples/ControlExample';
import DecoratorExampleSource from './Examples/DecoratorExample.tsx?raw';
import DecoratorExample from './Examples/DecoratorExample';
import DensityExampleSource from './Examples/DensityExample.tsx?raw';
import DensityExample from './Examples/DensityExample';
import ContentExampleSource from './Examples/ContentExample.tsx?raw';
import ContentExample from './Examples/ContentExample';
import InteractiveExampleSource from './Examples/InteractiveExample.tsx?raw';
import InteractiveExample from './Examples/InteractiveExample';
import NestedExampleSource from './Examples/NestedExample.tsx?raw';
import NestedExample from './Examples/NestedExample';
import SelectedExampleSource from './Examples/SelectedExample.tsx?raw';
import SelectedExample from './Examples/SelectedExample';
import StickyExampleSource from './Examples/StickyExample.tsx?raw';
import StickyExample from './Examples/StickyExample';
import HorizontalExampleSource from './Examples/HorizontalExample.tsx?raw';
import HorizontalExample from './Examples/HorizontalExample';
import DividerExampleSource from './Examples/DividerExample.tsx?raw';
import DividerExample from './Examples/DividerExample';

<Meta title="@hxnova/react-components/List/Examples" />
 
## Basic List

The Basic list is a simple icon list built with `ListItem`, `ListItemDecorator` and `ListItemContent`.
<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>


## List with Density

The List component comes in three density: `compact`, `standard`(default), and `comfortable`. 
<div className="sb-unstyled"> 
    <DensityExample />
</div>
<CodeExpand code={DensityExampleSource} showBorderTop style={{marginTop: 16}}/>


## List with Decorators

Use the `ListItemDecorator` component to add supporting icons or elements to the list item.

<div className="sb-unstyled">
  <DecoratorExample />
</div>
<CodeExpand code={DecoratorExampleSource} showBorderTop style={{marginTop: 16}}/>


## List with Content

Use the `ListItemContent` to and `primary` and `secondary` content in the list.
<div className="sb-unstyled"> 
    <ContentExample />
</div>
<CodeExpand code={ContentExampleSource} showBorderTop style={{marginTop: 16}}/>

## List with Divider

Use the `ListDivider` to add divider in the list.
<div className="sb-unstyled"> 
    <DividerExample />
</div>
<CodeExpand code={DividerExampleSource} showBorderTop style={{marginTop: 16}}/>



## List Controls
You can add `Checkbox` or `Switch` under the `ListItem`, to create a controlled list.
<div className="sb-unstyled"> 
    <ControlExample />
</div>
<CodeExpand code={ControlExampleSource} showBorderTop style={{marginTop: 16}}/>


## Nested list

You can create a nested list using the nested prop on a `ListItem`.
<div className="sb-unstyled"> 
    <NestedExample />
</div>
<CodeExpand code={NestedExampleSource} showBorderTop style={{marginTop: 16}}/>


## Interactive List

To make a List Item interactive, you can use `ListItemButton` inside a `ListItem`.
<div className="sb-unstyled"> 
    <InteractiveExample />
</div>
<CodeExpand code={InteractiveExampleSource} showBorderTop style={{marginTop: 16}}/>


## Selected list

Use the `selected` prop on the `ListItemButton` component to indicate whether or not an item is currently selected.
<div className="sb-unstyled"> 
    <SelectedExample />
</div>
<CodeExpand code={SelectedExampleSource} showBorderTop style={{marginTop: 16}}/>


## Horizontal List
Use the orientation=`horizontal` prop on the List component to display the List horizontally.
<div className="sb-unstyled"> 
    <HorizontalExample />
</div>
<CodeExpand code={HorizontalExampleSource} showBorderTop style={{marginTop: 16}}/>


## Sticky Subheader List

Use the `ListSubheader` component to create a sticky subheader in the list.
<div className="sb-unstyled"> 
    <StickyExample />
</div>
<CodeExpand code={StickyExampleSource} showBorderTop style={{marginTop: 16}}/>
