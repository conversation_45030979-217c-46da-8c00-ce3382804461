import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { Avatar } from '@hxnova/react-components/Avatar';
import { ListItem as NovaListItem, ListItemProps } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { List as NovaList } from '@hxnova/react-components/List';
import { Typography } from '@hxnova/react-components/Typography';
import Icon from '@hxnova/icons/Icon';

export default {
  title: '@hxnova/react-components/List',
  component: NovaList,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=4594-10868&t=GLVtMVxVPH63zvEu-0',
    },
  },
  tags: ['!autodocs'],
} as Meta<typeof NovaList>;

type ExtraListItemControls = {
  density: 'compact' | 'standard' | 'comfortable';
  icon: 'none' | 'icon' | 'avatar';
  primaryText?: string;
  secondaryText?: string;
};

const ListItemTemplate: StoryFn<(props: ListItemProps & ExtraListItemControls) => JSX.Element> = (args) => {
  return (
    <NovaList density={args.density}>
      <NovaListItem {...args}>
        <ListItemDecorator>
          <Icon family="material" name="person" size={24} />
        </ListItemDecorator>
        <ListItemContent>My account</ListItemContent>
        <ListItemDecorator>
          <Typography variant="bodySmall">⌘C</Typography>
          <Icon family="material" name="keyboard_arrow_right" size={24} />
        </ListItemDecorator>
      </NovaListItem>
    </NovaList>
  );
};

export const ListItem = {
  render: ListItemTemplate,
  args: {
    density: 'standard',
    disabled: false,
  },
  argTypes: {
    density: {
      control: { type: 'select' },
      options: ['compact', 'standard', 'comfortable'],
    },
  },
  parameters: {
    controls: {
      include: ['density', 'disabled'],
    },
  },
};

const ListTemplate: StoryFn<(props: ListItemProps & ExtraListItemControls) => JSX.Element> = ({
  density,
  icon,
  primaryText,
  secondaryText,
  ...args
}) => {
  return (
    <NovaList density={density} style={{ maxWidth: '200px' }}>
      <NovaListItem {...args}>
        <ListItemButton>
          {icon === 'icon' && (
            <ListItemDecorator>
              <Icon family="material" name="inbox" size={24} />
            </ListItemDecorator>
          )}
          {icon === 'avatar' && (
            <ListItemDecorator>
              <Avatar>
                <Icon family="material" name="person" size={24} />
              </Avatar>
            </ListItemDecorator>
          )}
          {(primaryText || secondaryText) && <ListItemContent primary={primaryText} secondary={secondaryText} />}
        </ListItemButton>
      </NovaListItem>
    </NovaList>
  );
};

export const List = {
  render: ListTemplate,
  args: {
    density: 'standard',
    primaryText: 'Primary',
    secondaryText: 'Secondary Text',
    icon: 'none',
    disabled: false,
  },
  argTypes: {
    density: {
      control: { type: 'select' },
      options: ['compact', 'standard', 'comfortable'],
    },
    icon: {
      control: { type: 'select' },
      options: ['none', 'icon', 'avatar'],
    },
  },
  parameters: {
    controls: {
      include: ['density', 'primaryText', 'secondaryText', 'icon', 'disabled'],
    },
  },
};
