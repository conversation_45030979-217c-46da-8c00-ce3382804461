# API Documentation

- [Breadcrumbs](#breadcrumbs)

# Breadcrumbs

API reference docs for the React Breadcrumbs component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import Breadcrumbs from '@hxnova/react-components/Breadcrumbs';
// or
import { Breadcrumbs } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component.<br>The component used for the Root slot.<br>Either a string to use a HTML element or a component. |
| **separator** | `ReactNode` | `'/'` | Custom separator node. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaBreadcrumbs-root | `'nav'` | The component that renders the root. |
| ol | .NovaBreadcrumbs-ol | `'ol'` | The component that renders the ol. |
| li | .NovaBreadcrumbs-li | `'li'` | The component that renders the li. |
| separator | .NovaBreadcrumbs-separator | `'li'` | The component that renders the separator. |

