import { useState } from 'react';
import { Breadcrumbs } from '@hxnova/react-components/Breadcrumbs';
import { Link } from '@hxnova/react-components/Link';
import { Menu } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import { Typography } from '@hxnova/react-components/Typography';
import { Button } from '@hxnova/react-components/Button';

const commonLinkStyles = {
  color: 'var(--palette-onSurfaceVariant)',
};

const primaryTextStyles = {
  color: 'var(--palette-primary)',
};

export default function CollapsedExample() {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        <MenuItem onClick={handleClose}>Breadcrumb 3</MenuItem>
        <MenuItem onClick={handleClose}>Breadcrumb 4</MenuItem>
      </Menu>
      <Breadcrumbs aria-label="breadcrumbs">
        <Link underline="hover" style={commonLinkStyles} href="#">
          Breadcrumb 1
        </Link>
        <Link underline="hover" style={commonLinkStyles} href="#">
          Breadcrumb 2
        </Link>
        <Button onClick={handleClick} variant="text" style={commonLinkStyles}>
          •••
        </Button>
        <Link underline="hover" style={commonLinkStyles} href="#">
          Breadcrumb 5
        </Link>
        <Typography style={primaryTextStyles}>Breadcrumb 6</Typography>
      </Breadcrumbs>
    </div>
  );
}
