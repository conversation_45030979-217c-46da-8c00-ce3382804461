import { Breadcrumbs } from '@hxnova/react-components/Breadcrumbs';
import { Link } from '@hxnova/react-components/Link';
import { Typography } from '@hxnova/react-components/Typography';

export default function BasicExample() {
  return (
    <Breadcrumbs aria-label="breadcrumbs">
      <Link underline="hover" style={{ color: 'var(--palette-onSurfaceVariant)' }} href="#">
        Breadcrumb 1
      </Link>
      <Typography style={{ color: 'var(--palette-primary)' }}>Breadcrumb 2</Typography>
    </Breadcrumbs>
  );
}
