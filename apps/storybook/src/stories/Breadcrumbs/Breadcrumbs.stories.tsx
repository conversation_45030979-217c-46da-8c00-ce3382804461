import { StoryFn, Meta } from '@storybook/react';
import { Breadcrumbs as NovaBreadcrumbs, BreadcrumbsProps } from '@hxnova/react-components/Breadcrumbs';
import { Typography } from '@hxnova/react-components/Typography';
import { Link } from '@hxnova/react-components/Link/Link';
import { brandBlue, grey } from '@hxnova/themes/colors';

const meta = {
  title: '@hxnova/react-components/Breadcrumbs',
  component: NovaBreadcrumbs,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1195-45244&p=f&t=Qh0I3fTEJL3cCO46-0',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof NovaBreadcrumbs>;

export default meta;

const Template: StoryFn<(props: BreadcrumbsProps) => JSX.Element> = (args) => {
  return (
    <NovaBreadcrumbs aria-label="breadcrumbs" {...args}>
      <Link underline="hover" href="#" style={{ color: grey[40] }}>
        Breadcrumb
      </Link>
      <Typography style={{ color: brandBlue[40] }}>Breadcrumb 2</Typography>
    </NovaBreadcrumbs>
  );
};

export const Breadcrumbs = {
  render: Template,
  args: {
    separator: '/',
  },
  parameters: {
    controls: {
      include: ['separator'],
    },
  },
};
