import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import * as BreadcrumbsStories from './Breadcrumbs.stories';
import { Breadcrumbs } from '@hxnova/react-components/Breadcrumbs';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import IconExample from './Examples/IconExample';
import IconExampleSource from './Examples/IconExample.tsx?raw';
import CollapsedExample from './Examples/CollapsedExample';
import CollapsedExampleSource from './Examples/CollapsedExample.tsx?raw';

<Meta title="@hxnova/react-components/Breadcrumbs/Examples" />

## Basic Usage

The `Breadcrumbs` component provides navigation with links. Use the `underline="hover"` prop on `Link` components for interactive behavior, and wrap the current page in `Typography` to show it's not clickable.

<div className="doc-story sb-story sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Breadcrumbs with Icon

The `startDecorator` prop on `Link` components allows you to add icons to your breadcrumbs. Common usage includes adding a home icon for the root level navigation.

<div className="doc-story sb-story sb-unstyled">
  <IconExample />
</div>
<CodeExpand code={IconExampleSource} showBorderTop style={{marginTop: 16}}/>

## Collapsed Breadcrumbs

For long navigation paths, use the `Menu` and `MenuItem` components to create a dropdown for collapsed items. The `anchorEl` and `open` props control the menu's visibility state.

<div className="doc-story sb-story sb-unstyled">
  <CollapsedExample />
</div>
<CodeExpand code={CollapsedExampleSource} showBorderTop style={{marginTop: 16}}/>
