import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';  
import * as FabStories from './Fab.stories';
import { Fab } from '@hxnova/react-components/Fab';
import SizeExample from './Examples/SizeExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';
import IconsExample from './Examples/IconsExample';
import IconsExampleSource from './Examples/IconsExample.tsx?raw';

<Meta title="@hxnova/react-components/Fab/Examples" />

## Fab Sizes

Fab come in three sizes: small, medium (default), and large. These can be adjusted using the `size` prop.

<div className="sb-unstyled">
    <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Fab with Icons

You can configure your Fab to show an icon on the left or right side of the fab text. These can be configured with the `startIcon` or `endIcon` props.

<div className="sb-unstyled">
    <IconsExample />
</div>
<CodeExpand code={IconsExampleSource} showBorderTop style={{marginTop: 16}}/>
