import type { Meta, StoryFn } from '@storybook/react';

import { Fab as NovaFab, FabProps } from '@hxnova/react-components/Fab';
import { Icon } from '@hxnova/icons';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: '@hxnova/react-components/Fab',
  component: NovaFab,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['!autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // argTypes: {
  //   backgroundColor: { control: 'color' },
  // },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  // args: { onClick: fn() },
} satisfies Meta<typeof NovaFab>;

export default meta;

const FabTemplate: StoryFn<
  (
    props: Omit<FabProps, 'startIcon' | 'endIcon'> & {
      showStartIcon?: boolean;
      showEndIcon?: boolean;
    },
  ) => JSX.Element
> = ({ showStartIcon, showEndIcon, ...other }) => {
  return (
    <NovaFab
      {...other}
      endIcon={showEndIcon ? <Icon family="material" name="close" /> : undefined}
      startIcon={showStartIcon ? <Icon family="material" name="edit" /> : undefined}
    />
  );
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Fab = {
  render: FabTemplate,
  args: {
    size: 'medium',
    children: 'Label',
    showStartIcon: true,
    showEndIcon: false,
  },
  parameters: {
    controls: {
      include: ['size', 'showStartIcon', 'showEndIcon', 'children'],
    },
  },
};
