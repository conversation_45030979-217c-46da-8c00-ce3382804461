import { Fab } from '@hxnova/react-components/Fab';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <Fab startIcon={<Icon family="material" name="edit" />}>Label</Fab>
      <Fab endIcon={<Icon family="material" name="close" />}>Label</Fab>
      <Fab startIcon={<Icon family="material" name="edit" />} />
    </div>
  );
}
