import { Fab } from '@hxnova/react-components/Fab';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <Fab size={'small'} startIcon={<Icon family="material" name="edit" />}>
        Label
      </Fab>
      <Fab size={'medium'} startIcon={<Icon family="material" name="edit" />}>
        Label
      </Fab>
      <Fab size={'large'} startIcon={<Icon family="material" name="edit" />}>
        Label
      </Fab>
    </div>
  );
}
