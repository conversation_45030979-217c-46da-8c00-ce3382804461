import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { IconButton as NovaIconButton } from '@hxnova/react-components/IconButton';
import Icon from '@hxnova/icons/Icon';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: '@hxnova/react-components/Icon Button',
  component: NovaIconButton,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['!autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // argTypes: {
  //   backgroundColor: { control: 'color' },
  // },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  // args: { onClick: fn() },
} satisfies Meta<typeof NovaIconButton>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const IconButton: Story = {
  args: {
    variant: 'filled',
    size: 'medium',
    disabled: false,
    children: <Icon family="material" name="edit" size={24} />,
  },
  argTypes: {
    disabled: {
      control: { type: 'boolean' },
    },
  },
  parameters: {
    controls: {
      include: ['variant', 'size', 'disabled'],
    },
  },
};
