import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import * as IconButtonStories from './IconButton.stories';
import { IconButton } from '@hxnova/react-components/IconButton';
import VariantExample from './Examples/VariantExample';
import VariantExampleSource from './Examples/VariantExample.tsx?raw';
import SizeExample from './Examples/SizeExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';

<Meta title="@hxnova/react-components/Icon Button/Examples" />

## IconButton Variants

The IconButton come in four style variations, which can be configured using the `variant` prop. The default variant is `filled`.
<div className="sb-unstyled">
    <VariantExample />
</div>
<CodeExpand code={VariantExampleSource} showBorderTop style={{marginTop: 16}}/>

## IconButton Sizes

The IconButton come in three sizes: small, medium (default), and large. These can be adjusted using the `size` prop.
<div className="sb-unstyled">
    <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>
