import { StoryFn, Meta } from '@storybook/react';
import { CssBaseline, CssBaselineProps } from '@hxnova/react-components/CssBaseline';
import { Typography } from '@hxnova/react-components/Typography';

const meta = {
  title: '@hxnova/react-components/CssBaseline',
  component: CssBaseline,
  parameters: {
    layout: 'centered',
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof CssBaseline>;

export default meta;

const Template: StoryFn<(props: CssBaselineProps) => JSX.Element> = (args) => {
  return (
    <CssBaseline {...args}>
      <div style={{ padding: '20px' }}>
        <Typography variant="titleMedium">Content with CssBaseline</Typography>
        <Typography variant="bodyMedium">
          This content is wrapped with the CssBaseline component, which provides consistent styling and resets across
          browsers.
        </Typography>
      </div>
    </CssBaseline>
  );
};

export const Default = {
  render: Template,
  argTypes: {
    children: {
      control: { type: 'label' },
    },
  },
};
