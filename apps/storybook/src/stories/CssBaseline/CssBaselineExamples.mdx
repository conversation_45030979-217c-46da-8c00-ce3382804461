import { Canvas, Meta } from '@storybook/blocks';
import { CssBaseline, Typography } from '@hxnova/react-components';

<Meta title="@hxnova/react-components/CssBaseline/Examples" />

# CSS Baseline

The `CssBaseline` component provides a consistent foundation for your application by normalizing styles across different browsers and establishing sensible defaults aligned with <PERSON>'s design system.

## Overview

`CssBaseline` is a foundational component that:

- Normalizes browser inconsistencies
- Implements Nova's base typography and color system
- Provides better default styles for HTML elements
- Ensures consistent box-sizing behavior
- Sets up responsive viewport defaults

## Basic Usage

There are two ways to use CssBaseline:

### 1. As a Wrapper Component

When you need to wrap content with a div element, use CssBaseline as a wrapper:

```tsx
import { CssBaseline } from '@hxnova/react-components';

function App() {
  return (
    <CssBaseline>
      <YourApp />
    </CssBaseline>
  );
}
```

### 2. As a Standalone Component

When you only need the global styles without wrapping content:

```tsx
import { CssBaseline } from '@hxnova/react-components';

function App() {
  return (
    <>
      <CssBaseline />
      <YourApp />
    </>
  );
}
```

> **Note**: Both approaches provide the same global styles. Choose the wrapper approach if you need the containing div element, or the standalone approach if you want to avoid extra DOM nodes.

## Key Features

### 1. Modern Box Model

Applies `border-box` sizing to ensure predictable element dimensions:

```css
*, *::before, *::after {
  box-sizing: border-box;
}
```

### 2. Responsive Viewport

Ensures proper viewport behavior on all devices:

```css
html, body {
  margin: 0;
  padding: 0;
  min-width: 100%;
  min-height: 100%;
}
```

### 3. Typography System Integration

Use `Hexagon Akkurat` font:

```css
body {
  font-family: Hexagon Akkurat, Roboto, Helvetica Neue, Arial, sans-serif;
  font-size: 1rem;
  line-height: 1.5rem;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
```

### 4. Color System Integration

Applies Nova's color system:

```css
body {
  background-color: var(--palette-surface);
  color: var(--palette-onSurface);
}
```

### 5. Element Reset

Removes default margins and styles from common elements:

```css
/* Typography elements */
h1, h2, h3, h4, h5, h6, p {
  margin: 0;
}

/* Lists */
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}
```

## Best Practices

### Do's
- ✅ Place `CssBaseline` at the root of your application
- ✅ Keep it as a single instance in your app

### Don'ts
- ❌ Don't use multiple instances of CssBaseline
- ❌ Don't skip CssBaseline when using other Nova components
- ❌ Don't place it deeply nested in your component tree
