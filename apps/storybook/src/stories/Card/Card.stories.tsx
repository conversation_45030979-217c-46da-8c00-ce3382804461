import type { Meta, StoryFn } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import { Card } from '@hxnova/react-components/Card';
import { Avatar } from '@hxnova/react-components/Avatar';
import { IconButton } from '@hxnova/react-components/IconButton';
import Icon from '@hxnova/icons/Icon';
import CardCover from '../../assets/card_cover.svg';
import avatarFirst from '../Avatar/Examples/images/avatar_first.png';
import avatarSecond from '../Avatar/Examples/images/avatar_second.png';
import avatarThird from '../Avatar/Examples/images/avatar_third.png';
import { Button } from '@hxnova/react-components/Button';
import { Tag } from '@hxnova/react-components/Tag';
import { Checkbox } from '@hxnova/react-components/Checkbox';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: '@hxnova/react-components/Card',
  component: Card.Root,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1037-456',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof Card>;

export default meta;

type CardTemplateProps = {
  orientation: 'vertical' | 'horizontal';
  clickable: boolean;
  disabled: boolean;
  showHeader: boolean;
  showHeading: boolean;
  showAvatar: boolean;
  heading: string;
  showSubheading: boolean;
  subheading: string;
  showTopActionIcon: boolean;
  showCheckBox: boolean;
  showImage: boolean;
  showTagLabel: boolean;
  showTags: boolean;
  showTitle: boolean;
  title: string;
  showSubtitle: boolean;
  subtitle: string;
  showSupportingText: boolean;
  supportingText: string;
  showSecondContentSlot: boolean;
  showActions: boolean;
  showPrimaryAction: boolean;
  showSecondaryAction: boolean;
  showTrailingActionIcon: boolean;
};

const onActionClick = (e: React.MouseEvent<HTMLElement, MouseEvent>, name: string) => {
  e.stopPropagation();
  action(name)();
};

const StackedCardTemplate: StoryFn<CardTemplateProps> = (props) => {
  const {
    orientation,
    clickable,
    disabled,
    showHeader,
    showHeading,
    showAvatar,
    heading,
    showSubheading,
    subheading,
    showTopActionIcon,
    showCheckBox,
    showImage,
    showTagLabel,
    showTags,
    showTitle,
    title,
    showSubtitle,
    subtitle,
    showSupportingText,
    supportingText,
    showSecondContentSlot,
    showActions,
    showPrimaryAction,
    showSecondaryAction,
    showTrailingActionIcon,
  } = props;
  return (
    <Card.Root
      style={{ width: '360px' }}
      orientation={orientation}
      disabled={disabled}
      onClick={clickable ? () => action('Click Card')('') : undefined}
    >
      {showHeader && (
        <Card.Header
          avatar={
            showAvatar && (
              <Avatar
                disabled={disabled}
                color={!disabled ? 'error' : undefined}
                style={
                  disabled
                    ? {
                        backgroundColor: 'var(--palette-onBackgroundDisabled)',
                        color: 'var(--palette-inverseOnSurface)',
                      }
                    : undefined
                }
              >
                AA
              </Avatar>
            )
          }
          action={
            <div style={{ display: 'flex', gap: '4px' }}>
              {showTopActionIcon && (
                <IconButton
                  variant="neutral"
                  aria-label="settings"
                  disabled={disabled}
                  onClick={(e) => onActionClick(e, 'Top Action')}
                >
                  <Icon family="material" name="more_vert" size={24} />
                </IconButton>
              )}
              {showCheckBox && <Checkbox disabled={disabled} onClick={(e) => onActionClick(e, 'Checkbox click')} />}
            </div>
          }
          heading={showHeading ? heading : undefined}
          subheading={showSubheading ? subheading : undefined}
        />
      )}
      {showImage && !showTagLabel && (
        <Card.Media
          component="img"
          image={CardCover}
          style={{ filter: disabled ? 'grayscale(100%)' : 'none', height: '156px' }}
        />
      )}
      {showImage && showTagLabel && (
        <div style={{ position: 'relative', height: '156px' }}>
          <Card.Media
            style={{
              position: 'relative',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              filter: disabled ? 'grayscale(100%)' : 'none',
            }}
            component="img"
            image={CardCover}
          />
          <Tag
            disabled={disabled}
            style={{ position: 'absolute', top: '16px', right: '24px' }}
            intensity="subtle"
            label="Tag label"
          />
        </div>
      )}
      <Card.Content
        startDecorator={
          showTags && (
            <div style={{ display: 'flex', gap: '8px' }}>
              <Tag disabled={disabled} label="Tag" variant="success" intensity="subtle" />
              <Tag disabled={disabled} label="Tag" variant="error" intensity="subtle" />
              <Tag disabled={disabled} label="Tag" variant="info" intensity="subtle" />
            </div>
          )
        }
        title={showTitle ? title : undefined}
        subtitle={showSubtitle ? subtitle : undefined}
        supportingText={showSupportingText ? supportingText : undefined}
        action={
          showTrailingActionIcon && (
            <IconButton
              disabled={disabled}
              variant="neutral"
              aria-label="settings"
              onClick={(e) => onActionClick(e, 'Trailing Action')}
            >
              <Icon family="material" name="more_vert" size={24} />
            </IconButton>
          )
        }
        endDecorator={
          showSecondContentSlot && (
            <div style={{ display: 'flex', gap: '8px' }}>
              <Avatar disabled={disabled} src={avatarFirst} />
              <Avatar disabled={disabled} src={avatarSecond} />
              <Avatar disabled={disabled} src={avatarThird} />
            </div>
          )
        }
      />
      {showActions && (
        <Card.Actions>
          {showPrimaryAction && (
            <Button disabled={disabled} variant="text" onClick={(e) => onActionClick(e, 'Primary Button Action')}>
              Button
            </Button>
          )}
          {showSecondaryAction && (
            <Button disabled={disabled} onClick={(e) => onActionClick(e, 'Secondary Button Action')}>
              Button
            </Button>
          )}
        </Card.Actions>
      )}
    </Card.Root>
  );
};

const HorizontalCardTemplate: StoryFn<CardTemplateProps> = (props) => {
  const {
    orientation,
    clickable,
    disabled,
    showAvatar,
    showCheckBox,
    showImage,
    showTags,
    showTitle,
    title,
    showSubtitle,
    subtitle,
    showSupportingText,
    supportingText,
    showSecondContentSlot,
    showActions,
    showPrimaryAction,
    showSecondaryAction,
    showTrailingActionIcon,
  } = props;
  return (
    <div style={{ width: '90vw' }}>
      <Card.Root
        style={{ height: '96px' }}
        orientation={orientation}
        disabled={disabled}
        onClick={clickable ? () => action('Click Card')('') : undefined}
      >
        {showImage && (
          <Card.Media
            component="img"
            style={{ height: '100%', maxWidth: '120px', filter: disabled ? 'grayscale(100%)' : 'none' }}
            image={CardCover}
          />
        )}
        <Card.Content
          title={showTitle ? title : undefined}
          subtitle={showSubtitle ? subtitle : undefined}
          supportingText={showSupportingText ? supportingText : undefined}
          startDecorator={
            showAvatar && (
              <Avatar
                style={
                  disabled
                    ? {
                        backgroundColor: 'var(--palette-onBackgroundDisabled)',
                        color: 'var(--palette-inverseOnSurface)',
                      }
                    : undefined
                }
                color={!disabled ? 'error' : undefined}
              >
                AA
              </Avatar>
            )
          }
          endDecorator={
            <div style={{ display: 'flex', gap: '28px', alignItems: 'center' }}>
              {showTags && (
                <div style={{ display: 'flex', gap: '8px' }}>
                  <Tag disabled={disabled} label="Tag" variant="success" intensity="subtle" />
                  <Tag disabled={disabled} label="Tag" variant="error" intensity="subtle" />
                  <Tag disabled={disabled} label="Tag" variant="info" intensity="subtle" />
                </div>
              )}
              {showSecondContentSlot && (
                <div style={{ display: 'flex', gap: '8px' }}>
                  <Avatar disabled={disabled} src={avatarFirst} />
                  <Avatar disabled={disabled} src={avatarSecond} />
                  <Avatar disabled={disabled} src={avatarThird} />
                </div>
              )}
            </div>
          }
        />
        {showActions && (
          <Card.Actions>
            {showPrimaryAction && (
              <Button disabled={disabled} variant="text" onClick={(e) => onActionClick(e, 'Primary Button Action')}>
                Button
              </Button>
            )}
            {showSecondaryAction && (
              <Button disabled={disabled} onClick={(e) => onActionClick(e, 'Secondary Button Action')}>
                Button
              </Button>
            )}
            {showTrailingActionIcon && (
              <IconButton
                disabled={disabled}
                variant="neutral"
                aria-label="settings"
                onClick={(e) => onActionClick(e, 'Trailing Action')}
              >
                <Icon family="material" name="more_vert" size={24} />
              </IconButton>
            )}
            {showCheckBox && <Checkbox disabled={disabled} onClick={(e) => onActionClick(e, 'Checkbox click')} />}
          </Card.Actions>
        )}
      </Card.Root>
    </div>
  );
};

const argsTypes = {
  showAvatar: {
    name: 'Show avatar',
    control: {
      type: 'boolean',
    },
  },
  showCheckBox: {
    name: 'Show checkbox',
    control: {
      type: 'boolean',
    },
  },
  showImage: {
    name: 'Show image',
    control: {
      type: 'boolean',
    },
  },
  showTags: {
    name: 'Show tags',
    control: {
      type: 'boolean',
    },
  },
  showTitle: {
    name: 'Show title',
    control: {
      type: 'boolean',
    },
  },
  showSubtitle: {
    name: 'Show subtitle',
    control: {
      type: 'boolean',
    },
  },
  showSupportingText: {
    name: 'Show supporting text',
    control: {
      type: 'boolean',
    },
  },
  showSecondContentSlot: {
    name: 'Show second content slot',
    control: {
      type: 'boolean',
    },
  },
  showActions: {
    name: 'Show actions',
    control: {
      type: 'boolean',
    },
  },
  showPrimaryAction: {
    name: 'Show primary action',
    control: {
      type: 'boolean',
    },
  },
  showSecondaryAction: {
    name: 'Show secondary action',
    control: {
      type: 'boolean',
    },
  },
  showTrailingActionIcon: {
    name: 'Show trailing action icon',
    control: {
      type: 'boolean',
    },
  },
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const StackedCard = {
  render: StackedCardTemplate,
  args: {
    orientation: 'vertical',
    clickable: false,
    disabled: false,
    showHeader: true,
    showAvatar: true,
    showHeading: true,
    heading: 'Heading',
    showSubheading: true,
    subheading: 'Subheading',
    showTopActionIcon: true,
    showCheckBox: true,
    showImage: true,
    showTagLabel: true,
    showTags: true,
    showTitle: true,
    title: 'Title',
    showSubtitle: true,
    subtitle: 'Subtitle',
    showSupportingText: true,
    supportingText: 'Supporting text',
    showSecondContentSlot: true,
    showActions: true,
    showPrimaryAction: true,
    showSecondaryAction: true,
    showTrailingActionIcon: true,
  },
  argTypes: {
    ...argsTypes,
    showHeader: {
      name: 'Show header',
      control: {
        type: 'boolean',
      },
    },
    showHeading: {
      name: 'Show heading',
      control: {
        type: 'boolean',
      },
    },
    showSubheading: {
      name: 'Show subheading',
      control: {
        type: 'boolean',
      },
    },
    showTopActionIcon: {
      name: 'Show top action icon',
      control: {
        type: 'boolean',
      },
    },
    showTagLabel: {
      name: 'Show image tag',
      control: {
        type: 'boolean',
      },
    },
  },
};

export const HorizontalCard = {
  render: HorizontalCardTemplate,
  args: {
    orientation: 'horizontal',
    clickable: false,
    disabled: false,
    showAvatar: true,
    showCheckBox: true,
    showImage: true,
    showTags: true,
    showTitle: true,
    title: 'Title',
    showSubtitle: true,
    subtitle: 'Subtitle',
    showSupportingText: true,
    supportingText: 'Supporting text',
    showSecondContentSlot: true,
    showActions: true,
    showPrimaryAction: true,
    showSecondaryAction: true,
    showTrailingActionIcon: true,
  },
  argTypes: {
    ...argsTypes,
  },
};
