import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import StackCardExampleSource from './Examples/StackCardExample.tsx?raw';
import StackCardExample from './Examples/StackCardExample';
import HorizontalCardExampleSource from './Examples/HorizontalCardExample.tsx?raw';
import HorizontalCardExample from './Examples/HorizontalCardExample';
import StackCardOne from './Examples/StackCardOne';
import StackCardOneSource from './Examples/StackCardOne.tsx?raw';
import StackCardTwo from './Examples/StackCardTwo';
import StackCardTwoSource from './Examples/StackCardTwo.tsx?raw';
import StackCardThree from './Examples/StackCardThree';
import StackCardThreeSource from './Examples/StackCardThree.tsx?raw';
import StackCardFour from './Examples/StackCardFour';
import StackCardFourSource from './Examples/StackCardFour.tsx?raw';
import HorizontalCardOne from './Examples/HorizontalCardOne';
import HorizontalCardOneSource from './Examples/HorizontalCardOne.tsx?raw';
import HorizontalCardTwo from './Examples/HorizontalCardTwo';
import HorizontalCardTwoSource from './Examples/HorizontalCardTwo.tsx?raw';
import HorizontalCardThree from './Examples/HorizontalCardThree';
import HorizontalCardThreeSource from './Examples/HorizontalCardThree.tsx?raw';
import HorizontalCardFour from './Examples/HorizontalCardFour';
import HorizontalCardFourSource from './Examples/HorizontalCardFour.tsx?raw';
import ClickableCard from './Examples/ClickableCard';
import ClickableCardSource from './Examples/ClickableCard.tsx?raw';
import DisabledCard from './Examples/DisabledCard';
import DisabledCardSource from './Examples/DisabledCard.tsx?raw';

<Meta title="@hxnova/react-components/Card/Examples" />

## Introduction

Cards are versatile surfaces designed to present header, media, content and actions related to a single topic. The Nova Card component features several complementary utility components for a variety of use cases:

* `Card.Root`: A surface-level container that groups related components.
* `Card.Header`: An optional wrapper for the card's header.
* `Card.Content`: An optional wrapper for the card's main content.
* `Card.Media`: An optional container for images, videos, and other media.
* `Card.Actions`: An optional wrapper that organizes a set of action buttons.

## Stacked Card

By default, the cards are laid out vertically. The following example shows variations of stacked cards.

### Basic stacked card

The following example shows how to lay out a basic stacked card using `CardContent` and `CardActions` components.

<div className="sb-unstyled">
  <StackCardOne />
</div>
<CodeExpand code={ StackCardOneSource } showBorderTop style={{marginTop: 16}}/>

### Stacked card with header

The following example shows how to lay out a stacked card with card header using `CardHeader` and `CardContent` components.

<div className="sb-unstyled">
  <StackCardTwo />
</div>
<CodeExpand code={ StackCardTwoSource } showBorderTop style={{marginTop: 16}}/>

### Stacked card with image

The following example shows how to lay out a stacked card with image using `CardMedia` and `CardContent` components.

<div className="sb-unstyled">
  <StackCardThree />
</div>
<CodeExpand code={ StackCardThreeSource } showBorderTop style={{marginTop: 16}}/>

### Stacked card with tags

The following example show how to lay out a stacked card with tags using `CardMedia`, `CardContent` and `CardActions` components.

<div className="sb-unstyled">
  <StackCardFour />
</div>
<CodeExpand code={ StackCardFourSource } showBorderTop style={{marginTop: 16}}/>

### Complex stack card

The following example shows a complex card.

<div className="sb-unstyled">
  <StackCardExample />
</div>
<CodeExpand code={StackCardExampleSource} showBorderTop style={{marginTop: 16}}/>

## Horizontal Card

You can pass `orientation="horizontal"` to lay out the cards horizontally. The following example shows variations  of horizontal cards.

### Basic horizontal card

The following example shows how to lay out a basic horizontal card using `CardContent` and `CardActions` components.

<div className="sb-unstyled">
  <HorizontalCardOne />
</div>
<CodeExpand code={ HorizontalCardOneSource } showBorderTop style={{marginTop: 16}}/>

### Horizontal card with image

The following example shows how to lay out a horizontal card with image using `CardMedia`, `CardContent` and `CardActions` components.

<div className="sb-unstyled">
  <HorizontalCardTwo />
</div>
<CodeExpand code={ HorizontalCardTwoSource } showBorderTop style={{marginTop: 16}}/>

### Horizontal card with avatar

The following example shows how to lay out a horizontal card with avatar using `CardContent` and `CardActions` components.

<div className="sb-unstyled">
  <HorizontalCardThree />
</div>
<CodeExpand code={ HorizontalCardThreeSource } showBorderTop style={{marginTop: 16}}/>

### Horizontal card with tags and actions

The following example shows how to lay out a horizontal card with tags and actions using `CardMedia`, `CardContent` and `CardActions` components.

<div className="sb-unstyled">
  <HorizontalCardFour />
</div>
<CodeExpand code={ HorizontalCardFourSource } showBorderTop style={{marginTop: 16}}/>

### Complex horizontal card

The following example shows a complex horizontal card.

<div className="sb-unstyled">
  <HorizontalCardExample />
</div>
<CodeExpand code={HorizontalCardExampleSource} showBorderTop style={{marginTop: 16}}/>

## Clickable Card

You can pass `onClick` prop to the `Card` component to make the entire card clickable.

<div className="sb-unstyled">
  <ClickableCard />
</div>
<CodeExpand code={ ClickableCardSource } showBorderTop style={{marginTop: 16}}/>


## Disabled Card

You can pass the `disabled` prop to the `Card` component to make the entire card disabled. For custom `ReactNode`, such as action, you need to control its disabled state separately.

<div className="sb-unstyled">
  <DisabledCard />
</div>
<CodeExpand code={ DisabledCardSource } showBorderTop style={{marginTop: 16}}/>




