import React from 'react';
import { Card } from '@hxnova/react-components/Card';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Tag } from '@hxnova/react-components/Tag';
import Icon from '@hxnova/icons/Icon';

export default function Example() {
  return (
    <Card.Root orientation="horizontal" style={{ height: '96px' }}>
      <Card.Media
        style={{ width: '100px', height: '100%' }}
        component="img"
        image={'https://cdn.sanity.io/images/eqlh3dcx/dev/c2ab4812a5485ba4a15aa8f4517b94edb5728c1d-1142x643.png'}
      />
      <Card.Content
        title={'Block engine'}
        subtitle={'Additive manufacturing'}
        supportingText={'Created 3rd November'}
        endDecorator={
          <div style={{ display: 'flex', gap: '8px' }}>
            <Tag label="Tag" variant="success" intensity="subtle" />
            <Tag label="Tag" variant="error" intensity="subtle" />
            <Tag label="Tag" variant="info" intensity="subtle" />
          </div>
        }
      />
      <Card.Actions>
        <IconButton variant="standard" aria-label="settings">
          <Icon family="material" name="group" size={24} />
        </IconButton>
        <IconButton variant="standard" aria-label="settings">
          <Icon family="material" name="ios_share" size={24} />
        </IconButton>
      </Card.Actions>
    </Card.Root>
  );
}
