import React from 'react';
import { Card } from '@hxnova/react-components/Card';
import { Checkbox } from '@hxnova/react-components/Checkbox';

export default function Example() {
  return (
    <Card.Root orientation="horizontal" style={{ width: '400px', height: '80px' }}>
      <Card.Media
        style={{ width: '100px', height: '100%' }}
        component="img"
        image={'https://cdn.sanity.io/images/eqlh3dcx/dev/c2ab4812a5485ba4a15aa8f4517b94edb5728c1d-1142x643.png'}
      />
      <Card.Content title={'Project name'} supportingText={'10MB'} />
      <Card.Actions>
        <Checkbox />
      </Card.Actions>
    </Card.Root>
  );
}
