import React from 'react';
import { Card } from '@hxnova/react-components/Card';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Button } from '@hxnova/react-components/Button';

export default function Example() {
  return (
    <Card.Root orientation="horizontal" style={{ width: '400px' }}>
      <Card.Content startDecorator={<Avatar>AA</Avatar>} title={'Title'} supportingText={'Supporting text'} />
      <Card.Actions>
        <Button variant="text">Cancel request</Button>
      </Card.Actions>
    </Card.Root>
  );
}
