# API Documentation

- [Card.Actions](#cardactions)
- [Card.Content](#cardcontent)
- [Card.Header](#cardheader)
- [Card.Media](#cardmedia)
- [Card.Root](#cardroot)

# Card.Actions

API reference docs for the React Card.Actions component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import CardActions from '@hxnova/react-components/CardActions';
// or
import { CardActions } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **id** | `string` | - |  |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCardActions-root | `'div'` | The component that renders the root slot. |

<br><br>

# Card.Content

API reference docs for the React Card.Content component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import CardContent from '@hxnova/react-components/CardContent';
// or
import { CardContent } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **action** | `ReactNode` | - | The action to display in the card header. |
| **endDecorator** | `ReactNode` | - | The end decorator of the component. |
| **orientation** | ``"vertical" \| "horizontal"`` | `'vertical'` | The orientation of the card content. |
| **startDecorator** | `ReactNode` | - | The start decorator of the component. |
| **subtitle** | `ReactNode` | - | The header of the component. |
| **supportingText** | `ReactNode` | - | The subheader of the component. |
| **title** | `ReactNode` | - | The Avatar element to display. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCardContent-root | `'div'` | The component that renders the root slot. |
| body | .NovaCardContent-body | `'div'` | The component that renders the body slot. |
| title | .NovaCardContent-title | `Typography` | The component that renders the title slot. |
| subtitle | .NovaCardContent-subtitle | `Typography` | The component that renders the subtitle slot. |
| supportingText | .NovaCardContent-supportingText | `Typography` | The component that renders the supportingText slot. |
| action | .NovaCardContent-action | `'div'` | The component that renders the action slot. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCardContent-bodyMain | `bodyMain` | Class name applied to the bodyMain element. |
| .NovaCardContent-orientationVertical | `orientationVertical` | Class name applied to the root element if `orientation="vertical"`. |
| .NovaCardContent-orientationHorizontal | `orientationHorizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaCardContent-disabled | `disabled` | Class name applied to the title, subtitle and supportingText elements if `disabled={true}`. |

<br><br>

# Card.Header

API reference docs for the React Card.Header component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import CardHeader from '@hxnova/react-components/CardHeader';
// or
import { CardHeader } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **action** | `ReactNode` | - | The action to display in the card header. |
| **avatar** | `ReactNode` | - | The Avatar element to display. |
| **heading** | `ReactNode` | - | The heading of the component. |
| **subheading** | `ReactNode` | - | The subheading of the component. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCardHeader-root | `'div'` | The component that renders the root slot. |
| avatar | .NovaCardHeader-avatar | `'div'` | The component that renders the avatar slot. |
| action | .NovaCardHeader-action | `'div'` | The component that renders the action slot. |
| content | .NovaCardHeader-content | `'div'` | The component that renders the content slot. |
| heading | .NovaCardHeader-heading | `Typography` | The component that renders the heading slot. |
| subheading | .NovaCardHeader-subheading | `Typography` | The component that renders the subheading slot. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCardHeader-disabled | `disabled` | Class name applied to the heading and subheading element if `disabled={true}`. |

<br><br>

# Card.Media

API reference docs for the React Card.Media component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import CardMedia from '@hxnova/react-components/CardMedia';
// or
import { CardMedia } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **image** | `string` | - | Image to be displayed as a background image.<br>Either `image` or `src` prop must be specified.<br>Note that caller must specify height otherwise the image will not be visible. |
| **src** | `string` | - | An alias for `image` property.<br>Available only with media components.<br>Media components: `video`, `audio`, `picture`, `iframe`, `img`. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCardMedia-root | `'div'` |  |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCardMedia-media | `media` | Class name applied to the root element if `component="video, audio, picture, iframe, or img"`. |
| .NovaCardMedia-img | `img` | Class name applied to the root element if `component="picture or img"`. |

<br><br>

# Card.Root

API reference docs for the React Card.Root component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import CardRoot from '@hxnova/react-components/CardRoot';
// or
import { CardRoot } from '@hxnova/react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **disabled** | ``false \| true`` | `false` | Indicate the disabled state of the card. |
| **orientation** | ``"vertical" \| "horizontal"`` | `'vertical'` | The orientation of the card. |

## Slots

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCardRoot-root | `'div'` | The component that renders the root slot. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCardRoot-orientationVertical | `orientationVertical` | Class name applied to the root element if `orientation="vertical"`. |
| .NovaCardRoot-orientationHorizontal | `orientationHorizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaCardRoot-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |

