{"name": "nextjs-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18", "next": "14.1.3", "@hxnova/react-components": "workspace:*", "@hxnova/themes": "workspace:*", "@hxnova/icons": "workspace:*", "@pigment-css/react": "^0.0.30", "@nexusui/branding": "^2.8.1", "@fontsource/roboto": "^5.0.12", "@fontsource/roboto-mono": "^5.0.17"}, "devDependencies": {"typescript": "^5.4.2", "@types/node": "^22.10.2", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.0.1", "postcss": "^8", "@pigment-css/nextjs-plugin": "^0.0.30"}}