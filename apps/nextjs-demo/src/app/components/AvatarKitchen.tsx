import { Avatar, AvatarProps } from '@hxnova/react-components/Avatar';
import { AvatarGroup } from '@hxnova/react-components/AvatarGroup';
import Icon from '@hxnova/icons/Icon';

export default function AvatarKitchen() {
  const sizes = ['small', 'medium', 'large'];
  const avatarTypes = ['image', 'icon', 'text'];
  const colors = ['error', 'primary', 'warning', 'info', 'success'];
  const iconSize = { small: 20, medium: 24, large: 28 };
  return (
    <div>
      <div style={{ marginLeft: '30px' }}>
        {sizes.map((size) => (
          <div key={size} style={{ display: 'flex', flexDirection: 'column' }}>
            {avatarTypes.map((type) => (
              <div key={`${size}-${type}`} style={{ display: 'flex', flexDirection: 'row' }}>
                {colors.map((color) => (
                  <div
                    key={`${size}-${type}-color:${color}`}
                    style={{ position: 'relative', height: '100px', width: '220px', gap: '16px' }}
                  >
                    <Avatar
                      src={type === 'image' ? '/images/avatar.jpeg' : undefined}
                      size={size as AvatarProps['size']}
                      color={color as AvatarProps['color']}
                    >
                      {type === 'text' && 'JB'}
                      {type === 'icon' && (
                        <Icon
                          family="material"
                          name={'person'}
                          size={iconSize[(size as AvatarProps['size']) ?? 'medium']}
                        />
                      )}
                    </Avatar>

                    <div
                      style={{
                        position: 'absolute',
                        top: '50px',
                        fontSize: '14px',
                        left: '-30px',
                        whiteSpace: 'pre-wrap',
                      }}
                    >
                      {`${size}-${type}\ncolor:${color}-status:${color}`}
                    </div>
                  </div>
                ))}
                <div
                  key={`${size}-${type}-disabled`}
                  style={{ position: 'relative', height: '100px', width: '220px', gap: '16px' }}
                >
                  <Avatar src={type === 'image' ? '/images/avatar.jpeg' : undefined} color={'primary'} disabled>
                    {type === 'text' && 'JB'}
                    {type === 'icon' && (
                      <Icon
                        family="material"
                        name={'person'}
                        size={iconSize[(size as AvatarProps['size']) ?? 'medium']}
                      />
                    )}
                  </Avatar>
                  <div
                    style={{
                      position: 'absolute',
                      top: '50px',
                      fontSize: '14px',
                      whiteSpace: 'pre-wrap',
                    }}
                  >
                    {`${size}-${type}\ndisabled`}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>

      <div style={{ marginTop: '30px' }}>
        {sizes.map((size) => (
          <div key={size} style={{ display: 'flex', flexDirection: 'row' }}>
            {avatarTypes.map((type) => (
              <div
                key={`${size}-${type}`}
                style={{ display: 'flex', flexDirection: 'column', height: '180px', width: '220px', gap: '16px' }}
              >
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <AvatarGroup>
                    {Array.from(new Array(3).keys()).map((i) => (
                      <Avatar
                        key={i}
                        src={type === 'image' ? '/images/avatar.jpeg' : undefined}
                        size={size as AvatarProps['size']}
                        color="error"
                      >
                        {type === 'text' && 'JB'}
                        {type === 'icon' && (
                          <Icon
                            family="material"
                            name={'person'}
                            size={iconSize[(size as AvatarProps['size']) ?? 'medium']}
                          />
                        )}
                      </Avatar>
                    ))}
                  </AvatarGroup>
                  <div
                    style={{
                      fontSize: '14px',
                    }}
                  >{`${size}-${type}-color:error`}</div>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <AvatarGroup disabled>
                    {Array.from(new Array(3).keys()).map((i) => (
                      <Avatar
                        key={i}
                        src={type === 'image' ? '/images/avatar.jpeg' : undefined}
                        size={size as AvatarProps['size']}
                        color="error"
                      >
                        {type === 'text' && 'JB'}
                        {type === 'icon' && (
                          <Icon
                            family="material"
                            name={'person'}
                            size={iconSize[(size as AvatarProps['size']) ?? 'medium']}
                          />
                        )}
                      </Avatar>
                    ))}
                  </AvatarGroup>
                  <div
                    style={{
                      fontSize: '14px',
                      marginTop: '16px',
                      whiteSpace: 'pre-wrap',
                    }}
                  >{`${size}-${type}\ncolor:error-status:error-disabled`}</div>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}
