import { Divider } from '@hxnova/react-components/Divider';

export default function FabKitchen() {
  return (
    <div style={{ margin: '20px', display: 'flex' }}>
      <div>
        <div
          style={{
            fontSize: '14px',
            whiteSpace: 'pre-wrap',
          }}
        >
          {`vertical Divider`}
        </div>
        <div
          style={{
            display: 'flex',
            width: '300px',
            height: '300px',
          }}
        >
          <Divider orientation="vertical" />
        </div>
      </div>

      <div>
        <div
          style={{
            fontSize: '14px',
            whiteSpace: 'pre-wrap',
          }}
        >
          <div
            style={{
              display: 'flex',
              width: '300px',
              height: '300px',
            }}
          >
            {`horizontal Divider`}
          </div>
          <Divider orientation="horizontal" />
        </div>
      </div>
    </div>
  );
}
