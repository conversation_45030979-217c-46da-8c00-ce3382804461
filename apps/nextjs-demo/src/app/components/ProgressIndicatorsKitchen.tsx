'use client';
import * as React from 'react';
import { CircularProgress } from '@hxnova/react-components/CircularProgress';
import { LinearProgress } from '@hxnova/react-components/LinearProgress';

const headingStyle = {
  fontWeight: 600,
  marginBottom: '16px',
};

const subtitleStyle = {
  fontWeight: 500,
  marginBottom: '8px',
  fontSize: '16px',
};

export default function ProgressIndicatorsKitchen() {
  const [progress, setProgress] = React.useState(0);

  React.useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prevProgress) => {
        if (prevProgress >= 100) {
          return 0;
        }
        return prevProgress + 10;
      });
    }, 800);

    return () => {
      clearInterval(timer);
    };
  }, []);

  return (
    <div style={{ height: '100%', width: '100%' }}>
      <div style={{ padding: '32px' }}>
        <div style={{ fontSize: '24px', ...headingStyle }}>Progress Indicators</div>

        <div style={{ marginBottom: '48px' }}>
          <div style={{ fontSize: '20px', ...headingStyle }}>Circular Progress</div>

          <div style={{ display: 'flex', flexDirection: 'row', gap: '32px', marginBottom: '32px' }}>
            <div>
              <div style={subtitleStyle}>Indeterminate</div>
              <CircularProgress />
            </div>

            <div>
              <div style={subtitleStyle}>Determinate ({progress}%)</div>
              <CircularProgress variant="determinate" value={progress} />
            </div>

            <div>
              <div style={subtitleStyle}>With different sizes</div>
              <div style={{ display: 'flex', flexDirection: 'row', gap: '24px', alignItems: 'center' }}>
                <CircularProgress size={24} />
                <CircularProgress size={40} />
                <CircularProgress size={56} />
              </div>
            </div>
          </div>

          <div style={{ display: 'flex', flexDirection: 'row', gap: '32px' }}>
            <div>
              <div style={subtitleStyle}>With different colors</div>
              <div style={{ display: 'flex', flexDirection: 'row', gap: '24px' }}>
                <CircularProgress color="primary" />
                <CircularProgress color="error" />
                <CircularProgress color="success" />
                <CircularProgress color="info" />
                <CircularProgress color="warning" />
              </div>
            </div>

            <div>
              <div style={subtitleStyle}>With different thickness</div>
              <div style={{ display: 'flex', flexDirection: 'row', gap: '16px' }}>
                <CircularProgress thickness={2} />
                <CircularProgress thickness={4} />
                <CircularProgress thickness={6} />
              </div>
            </div>
          </div>
        </div>

        <div style={{ marginBottom: '32px' }}>
          <div style={{ fontSize: '20px', ...headingStyle }}>Linear Progress</div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '32px', maxWidth: '600px' }}>
            <div>
              <div style={subtitleStyle}>Indeterminate</div>
              <LinearProgress />
            </div>

            <div>
              <div style={subtitleStyle}>Determinate ({progress}%)</div>
              <LinearProgress variant="determinate" value={progress} />
            </div>

            <div>
              <div style={subtitleStyle}>With different colors</div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                <LinearProgress color="primary" />
                <LinearProgress color="error" />
                <LinearProgress color="success" />
                <LinearProgress color="info" />
                <LinearProgress color="warning" />
              </div>
            </div>

            <div>
              <div style={subtitleStyle}>With different thickness</div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                <LinearProgress thickness={2} />
                <LinearProgress thickness={4} />
                <LinearProgress thickness={8} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
