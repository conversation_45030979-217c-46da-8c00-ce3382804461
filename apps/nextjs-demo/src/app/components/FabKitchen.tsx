import { FabProps, Fab, Typography } from '@hxnova/react-components';
import { Icon } from '@hxnova/icons';

export default function FabKitchen() {
  return (
    <div style={{ margin: '20px', display: 'flex', flexDirection: 'row', gap: '30px' }}>
      {(['small', 'medium', 'large'] as Array<FabProps['size']>).map((size) => (
        <div key={size}>
          <Typography
            variant={'labelMedium'}
            sx={(theme) => ({ display: 'block', marginBottom: '12px', color: theme.vars.palette.onSurface })}
          >
            {size}
          </Typography>
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: '20px',
            }}
          >
            <Fab key={size} size={size} startIcon={<Icon family="material" name="edit" />}>
              Label
            </Fab>
          </div>
        </div>
      ))}
    </div>
  );
}
