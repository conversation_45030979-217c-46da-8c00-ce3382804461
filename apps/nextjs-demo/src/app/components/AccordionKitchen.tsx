import { useState } from 'react';
import { Accordion } from '@hxnova/react-components/Accordion';
import { Typography } from '@hxnova/react-components/Typography';

export default function AccordionKitchen() {
  const [expanded, setExpanded] = useState<string | false>(false);

  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <div
      sx={(theme) => ({
        display: 'flex',
        flexDirection: 'column',
        gap: '24px',
        padding: '16px',
        color: theme.vars.palette.onSurface,
      })}
    >
      <div
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: '24px',
          maxWidth: '1200px',
          margin: '0 auto',
        }}
      >
        <div>
          <Typography variant="headlineMedium" sx={{ marginBottom: '16px' }}>
            Basic Accordion
          </Typography>
          <Accordion.Group>
            <Accordion.Item>
              <Accordion.Summary>First accordion</Accordion.Summary>
              <Accordion.Details>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore magna aliqua.
              </Accordion.Details>
            </Accordion.Item>
            <Accordion.Item>
              <Accordion.Summary>Second accordion</Accordion.Summary>
              <Accordion.Details>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore magna aliqua.
              </Accordion.Details>
            </Accordion.Item>
            <Accordion.Item>
              <Accordion.Summary>Third accordion</Accordion.Summary>
              <Accordion.Details>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore magna aliqua.
              </Accordion.Details>
            </Accordion.Item>
          </Accordion.Group>
        </div>

        <div>
          <Typography variant="headlineMedium" sx={{ marginBottom: '16px' }}>
            Controlled Accordion
          </Typography>
          <Accordion.Group>
            <Accordion.Item expanded={expanded === 'panel1'} onChange={handleChange('panel1')}>
              <Accordion.Summary>Controlled Panel 1</Accordion.Summary>
              <Accordion.Details>
                This is a controlled accordion panel. It can be expanded or collapsed programmatically.
              </Accordion.Details>
            </Accordion.Item>
            <Accordion.Item expanded={expanded === 'panel2'} onChange={handleChange('panel2')}>
              <Accordion.Summary>Controlled Panel 2</Accordion.Summary>
              <Accordion.Details>
                Only one panel can be expanded at a time in this controlled example.
              </Accordion.Details>
            </Accordion.Item>
            <Accordion.Item expanded={expanded === 'panel3'} onChange={handleChange('panel3')}>
              <Accordion.Summary>Controlled Panel 3</Accordion.Summary>
              <Accordion.Details>
                Try clicking different panels to see how they interact with each other.
              </Accordion.Details>
            </Accordion.Item>
          </Accordion.Group>
        </div>

        <div>
          <Typography variant="headlineMedium" sx={{ marginBottom: '16px' }}>
            Disabled Accordion
          </Typography>
          <Accordion.Group>
            <Accordion.Item>
              <Accordion.Summary>Enabled accordion</Accordion.Summary>
              <Accordion.Details>This accordion can be expanded and collapsed normally.</Accordion.Details>
            </Accordion.Item>
            <Accordion.Item disabled>
              <Accordion.Summary>Disabled accordion</Accordion.Summary>
              <Accordion.Details>This accordion is disabled and cannot be interacted with.</Accordion.Details>
            </Accordion.Item>
            <Accordion.Item>
              <Accordion.Summary>Another enabled accordion</Accordion.Summary>
              <Accordion.Details>
                This accordion demonstrates the contrast between enabled and disabled states.
              </Accordion.Details>
            </Accordion.Item>
          </Accordion.Group>
        </div>

        <div>
          <Typography variant="headlineMedium" sx={{ marginBottom: '16px' }}>
            Compact Accordion
          </Typography>
          <Accordion.Group density="compact">
            <Accordion.Item>
              <Accordion.Summary>Compact accordion 1</Accordion.Summary>
              <Accordion.Details>
                This accordion uses the compact variant for more compact presentation.
              </Accordion.Details>
            </Accordion.Item>
            <Accordion.Item>
              <Accordion.Summary>Compact accordion 2</Accordion.Summary>
              <Accordion.Details>
                The compact variant is useful when space is limited or for information-dense interfaces.
              </Accordion.Details>
            </Accordion.Item>
          </Accordion.Group>
        </div>

        <div>
          <Typography variant="headlineMedium" sx={{ marginBottom: '16px' }}>
            Comfortable Accordion
          </Typography>
          <Accordion.Group density="comfortable">
            <Accordion.Item>
              <Accordion.Summary>Comfortable accordion 1</Accordion.Summary>
              <Accordion.Details>
                This accordion uses the comfortable variant for more comfortable presentation.
              </Accordion.Details>
            </Accordion.Item>
            <Accordion.Item>
              <Accordion.Summary>Comfortable accordion 2</Accordion.Summary>
              <Accordion.Details>
                The comfortable variant is useful when space is limited or for information-dense interfaces.
              </Accordion.Details>
            </Accordion.Item>
          </Accordion.Group>
        </div>

        <div>
          <Typography variant="headlineMedium" sx={{ marginBottom: '16px' }}>
            Without Dividers
          </Typography>
          <Accordion.Group disableDivider>
            <Accordion.Item>
              <Accordion.Summary>No divider 1</Accordion.Summary>
              <Accordion.Details>This accordion group has dividers disabled.</Accordion.Details>
            </Accordion.Item>
            <Accordion.Item>
              <Accordion.Summary>No divider 2</Accordion.Summary>
              <Accordion.Details>Notice how there are no lines between the accordion items.</Accordion.Details>
            </Accordion.Item>
          </Accordion.Group>
        </div>
      </div>
    </div>
  );
}
