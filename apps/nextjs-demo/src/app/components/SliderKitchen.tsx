import * as React from 'react';
import { Slider } from '@hxnova/react-components/Slider';

export default function Slider<PERSON>itchen() {
  const [value, setValue] = React.useState<number[]>([-50, 0]);

  const handleChange = (event: Event, newValue: number | number[]) => {
    setValue(newValue as number[]);
  };

  return (
    <div
      sx={(theme) => ({
        display: 'flex',
        flexDirection: 'column',
        gap: '24px',
        padding: '16px',
        color: theme.vars.palette.onSurfaceVariant,
        '& .NovaSlider-thumb': { '--nova-slider-thumbColor': 'var(--palette-backgroundDisabled)' },
      })}
    >
      <div style={{ display: 'flex', flexDirection: 'row', gap: '24px', flexWrap: 'wrap' }}>
        {/* Continuous */}
        <div style={{ flex: 1, minWidth: '354px' }}>
          <h3>Continuous</h3>
          <div style={{ width: '100%' }}>
            <div style={{ marginBottom: '16px' }}>
              <Slider defaultValue={0} valueLabelDisplay="auto" marks={[{ value: 100, label: '' }]} />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Slider defaultValue={50} valueLabelDisplay="auto" marks={[{ value: 100, label: '' }]} />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Slider defaultValue={100} valueLabelDisplay="auto" />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Slider defaultValue={30} disabled marks={[{ value: 100, label: '' }]} />
            </div>
          </div>
        </div>

        {/* Centered */}
        <div style={{ flex: 1, minWidth: '354px' }}>
          <h3>Centered</h3>
          <div style={{ width: '100%' }}>
            <div style={{ marginBottom: '16px' }}>
              <Slider
                track={false}
                defaultValue={0}
                min={-100}
                max={100}
                marks={[
                  { value: -100, label: '' },
                  { value: 0, label: '' },
                  { value: 100, label: '' },
                ]}
                valueLabelDisplay="auto"
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Slider
                track={false}
                defaultValue={-50}
                min={-100}
                max={100}
                marks={[
                  { value: -100, label: '' },
                  { value: 0, label: '' },
                  { value: 100, label: '' },
                ]}
                valueLabelDisplay="auto"
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Slider
                track={false}
                defaultValue={50}
                min={-100}
                max={100}
                marks={[
                  { value: -100, label: '' },
                  { value: 0, label: '' },
                  { value: 100, label: '' },
                ]}
                valueLabelDisplay="auto"
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Slider
                defaultValue={0}
                min={-100}
                max={100}
                disabled
                track={false}
                marks={[
                  { value: -100, label: '' },
                  { value: 0, label: '' },
                  { value: 100, label: '' },
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      <div style={{ display: 'flex', flexDirection: 'row', gap: '24px', flexWrap: 'wrap' }}>
        {/* Discrete */}
        <div style={{ flex: 1, minWidth: '354px' }}>
          <h3>Discrete</h3>
          <div style={{ width: '100%', marginBottom: '16px' }}>
            <Slider defaultValue={0} step={10} marks valueLabelDisplay="auto" />
          </div>
          <div style={{ width: '100%', marginBottom: '16px' }}>
            <Slider aria-label="Small steps" defaultValue={40} step={10} marks valueLabelDisplay="auto" />
          </div>
          <div style={{ width: '100%', marginBottom: '16px' }}>
            <Slider defaultValue={100} step={10} marks valueLabelDisplay="auto" />
          </div>
          <div style={{ width: '100%', marginBottom: '16px' }}>
            <Slider defaultValue={50} disabled step={10} marks />
          </div>
        </div>
        {/* Range */}
        <div style={{ flex: 1, minWidth: '354px' }}>
          <h3>Range</h3>
          <div style={{ width: '100%', marginBottom: '16px' }}>
            <Slider
              value={value}
              onChange={handleChange}
              min={-100}
              max={100}
              step={10}
              marks={[
                { value: -100, label: '' },
                { value: 100, label: '' },
              ]}
              valueLabelDisplay="auto"
            />
          </div>
          <div style={{ width: '100%', marginBottom: '16px' }}>
            <Slider
              value={value}
              onChange={handleChange}
              min={-100}
              max={100}
              disabled
              marks={[
                { value: -100, label: '' },
                { value: 100, label: '' },
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
