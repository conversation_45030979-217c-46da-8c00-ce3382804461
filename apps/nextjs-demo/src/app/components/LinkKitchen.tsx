import { Link } from '@hxnova/react-components/Link';
import Icon from '@hxnova/icons/Icon';
export default function Link<PERSON>itchen() {
  return (
    <div style={{ margin: '20px', display: 'flex', flexDirection: 'row', gap: '30px' }}>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          gap: '20px',
        }}
      >
        <Link href="https://www.baidu.com/" startDecorator={<Icon family="material" name="edit" />}>
          Link
        </Link>
        <Link href="https://www.baidu.com/" endDecorator={<Icon family="material" name="arrow_forward" />}>
          Link
        </Link>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          gap: '20px',
        }}
      >
        <Link href="https://www.baidu.com/" disabled startDecorator={<Icon family="material" name="edit" />}>
          disabled
        </Link>
        <Link href="https://www.baidu.com/" disabled endDecorator={<Icon family="material" name="arrow_forward" />}>
          disabled
        </Link>
      </div>
    </div>
  );
}
