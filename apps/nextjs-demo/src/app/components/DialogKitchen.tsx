import React, { useState } from 'react';
import { Button } from '@hxnova/react-components/Button';
import { Dialog } from '@hxnova/react-components/Dialog';
import { Avatar, Typography, TextField, IconButton } from '@hxnova/react-components';
import Icon from '@hxnova/icons/Icon';

export default function DialogKitchen() {
  const [basicDialogOpen, setBasicDialogOpen] = useState(false);
  const [destroyDialogOpen, setDestroyDialogOpen] = useState(false);
  const [threeActionsDialogOpen, setThreeActionsDialogOpen] = useState(false);
  const [contentDialogOpen, setContentDialogOpen] = useState(false);
  const [fullScreenDialogOpen, setFullScreenDialogOpen] = useState(false);

  const handleClose = () => {
    setBasicDialogOpen(false);
    setDestroyDialogOpen(false);
    setThreeActionsDialogOpen(false);
    setContentDialogOpen(false);
    setFullScreenDialogOpen(false);
  };
  return (
    <>
      <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
        <Button
          variant="text"
          onClick={() => {
            setBasicDialogOpen(true);
          }}
        >
          1) Show basic dialog
        </Button>
        <Button
          variant="text"
          onClick={() => {
            setDestroyDialogOpen(true);
          }}
        >
          2) Show destructive action dialog
        </Button>
        <Button
          variant="text"
          onClick={() => {
            setThreeActionsDialogOpen(true);
          }}
        >
          3) Show three actions dialog
        </Button>
        <Button
          variant="text"
          onClick={() => {
            setContentDialogOpen(true);
          }}
        >
          4) Show dialog with content
        </Button>
        <Button
          variant="text"
          onClick={() => {
            setFullScreenDialogOpen(true);
          }}
        >
          5) Show full screen dialog
        </Button>
      </div>

      {/*  Basic dialog */}
      <Dialog.Root open={basicDialogOpen} onClose={handleClose}>
        <Dialog.Header
          supportingText={
            'A dialog is a type of modal window that appears in front of app content to provide critical information, or ask for a decision.'
          }
        >
          Basic dialog title
        </Dialog.Header>
        <Dialog.Actions>
          <Button variant="text" onClick={handleClose}>
            Button
          </Button>
          <Button variant="filled" onClick={handleClose}>
            Button
          </Button>
        </Dialog.Actions>
      </Dialog.Root>

      {/*  Destructive action dialog */}
      <Dialog.Root open={destroyDialogOpen} onClose={handleClose}>
        <Dialog.Header supportingText={'Files will be permanently deleted from your account and all synced devices.'}>
          Delete selected files
        </Dialog.Header>
        <Dialog.Actions>
          <Button variant="text" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="filled" color="error" onClick={handleClose}>
            Delete
          </Button>
        </Dialog.Actions>
      </Dialog.Root>

      {/*  Three actions dialog */}
      <Dialog.Root open={threeActionsDialogOpen} onClose={handleClose}>
        <Dialog.Header
          supportingText={
            'A dialog is a type of modal window that appears in front of app content to provide critical information, or ask for a decision.'
          }
        >
          Basic dialog title
        </Dialog.Header>
        <Dialog.Actions sx={{ justifyContent: 'space-between' }}>
          <Button variant="text" onClick={handleClose}>
            Button
          </Button>
          <div sx={{ display: 'flex', gap: '8px' }}>
            <Button variant="text" onClick={handleClose}>
              Button
            </Button>
            <Button variant="filled" onClick={handleClose}>
              Button
            </Button>
          </div>
        </Dialog.Actions>
      </Dialog.Root>

      {/* Dialog with content */}
      <Dialog.Root open={contentDialogOpen} onClose={handleClose}>
        <Dialog.Header
          supportingText={
            'This will reset your app preferences back to their default settings. The following accounts will also be signed out:'
          }
          icon={<Icon family="material" name="restart_alt" size={24} />}
        >
          Reset settings?
        </Dialog.Header>
        <Dialog.Content
          topDivider
          bottomDivider
          sx={(th) => ({
            gap: 16,
            color: th.vars.palette.onSurfaceVariant,
          })}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Avatar color="info">JC</Avatar>
            <Typography variant="bodySmall"><EMAIL></Typography>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Avatar color="warning">CW</Avatar>
            <Typography variant="bodySmall"><EMAIL></Typography>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Avatar color="error">EH</Avatar>
            <Typography variant="bodySmall"><EMAIL></Typography>
          </div>
        </Dialog.Content>
        <Dialog.Actions>
          <Button variant="text" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="filled" onClick={handleClose}>
            Accept
          </Button>
        </Dialog.Actions>
      </Dialog.Root>

      {/* Full screen dialog */}
      <Dialog.Root open={fullScreenDialogOpen} onClose={handleClose} fullScreen>
        <Dialog.Header>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
            <IconButton variant="standard" onClick={handleClose}>
              <Icon family="material" name="close" size={24} />
            </IconButton>
            <Typography
              variant="titleMedium"
              sx={(th) => ({
                fontWeight: 400,
                color: th.vars.palette.onSurface,
              })}
            >
              Create project
            </Typography>
            <Button variant="filled" onClick={handleClose}>
              Save
            </Button>
          </div>
        </Dialog.Header>
        <Dialog.Content style={{ gap: 16 }}>
          <TextField label="Project name" placeholder="Project name" fullWidth />
          <TextField label="Project description" placeholder="Project description" fullWidth />
          <TextField label="Start date" placeholder="Start date" fullWidth />
          <TextField label="End date" placeholder="End date" fullWidth />
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}
