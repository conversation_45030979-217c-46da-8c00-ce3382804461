import { Typography, TypographyProps } from '@hxnova/react-components/Typography';

export default function TypographyKitchen() {
  return (
    <div>
      {(
        [
          'displayLarge',
          'displayMedium',
          'displaySmall',
          'headlineLarge',
          'headlineMedium',
          'headlineSmall',
          'titleLarge',
          'titleMedium',
          'titleSmall',
          'bodyLarge',
          'bodyMedium',
          'bodySmall',
          'labelLarge',
          'labelMedium',
          'labelSmall',
        ] as Array<TypographyProps['variant']>
      ).map((variant) => (
        <Typography key={variant} variant={variant} sx={{ display: 'block' }}>
          {variant}
        </Typography>
      ))}
    </div>
  );
}
