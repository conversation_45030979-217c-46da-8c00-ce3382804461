import * as React from 'react';
import {
  Avatar,
  Button,
  Drawer,
  List,
  ListItem,
  ListItemContent,
  ListItemButton,
  ListItemDecorator,
  Typography,
  IconButton,
  DrawerRootProps,
  Divider,
} from '@hxnova/react-components';
import NexusCoreIcon from '@nexusui/branding/NexusCore';
import Icon from '@hxnova/icons/Icon';

interface ContentProps {
  open?: boolean;
  rail?: boolean;
  railWidth?: number;
}
const Content = ({ open, rail, railWidth = 80 }: ContentProps) => (
  <div
    style={{
      padding: '16px',
      transition: 'margin-left 225ms cubic-bezier(0.4, 0, 0.6, 1)',
      marginLeft: open || rail ? (rail ? railWidth : 360) : 0,
    }}
  >
    <Typography variant="bodySmall">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
      magna aliqua. Rhoncus dolor purus non enim praesent elementum facilisis leo vel. Risus at ultrices mi tempus
      imperdiet. Semper risus in hendrerit gravida rutrum quisque non tellus.
    </Typography>
  </div>
);

function DrawerExample({
  open,
  onClose,
  variant,
  anchor,
}: Pick<DrawerRootProps, 'open' | 'onClose' | 'variant' | 'anchor'>) {
  return (
    <div sx={{ display: 'flex' }}>
      <Drawer.Root
        slotProps={{
          container: {
            style: {
              position: 'absolute',
            },
          },
        }}
        variant={variant}
        anchor={anchor}
        open={open}
        onClose={onClose}
      >
        <Drawer.Body>
          <Drawer.NavGroup>
            <Drawer.NavItem selected label="Label" startDecorator={<Icon family="material" name="home" />} />
            <Drawer.NavItem label="Label" startDecorator={<Icon family="material" name="settings" />} />
            <Drawer.NavItem label="Label" startDecorator={<Icon family="material" name="person" />} />
          </Drawer.NavGroup>
        </Drawer.Body>
      </Drawer.Root>
      <Content open={open} />
    </div>
  );
}

function BasicNavigationDrawerExample() {
  const [open, setOpen] = React.useState(false);
  const handleClose = () => setOpen(false);
  const toggleDrawer = () => setOpen(!open);

  const [activeItem, setActiveItem] = React.useState('1');
  const handleItemSelect = (itemId: string) => setActiveItem(itemId);

  return (
    <div sx={{ display: 'flex' }}>
      <Drawer.Root
        slotProps={{
          container: {
            style: {
              position: 'absolute',
            },
          },
          subNavContainer: {
            style: {
              position: 'absolute',
            },
          },
        }}
        variant="permanent"
        showRailLabel={true}
        onClose={handleClose}
        expanded={open}
        activeItem={activeItem}
        onItemSelect={handleItemSelect}
      >
        <Drawer.Header
          productLogo={<NexusCoreIcon height={40} width={40} />}
          pageTitle={'Product name'}
          endDecorator={
            <IconButton
              sx={(theme) => ({
                color: theme.vars.palette.onSurfaceVariant,
              })}
              variant={'standard'}
              color={'primary'}
              onClick={toggleDrawer}
            >
              {open ? (
                <Icon family="material" name="left_panel_close" />
              ) : (
                <Icon family="material" name="left_panel_open" />
              )}
            </IconButton>
          }
        />
        <Drawer.Body>
          <Drawer.NavGroup>
            <Drawer.NavItem
              itemId="1"
              label="Label"
              startDecorator={<Icon family="material" name="home" />}
              trailingLabel={'100+'}
            />
            <Drawer.NavItem
              itemId="2"
              label="Label"
              startDecorator={<Icon family="material" name="inbox" />}
              trailingBadge={{
                badgeContent: '3',
                color: 'error',
              }}
            />
            <Drawer.NavItem
              itemId="3"
              label="Label"
              startDecorator={<Icon family="material" name="group" />}
            ></Drawer.NavItem>
            <Drawer.NavItem itemId="5" label="Label" startDecorator={<Icon family="material" name="outbox" />} />
          </Drawer.NavGroup>
          <Divider />
          <Drawer.NavGroup>
            <Drawer.NavItem itemId="6" label="Label" startDecorator={<Icon family="material" name="star" />} />
            <Drawer.NavItem itemId="7" label="Label" startDecorator={<Icon family="material" name="folder" />} />
          </Drawer.NavGroup>
        </Drawer.Body>
        <Drawer.Footer>
          <Divider />
          <Drawer.NavGroup>
            <Drawer.NavItem
              itemId="8"
              label="Label"
              startDecorator={<Icon family="material" name="folder" />}
              trailingLabel={'100+'}
            />
          </Drawer.NavGroup>
          <List sx={{ height: '72px', justifyContent: 'center' }}>
            <ListItem sx={{ paddingInline: '0.75rem' }}>
              <ListItemButton>
                <ListItemDecorator>
                  <Avatar color={'error'}>AA</Avatar>
                </ListItemDecorator>
                {open && (
                  <>
                    <ListItemContent primary="John Doe" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Icon family="material" name="keyboard_arrow_right" />
                    </ListItemDecorator>
                  </>
                )}
              </ListItemButton>
            </ListItem>
          </List>
        </Drawer.Footer>
      </Drawer.Root>
      <Content open={open} rail={!open} railWidth={80} />
    </div>
  );
}

function SecondLevelNavigationDrawerExample() {
  const [open, setOpen] = React.useState(false);
  const handleClose = () => setOpen(false);

  const [activeItem, setActiveItem] = React.useState('1');
  const handleItemSelect = (itemId: string) => setActiveItem(itemId);

  const [railWidth, setRailWidth] = React.useState(80);

  const handleToggleSubNavDrawer = (isOpen: boolean) => {
    setRailWidth(isOpen ? 357 : 80);
  };

  return (
    <div sx={{ display: 'flex' }}>
      <Drawer.Root
        slotProps={{
          container: {
            style: {
              position: 'absolute',
            },
          },
          subNavContainer: {
            style: {
              position: 'absolute',
            },
          },
        }}
        variant="permanent"
        showRailLabel={true}
        onClose={handleClose}
        expanded={open}
        activeItem={activeItem}
        onItemSelect={handleItemSelect}
        onToggleSubNavDrawer={handleToggleSubNavDrawer}
      >
        <Drawer.Header productLogo={<NexusCoreIcon height={40} width={40} />} pageTitle={'Product name'} />
        <Drawer.Body>
          <Drawer.NavGroup>
            <Drawer.NavItem
              itemId="1"
              label="Label"
              startDecorator={<Icon family="material" name="home" />}
              trailingLabel={'100+'}
            />
            <Drawer.NavItem
              itemId="2"
              label="Label"
              startDecorator={<Icon family="material" name="inbox" />}
              trailingBadge={{
                badgeContent: '3',
                color: 'error',
              }}
            />
            <Drawer.NavItem label="Label" startDecorator={<Icon family="material" name="group" />}>
              <Drawer.NavItem itemId="3" label="Label" startDecorator={<Icon family="material" name="star" />} />
              <Drawer.NavItem itemId="4" label="Label" startDecorator={<Icon family="material" name="star" />} />
            </Drawer.NavItem>
            <Drawer.NavItem itemId="5" label="Label" startDecorator={<Icon family="material" name="outbox" />} />
          </Drawer.NavGroup>
          <Divider />
          <Drawer.NavGroup>
            <Drawer.NavItem itemId="6" label="Label" startDecorator={<Icon family="material" name="star" />} />
            <Drawer.NavItem itemId="7" label="Label" startDecorator={<Icon family="material" name="folder" />} />
          </Drawer.NavGroup>
        </Drawer.Body>
        <Drawer.Footer>
          <Divider />
          <Drawer.NavGroup>
            <Drawer.NavItem
              itemId="8"
              label="Label"
              startDecorator={<Icon family="material" name="folder" />}
              trailingLabel={'100+'}
            />
          </Drawer.NavGroup>
          <List sx={{ height: '72px', justifyContent: 'center' }}>
            <ListItem sx={{ paddingInline: '0.75rem' }}>
              <ListItemButton>
                <ListItemDecorator>
                  <Avatar color={'error'}>AA</Avatar>
                </ListItemDecorator>
                {open && (
                  <>
                    <ListItemContent primary="John Doe" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Icon family="material" name="keyboard_arrow_right" />
                    </ListItemDecorator>
                  </>
                )}
              </ListItemButton>
            </ListItem>
          </List>
        </Drawer.Footer>
      </Drawer.Root>
      <Content open={open} rail={!open} railWidth={railWidth} />
    </div>
  );
}

type Anchor = 'top' | 'left' | 'bottom' | 'right';
export default function DrawerKitchen() {
  const [open, setOpen] = React.useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const [state, setState] = React.useState({
    top: false,
    left: false,
    bottom: false,
    right: false,
  });

  const toggleDrawer = (anchor: Anchor, open: boolean) => (_event: React.KeyboardEvent | React.MouseEvent) => {
    setState({ ...state, [anchor]: open });
  };

  return (
    <div
      sx={(theme) => ({
        width: '100%',
        height: '100%',
        padding: '8px',
        position: 'relative',
        color: theme.vars.palette.onSurfaceVariant,
      })}
    >
      {/* 1. Drawer */}
      <div>
        <h3 sx={{ marginBlock: '16px' }}>Basic Drawer</h3>
        <h4 sx={{ marginBlock: '16px' }}>Temporary</h4>
        <div sx={{ display: 'flex', gap: '16px' }}>
          {(['left', 'right', 'top', 'bottom'] as const).map((anchor) => (
            <React.Fragment key={anchor}>
              <Button
                variant="text"
                sx={{
                  textTransform: 'uppercase',
                }}
                onClick={toggleDrawer(anchor, true)}
              >
                {anchor}
              </Button>
              <Drawer.Root anchor={anchor} open={state[anchor]} onClose={toggleDrawer(anchor, false)}>
                <Drawer.Body>
                  <Drawer.NavGroup>
                    <Drawer.NavItem selected label="Label" startDecorator={<Icon family="material" name="home" />} />
                    <Drawer.NavItem label="Label" startDecorator={<Icon family="material" name="settings" />} />
                    <Drawer.NavItem label="Label" startDecorator={<Icon family="material" name="person" />} />
                  </Drawer.NavGroup>
                </Drawer.Body>
              </Drawer.Root>
            </React.Fragment>
          ))}
        </div>

        <h4 sx={{ marginBlock: '16px' }}>Persistent</h4>
        <div>
          {open ? (
            <Button variant="text" onClick={handleClose}>
              CLOSE
            </Button>
          ) : (
            <Button variant="text" onClick={handleOpen}>
              OPEN
            </Button>
          )}

          <div
            sx={(theme) => ({
              width: '100%',
              height: 500,
              border: `1px solid ${theme.vars.palette.outlineVariant}`,
              padding: '16px',
              position: 'relative',
              overflow: 'hidden',
            })}
          >
            <DrawerExample open={open} variant="persistent" />
          </div>
        </div>

        <h4 sx={{ marginBlock: '16px' }}>Permanent</h4>
        <div
          sx={(theme) => ({
            width: '100%',
            height: 500,
            border: `1px solid ${theme.vars.palette.outlineVariant}`,
            padding: '16px',
            position: 'relative',
          })}
        >
          <DrawerExample open={true} variant="permanent" />
        </div>
      </div>

      {/* 2. Navigation Drawer */}
      <div>
        <h3 sx={{ marginBlock: '16px' }}>Navigation Drawer</h3>
        <h4 sx={{ marginBlock: '16px' }}>Basic</h4>
        <div
          sx={(theme) => ({
            width: '100%',
            height: 800,
            border: `1px solid ${theme.vars.palette.outlineVariant}`,
            padding: '16px',
            position: 'relative',
            overflow: 'hidden',
          })}
        >
          <BasicNavigationDrawerExample />
        </div>

        <h4 sx={{ marginBlock: '16px' }}>Second Level Navigation Drawer</h4>
        <div
          sx={(theme) => ({
            width: '100%',
            height: 800,
            border: `1px solid ${theme.vars.palette.outlineVariant}`,
            padding: '16px',
            position: 'relative',
            overflow: 'hidden',
          })}
        >
          <SecondLevelNavigationDrawerExample />
        </div>
      </div>
    </div>
  );
}
