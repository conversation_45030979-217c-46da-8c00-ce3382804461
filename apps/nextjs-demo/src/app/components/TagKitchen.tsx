import { Tag } from '@hxnova/react-components/Tag';

export default function TagKitchen() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <div style={{ display: 'flex', gap: '1rem' }}>
        <Tag label="Tag label" intensity="bold" variant="neutral" />
        <Tag label="Tag label" intensity="subtle" variant="neutral" />
      </div>

      <div style={{ display: 'flex', gap: '1rem' }}>
        <Tag label="Tag label" intensity="bold" variant="error" />
        <Tag label="Tag label" intensity="subtle" variant="error" />
      </div>

      <div style={{ display: 'flex', gap: '1rem' }}>
        <Tag label="Tag label" intensity="bold" variant="warning" />
        <Tag label="Tag label" intensity="subtle" variant="warning" />
      </div>

      <div style={{ display: 'flex', gap: '1rem' }}>
        <Tag label="Tag label" intensity="bold" variant="info" />
        <Tag label="Tag label" intensity="subtle" variant="info" />
      </div>

      <div style={{ display: 'flex', gap: '1rem' }}>
        <Tag label="Tag label" intensity="bold" variant="success" />
        <Tag label="Tag label" intensity="subtle" variant="success" />
      </div>

      <div style={{ display: 'flex', gap: '1rem' }}>
        <Tag label="Tag label" disabled />
      </div>
    </div>
  );
}
