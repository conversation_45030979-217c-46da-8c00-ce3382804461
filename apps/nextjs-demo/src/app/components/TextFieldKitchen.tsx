import React from 'react';
import { TextField } from '@hxnova/react-components/TextField';
import { Typography } from '@hxnova/react-components/Typography';
import { IconButton } from '@hxnova/react-components/IconButton';
import { TextFieldProps } from '@hxnova/react-components/TextField';
import Icon from '@hxnova/icons/Icon';

export default function TextFieldKitchen() {
  const startDecorator = <Icon family="material" name="search" />;
  const endDecorator = (size?: 'medium' | 'small' | 'large', disabled?: boolean) => (
    <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
      <Typography>Kg</Typography>
      <IconButton variant="standard" color="inherit" size={size} disabled={disabled}>
        <Icon family="material" name="close" />
      </IconButton>
    </div>
  );
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 50, margin: 8 }}>
      {/* <div>
        <TextField
          placeholder="Name"
          label="Label"
          helperText="Supported text Supported text "
          startDecorator={startDecorator('medium')}
          size={'medium'}
          maxLength={10}
          fullWidth
          sx={{ maxWidth: '500px' }}
          value={'12'}
          onChange={(e) => {
            console.log('onChange', e.target.value);
          }}
          onFocus={(e) => {
            console.log('onFocus');
          }}
          onBlur={(e) => {
            console.log('onBlur');
          }}
          onKeyDown={(e) => {
            console.log('onKeyDown');
          }}
          onKeyUp={(e) => {
            console.log('onKeyDown');
          }}
          onClick={(e) => {
            console.log('onClick');
          }}
        />
      </div> */}
      {(['small', 'medium', 'large'] as Array<TextFieldProps['size']>).map((size) => (
        <div key={size} style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ fontWeight: 700, width: '120px' }}>{size}</div>
          <div style={{ display: 'flex', flexDirection: 'row', gap: 16 }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
              <TextField size={size} />
              <TextField placeholder="Name" size={size} />
              <TextField placeholder="Name" label="Label" size={size} />
              <TextField placeholder="Name" label="Label" helperText="Supported text" size={size} />
              <TextField
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                startDecorator={startDecorator}
                error
                size={size}
              />
              <TextField
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                startDecorator={startDecorator}
                endDecorator={endDecorator(size, false)}
                size={size}
              />
              <TextField
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                startDecorator={startDecorator}
                endDecorator={endDecorator(size, false)}
                size={size}
                maxLength={20}
              />
              <TextField
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                size={size}
                multiline
                minRows={3}
                maxLength={100}
              />
            </div>
            <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
              <TextField defaultValue={'hello'} size={size} />
              <TextField defaultValue={'hello'} placeholder="Name" size={size} />
              <TextField defaultValue={'hello'} placeholder="Name" label="Label" size={size} />
              <TextField
                defaultValue={'hello'}
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                size={size}
              />
              <TextField
                defaultValue={'hello'}
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                startDecorator={startDecorator}
                error
                size={size}
              />
              <TextField
                defaultValue={'hello'}
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                startDecorator={startDecorator}
                endDecorator={endDecorator(size, false)}
                size={size}
              />
              <TextField
                defaultValue={'hello'}
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                startDecorator={startDecorator}
                endDecorator={endDecorator(size, false)}
                size={size}
                maxLength={20}
              />
              <TextField
                defaultValue={'hello'}
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                size={size}
                multiline
                minRows={3}
                maxLength={100}
              />
            </div>
            <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
              <TextField defaultValue={'hello'} disabled size={size} />
              <TextField defaultValue={'hello'} placeholder="Name" disabled size={size} />
              <TextField defaultValue={'hello'} placeholder="Name" label="Label" disabled size={size} />
              <TextField
                defaultValue={'hello'}
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                disabled
                size={size}
              />
              <TextField
                defaultValue={'hello'}
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                startDecorator={startDecorator}
                disabled
                size={size}
              />
              <TextField
                defaultValue={'hello'}
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                startDecorator={startDecorator}
                endDecorator={endDecorator(size, true)}
                disabled
                size={size}
              />
              <TextField
                defaultValue={'hello'}
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                startDecorator={startDecorator}
                endDecorator={endDecorator(size, true)}
                disabled
                size={size}
                maxLength={20}
              />
              <TextField
                defaultValue={'hello'}
                placeholder="Name"
                label="Label"
                helperText="Supported text"
                size={size}
                disabled
                multiline
                minRows={3}
                maxLength={100}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
