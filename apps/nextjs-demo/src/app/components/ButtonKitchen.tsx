import { ButtonProps, Button } from '@hxnova/react-components';
import Icon from '@hxnova/icons/Icon';

export default function ButtonKitchen() {
  return (
    <div>
      {(['primary', 'error'] as Array<ButtonProps['color']>).map((color) =>
        [false, true].map((disabled) => (
          <div key={`${color}-${disabled}`}>
            {(['filled', 'outlined', 'text'] as Array<ButtonProps['variant']>).map((variant) => (
              <Button
                key={variant}
                sx={{ margin: 2 }}
                startIcon={<Icon family="material" name={'home'} size={24} />}
                disabled={disabled}
                variant={variant}
                color={color}
              >
                Button
              </Button>
            ))}
          </div>
        )),
      )}
    </div>
  );
}
