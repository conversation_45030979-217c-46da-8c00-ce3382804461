import Icon from '@hxnova/icons/Icon';
import { NovaPigmentTheme } from '@hxnova/react-components/types/theme';
import { ExtendTheme } from '@pigment-css/react/theme';

export default function IconKitchen() {
  return (
    <div style={{ color: 'black' }}>
      <h2>Material Icons</h2>
      <div style={{ fontSize: 48 }}>
        <Icon family="material" name="home" />
        <Icon family="material" name="home" filled />
        <Icon family="material" name="arrow_back" />
        <Icon family="material" name="arrow_back" filled />
        <Icon family="material" name="settings" sx={{ color: 'var(--palette-primary)', fontSize: 96 }} />
        <Icon family="material" name="settings" filled sx={{ color: 'var(--palette-primary)', fontSize: 96 }} />
      </div>
      <h2>Nova Custom Icons</h2>
      <div style={{ fontSize: 48 }}>
        <Icon family="nova" name="Plane-Z" />
        <Icon family="nova" name="BLK360-G2-Off__hxgn" />

        <Icon family="nova" name="3D-Surface" sx={{ color: 'var(--palette-primary)', fontSize: 96 }} />
      </div>
    </div>
  );
}
