'use client';

import * as React from 'react';
import { keyframes } from '@pigment-css/react';
import Icon from '@hxnova/icons/Icon';
import {
  Button,
  Alert,
  Snackbar,
  SnackbarCloseReason,
  AlertProps,
  Typography,
  Checkbox,
  SnackbarProps,
} from '@hxnova/react-components';

const iconMapping = {
  success: <Icon family="material" name="check_circle" size={24} />,
  warning: <Icon family="material" name="report_problem" size={24} />,
  error: <Icon family="material" name="error" size={24} />,
  info: <Icon family="material" name="info" size={24} />,
};

interface SnackbarAlertProps {
  intensity: AlertProps['intensity'];
  color: 'error' | 'warning' | 'info' | 'success';
  anchorOrigin?: SnackbarProps['anchorOrigin'];
  label: string;
}
function SnackbarAlert({ intensity, color, label, anchorOrigin }: SnackbarAlertProps) {
  const [open, setOpen] = React.useState(false);
  const handleClick = () => {
    setOpen(true);
  };

  const handleClose = (_event?: React.SyntheticEvent | Event, reason?: SnackbarCloseReason) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpen(false);
  };
  return (
    <div>
      <Button onClick={handleClick}>{label}</Button>
      <Snackbar open={open} autoHideDuration={4000} onClose={handleClose} anchorOrigin={anchorOrigin}>
        <Alert onClose={handleClose} intensity={intensity} color={color} startDecorator={iconMapping[color]}>
          This is an {intensity} {color} Alert inside a Snackbar!
        </Alert>
      </Snackbar>
    </div>
  );
}

function CustomSnackbar() {
  const [open, setOpen] = React.useState(false);
  const handleClick = () => {
    setOpen(true);
  };

  const handleClose = (_event?: React.SyntheticEvent | Event, _reason?: SnackbarCloseReason) => {
    setOpen(false);
  };

  return (
    <div>
      <Button onClick={handleClick}>Custom content</Button>
      <div style={{ display: 'flex', flexDirection: 'row', gap: '12px' }}>
        <Snackbar open={open} onClose={handleClose} autoHideDuration={4000}>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              minWidth: '400px',
              border: '1px solid #E6EAF0',
              borderRadius: '16px',
              padding: '16px',
              boxShadow:
                '0px 62px 17px 0px rgba(0, 0, 0, 0.00), 0px 10px 10px 0px rgba(0, 0, 0, 0.09), 0px 2px 5px 0px rgba(0, 0, 0, 0.10)',
            }}
          >
            <Typography variant="titleSmall">To Close this snackbar, you have to:</Typography>
            <div>
              <Checkbox defaultChecked={true} color="primary" label={'Wait for 3 seconds.'} />
              <Checkbox defaultChecked={true} color="primary" label={'Click outside of the snackbar.'} />
              <Checkbox defaultChecked={true} color="primary" label={'Press ESC key.'} />
            </div>
          </div>
        </Snackbar>
      </div>
    </div>
  );
}

function CustomAnimation() {
  const [open, setOpen] = React.useState(false);
  const animationDuration = 600;
  const handleClick = () => {
    setOpen(true);
  };

  const handleClose = (_event?: React.SyntheticEvent | Event, _reason?: SnackbarCloseReason) => {
    setOpen(false);
  };

  const enterAnimation = keyframes`
    0% {
      transform: translateX(-50%) translateY(100%);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateX(-50%) translateY(0);
    }
  `;

  const exitAnimation = keyframes`
    0% {
      transform: translateX(-50%) translateY(0);
      opacity: 1;
    }
    100% {
      transform: translateX(-50%) translateY(100%);
      opacity: 0;
    }
  `;

  return (
    <div>
      <Button onClick={handleClick}>Custom animation</Button>
      <div style={{ display: 'flex', flexDirection: 'row', gap: '12px' }}>
        <Snackbar
          style={{
            animation: `${open ? enterAnimation : exitAnimation} ${animationDuration}ms forwards`,
          }}
          open={open}
          onClose={handleClose}
          animationDuration={animationDuration}
        >
          <Alert onClose={handleClose} intensity="bold" color="primary" startDecorator={iconMapping.success}>
            This is a custom snackbar with custom animation!
          </Alert>
        </Snackbar>
      </div>
    </div>
  );
}

export default function SnackbarKitchen() {
  return (
    <div style={{ margin: '20px', display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        {(['bold', 'subtle'] as Array<AlertProps['intensity']>).map((intensity) => (
          <div key={intensity} style={{ display: 'flex', flexDirection: 'row', gap: '12px' }}>
            {(['primary', 'error', 'warning', 'info', 'success'] as Array<SnackbarAlertProps['color']>).map(
              (color, index) => (
                <SnackbarAlert
                  key={`${intensity}-${color}`}
                  intensity={intensity}
                  color={color}
                  label={`${intensity}-${color}`}
                />
              ),
            )}
          </div>
        ))}
      </div>
      <div style={{ display: 'flex', flexDirection: 'row', gap: '12px' }}>
        {(
          [
            { vertical: 'top', horizontal: 'left' },
            { vertical: 'top', horizontal: 'center' },
            { vertical: 'top', horizontal: 'right' },
            { vertical: 'bottom', horizontal: 'left' },
            { vertical: 'bottom', horizontal: 'center' },
            { vertical: 'bottom', horizontal: 'right' },
          ] as Array<SnackbarProps['anchorOrigin']>
        ).map((position: SnackbarProps['anchorOrigin']) => (
          <SnackbarAlert
            key={`${position?.vertical}-${position?.horizontal}`}
            intensity={'bold'}
            color={'success'}
            anchorOrigin={position}
            label={`${position?.vertical}-${position?.horizontal}`}
          />
        ))}
      </div>
      <div style={{ display: 'flex', flexDirection: 'row', gap: '12px' }}>
        <CustomSnackbar />
        <CustomAnimation />
      </div>
    </div>
  );
}
