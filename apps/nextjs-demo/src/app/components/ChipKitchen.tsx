import { Chip } from '@hxnova/react-components/Chip';
import Icon from '@hxnova/icons/Icon';

export default function Chip<PERSON>itchen() {
  const HomeIcon = <Icon family="material" name="home" size={24} />;
  const DeleteIcon = <Icon family="material" name="close" size={24} />;
  return (
    <div>
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} style={{ margin: 8 }} />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} style={{ margin: 8 }} selected />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} style={{ margin: 8 }} disabled />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} style={{ margin: 8 }} selected disabled />
      <br />
      <br />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} style={{ margin: 8 }} onClick={() => {}} />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        style={{ margin: 8 }}
        onClick={() => {}}
        selected
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        style={{ margin: 8 }}
        onClick={() => {}}
        disabled
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        style={{ margin: 8 }}
        onClick={() => {}}
        selected
        disabled
      />
      <br />
      <br />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} style={{ margin: 8 }} size={'small'} />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} style={{ margin: 8 }} size={'small'} selected />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} style={{ margin: 8 }} size={'small'} disabled />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        style={{ margin: 8 }}
        size={'small'}
        selected
        disabled
      />
      <br />
      <br />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'small'}
        onClick={() => {}}
        style={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'small'}
        onClick={() => {}}
        selected
        style={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'small'}
        onClick={() => {}}
        disabled
        style={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'small'}
        onClick={() => {}}
        selected
        disabled
        style={{ margin: 8 }}
      />
      <br />
      <br />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} size={'large'} style={{ margin: 8 }} />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} size={'large'} style={{ margin: 8 }} selected />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} size={'large'} style={{ margin: 8 }} disabled />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'large'}
        selected
        disabled
        style={{ margin: 8 }}
      />
      <br />
      <br />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'large'}
        onClick={() => {}}
        style={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'large'}
        onClick={() => {}}
        selected
        style={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'large'}
        onClick={() => {}}
        disabled
        style={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'large'}
        onClick={() => {}}
        style={{ margin: 8 }}
        selected
        disabled
      />
      <br />
      <br />
    </div>
  );
}
