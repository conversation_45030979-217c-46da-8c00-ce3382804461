import { Tooltip } from '@hxnova/react-components/Tooltip';
import { Button } from '@hxnova/react-components';

export default function TooltipKitchen() {
  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', gap: '24px', alignItems: 'center', marginTop: '50px' }}>
        <Tooltip showArrow title="Basic tooltip">
          <Button variant="outlined">Basic tooltip</Button>
        </Tooltip>

        <Tooltip showArrow title="Advanced tooltip" placement="bottom" open>
          <Button variant="outlined">Advanced tooltip</Button>
        </Tooltip>
      </div>
    </div>
  );
}
