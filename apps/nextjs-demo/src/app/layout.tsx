import type { Metadata } from 'next';
import '@pigment-css/react/styles.css';
import { Inter } from 'next/font/google';
import '@hxnova/themes';
import './globals.css';
import { CssBaseline } from '@hxnova/react-components/CssBaseline';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <CssBaseline />
      <body className={inter.className}>{children}</body>
    </html>
  );
}
