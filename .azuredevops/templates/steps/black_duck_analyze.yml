parameters:
  - name: semVer
    type: string
  - name: blackduckScan
    type: boolean    

steps:
  - task: BlackDuckDetectTask@10
    displayName: Black Duck Scan
    condition: and(succeeded(), eq(${{ parameters.blackduckScan }}, true))
    inputs:
      BlackDuckScaService: "BlackDuckV2"
      DetectVersion: "latest"
      DetectArguments: |
        --detect.project.name=MI-Genesis
        --detect.project.version.name=Nova
        --detect.timeout=3600
        --detect.tools=DETECTOR
        --detect.source.path=$(Build.SourcesDirectory)
        --detect.excluded.directories.patterns=**/apps/**,**/node_modules/**,**/dist/**,**/build/**,**/coverage/**
        --detect.excluded.detector.types=NPM
        --detect.npm.include.dev.dependencies=false
        --detect.detector.search.continue=true
        --detect.detector.search.depth=5
        --detect.wait.for.results=true
        --detect.cleanup=false
        --detect.risk.report.pdf=true
        --detect.risk.report.pdf.path=$(Build.SourcesDirectory)/reports/blackduck
        --detect.policy.check.fail.on.severities=NONE
        --detect.accuracy.required=NONE
        --detect.tools.excluded=SIGNATURE_SCAN
        --detect.yarn.dependency.types.excluded=NON_PRODUCTION
        --detect.npm.dependency.types.excluded=DEV
        --detect.bdio.enable.content.exclusion=true
        --logging.level.com.synopsys.integration=DEBUG