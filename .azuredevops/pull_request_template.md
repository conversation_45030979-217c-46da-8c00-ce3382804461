# JIRA issue

<https://hexagonmi.atlassian.net/browse/NOVA-XXXXX>

# Summary

#### Use a bulleted list to highlight the key changes/additions

</br></br>

# Screenshots/Images

</br></br>

# Test notes

#### Note all places that have been affected by your changes and include explicit instructions for how to test/verify your changes

</br></br>

# Checklist

Thank you for your contribution to the Nova design System. Please update the checklist below prior to submitting your PR.

- [ ] The pull request title is clear and descriptive
- [ ] The `JIRA issue(s)` have been linked and the PR `Tags` have been updated with the JIRA issue(s)
- [ ] Code builds and run with no errors or warnings
- [ ] The code is properly formatted. Run `npx nx format`
- [ ] Changes have been linted via `npx nx lint --fix` and issues have been addressed or comments have been added when a lint message has been disabled
<!-- - [ ] The code passes all unit tests run via `npx nx test` -->
- [ ] Documentation has been updated, as needed
<!-- - [ ] Component Status Badge has been updated, as needed -->
