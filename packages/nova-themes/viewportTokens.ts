// Define CSS variable type for clarity
type CSSVarToken = string;

// Define different size key sets based on actual usage
type BasicSizeKey = '2xs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
type RadiusSizeKey = '2xs' | 'xs' | 'sm' | 'md';
type VerticalSpacingKeys = '2xs' | 'xs' | 'md' | 'lg' | 'xl';

// Export the main ViewportTokens interface
export interface ViewportTokens {
  spacing: {
    padding: {
      topBottom: Record<BasicSizeKey, CSSVarToken>;
      leftRight: Record<BasicSizeKey, CSSVarToken>;
    };
    spaceBetween: {
      horizontal: Record<BasicSizeKey, CSSVarToken>;
      vertical: Record<VerticalSpacingKeys, CSSVarToken>;
    };
  };
  radius: Record<RadiusSizeKey, CSSVarToken>;
  height: {
    md: CSSVarToken;
    lg: CSSVarToken;
    xl: CSSVarToken;
    '2xl': CSSVarToken;
    '3xl': CSSVarToken;
    '4xl': CSSVarToken;
    '5xl': CSSVarToken;
  };
  outline: {
    slight: CSSVarToken;
    regular: CSSVarToken;
    considerate: CSSVarToken;
    exaggerated: CSSVarToken;
  };
  components: {
    icons: {
      md: CSSVarToken;
      lg: CSSVarToken;
      xl: CSSVarToken;
    };
    sideNav: {
      width: CSSVarToken;
    };
    card: {
      width: {
        stacked: CSSVarToken;
        horizontal: CSSVarToken;
      };
      height: {
        vertical: CSSVarToken;
      };
    };
    topNavBar: {
      width: CSSVarToken;
      margin: CSSVarToken;
    };
    sideSheet: {
      radius: CSSVarToken;
    };
    navDrawer: {
      padding: CSSVarToken;
    };
  };
}

const viewportTokens: ViewportTokens = {
  spacing: {
    padding: {
      topBottom: {
        '2xs': 'var(--spacing-padding-top-bottom-2xs, 4px)',
        xs: 'var(--spacing-padding-top-bottom-xs, 8px)',
        sm: 'var(--spacing-padding-top-bottom-sm, 12px)',
        md: 'var(--spacing-padding-top-bottom-md, 16px)',
        lg: 'var(--spacing-padding-top-bottom-lg, 24px)',
        xl: 'var(--spacing-padding-top-bottom-xl, 32px)',
      },
      leftRight: {
        '2xs': 'var(--spacing-padding-left-right-2xs, 4px)',
        xs: 'var(--spacing-padding-left-right-xs, 8px)',
        sm: 'var(--spacing-padding-left-right-sm, 12px)',
        md: 'var(--spacing-padding-left-right-md, 16px)',
        lg: 'var(--spacing-padding-left-right-lg, 24px)',
        xl: 'var(--spacing-padding-left-right-xl, 32px)',
      },
    },
    spaceBetween: {
      horizontal: {
        '2xs': 'var(--spacing-space-between-horizontal-2xs, 4px)',
        xs: 'var(--spacing-space-between-horizontal-xs, 8px)',
        sm: 'var(--spacing-space-between-horizontal-sm, 12px)',
        md: 'var(--spacing-space-between-horizontal-md, 16px)',
        lg: 'var(--spacing-space-between-horizontal-lg, 24px)',
        xl: 'var(--spacing-space-between-horizontal-xl, 32px)',
      },
      vertical: {
        '2xs': 'var(--spacing-space-between-vertical-2xs, 4px)',
        xs: 'var(--spacing-space-between-vertical-xs, 8px)',
        md: 'var(--spacing-space-between-vertical-md, 16px)',
        lg: 'var(--spacing-space-between-vertical-lg, 24px)',
        xl: 'var(--spacing-space-between-vertical-xl, 32px)',
      },
    },
  },
  height: {
    md: 'var(--height-md, 16px)',
    lg: 'var(--height-lg, 24px)',
    xl: 'var(--height-xl, 32px)',
    '2xl': 'var(--height-2xl, 40px)',
    '3xl': 'var(--height-3xl, 48px)',
    '4xl': 'var(--height-4xl, 56px)',
    '5xl': 'var(--height-5xl, 64px)',
  },
  outline: {
    slight: 'var(--outline-slight, 0.5px)',
    regular: 'var(--outline-regular, 1px)',
    considerate: 'var(--outline-considerate, 1.5px)',
    exaggerated: 'var(--outline-exaggerated, 2px)',
  },
  components: {
    sideNav: {
      width: 'var(--component-sidenav-width, 80px)',
    },
    card: {
      width: {
        stacked: 'var(--component-card-width-stacked, 360px)',
        horizontal: 'var(--component-card-width-horizontal, 1100px)',
      },
      height: {
        vertical: 'var(--component-card-height-vertical, 96px)',
      },
    },
    topNavBar: {
      width: 'var(--component-topnav-width, 1920px)',
      margin: 'var(--margin-topnav, 60px)',
    },
    sideSheet: {
      radius: 'var(--component-sidesheet-radius, 4px)',
    },
    navDrawer: {
      padding: 'var(--component-navdrawer-padding, 24px)',
    },
    icons: {
      md: 'var(--icon-size-md, 16px)',
      lg: 'var(--icon-size-lg, 20px)',
      xl: 'var(--icon-size-xl, 24px)',
    },
  },
  radius: {
    '2xs': 'var(--radius-2xs, 4px)',
    xs: 'var(--radius-xs, 8px)',
    sm: 'var(--radius-sm, 12px)',
    md: 'var(--radius-md, 16px)',
  },
};

export default viewportTokens;
