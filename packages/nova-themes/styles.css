/**
* font-face Hexagon Akkurat declarations
* it supports woff2 and woff formats
* it supports normal black bold styles
* it locates in /fonts/ folder
* it named as Hexagon Akkurat Web <style>.<format> for example Hexagon Akkurat Web.woff2
*/
@font-face {
  font-family: 'Hexagon Akkurat';
  src:
    url('./fonts/Hexagon Akkurat Web.woff2') format('woff2'),
    url('./fonts/Hexagon Akkurat Web.woff') format('woff');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Hexagon Akkurat';
  src:
    url('./fonts/Hexagon Akkurat Web Black.woff2') format('woff2'),
    url('./fonts/Hexagon Akkurat Web Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Hexagon Akkurat';
  src:
    url('./fonts/Hexagon Akkurat Web Bold.woff2') format('woff2'),
    url('./fonts/Hexagon Akkurat Web Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
}

/**
 * Nova Design System - Theme CSS Variables
 * 
 * This file contains all the CSS custom properties (variables) used throughout the Nova design system.
 * It follows a mobile-first approach with progressive enhancement through media queries.
 * 
 * Breakpoints:
 * - Mobile: 0-376px (default)
 * - Tablet Portrait: 376px-769px
 * - Tablet Landscape: 769px-1441px
 * - Desktop: 1920px-2559px
 * 
 * Variable Categories:
 * - Radius tokens: Border radius values for different components
 * - Spacing tokens: Padding and margins for layout spacing
 * - Height tokens: Standard height values for components
 * - Icon size tokens: Standardized icon dimensions
 * - Outline tokens: Border width values
 * - Component tokens: Specific dimensions for components like cards, navigation, etc.
 * 
 * Usage:
 * var(--token-name) // Example: var(--radius-2xs)
 * 
 * Note: Values defined in :root will be overridden by media queries based on viewport width.
 * Each breakpoint only defines the values that change from the previous breakpoint.
 */
:root {
  /* Default values that will be overridden by media queries */
}

/* Mobile breakpoint */
@media (min-width: 0px) {
  :root {
    /* Radius tokens - mobile defaults */
    --radius-2xs: 4px;
    --radius-xs: 8px;
    --radius-sm: 12px;
    --radius-md: 16px;
    --radius-fully-rounded: 999px;
    --radius-hex-web-cards: 8px;

    /* Spacing tokens - mobile defaults */
    --spacing-padding-top-bottom-2xs: 4px;
    --spacing-padding-top-bottom-xs: 4px;
    --spacing-padding-top-bottom-sm: 8px;
    --spacing-padding-top-bottom-md: 12px;
    --spacing-padding-top-bottom-lg: 12px;
    --spacing-padding-top-bottom-xl: 16px;

    --spacing-padding-left-right-2xs: 4px;
    --spacing-padding-left-right-xs: 4px;
    --spacing-padding-left-right-sm: 8px;
    --spacing-padding-left-right-md: 12px;
    --spacing-padding-left-right-lg: 16px;
    --spacing-padding-left-right-xl: 24px;

    --spacing-space-between-horizontal-2xs: 4px;
    --spacing-space-between-horizontal-xs: 4px;
    --spacing-space-between-horizontal-sm: 8px;
    --spacing-space-between-horizontal-md: 8px;
    --spacing-space-between-horizontal-lg: 8px;
    --spacing-space-between-horizontal-xl: 12px;

    --spacing-space-between-vertical-2xs: 4px;
    --spacing-space-between-vertical-xs: 4px;
    --spacing-space-between-vertical-md: 8px;
    --spacing-space-between-vertical-lg: 8px;
    --spacing-space-between-vertical-xl: 12px;

    /* Height tokens - mobile defaults */
    --height-md: 16px;
    --height-lg: 24px;
    --height-xl: 32px;
    --height-2xl: 40px;
    --height-3xl: 48px;
    --height-4xl: 56px;
    --height-5xl: 64px;

    /* Icon size tokens - mobile defaults */
    --icon-size-md: 16px;
    --icon-size-lg: 16px;
    --icon-size-xl: 20px;

    /* Outline tokens - mobile defaults */
    --outline-slight: 0.5px;
    --outline-regular: 1px;
    --outline-considerate: 1.5px;
    --outline-exaggerated: 2px;

    /* Component tokens - mobile defaults */
    --component-card-width-stacked: 288px;
    --component-card-width-horizontal: 288px;
    --component-card-height-vertical: 84px;
    --component-sidenav-width: 0px;

    /* TopNavBar component tokens */
    --component-topnav-width: 375px;
    --margin-topnav: 16px;

    /* SideSheet component tokens */
    --component-sidesheet-radius: 16px;

    /* NavDrawer component tokens */
    --component-navdrawer-padding: 24px;
  }
}

/* Tablet breakpoint */
@media (min-width: 600px) {
  :root {
    /* Radius tokens - tablet */
    --radius-2xs: 4px;
    --radius-xs: 8px;
    --radius-sm: 12px;
    --radius-md: 16px;
    --radius-fully-rounded: 999px;
    --radius-hex-web-cards: 4px;

    /* Spacing tokens - tablet */
    --spacing-padding-top-bottom-2xs: 4px;
    --spacing-padding-top-bottom-xs: 4px;
    --spacing-padding-top-bottom-sm: 8px;
    --spacing-padding-top-bottom-md: 12px;
    --spacing-padding-top-bottom-lg: 16px;
    --spacing-padding-top-bottom-xl: 24px;

    --spacing-padding-left-right-2xs: 4px;
    --spacing-padding-left-right-xs: 4px;
    --spacing-padding-left-right-sm: 8px;
    --spacing-padding-left-right-md: 12px;
    --spacing-padding-left-right-lg: 16px;
    --spacing-padding-left-right-xl: 24px;

    --spacing-space-between-horizontal-2xs: 4px;
    --spacing-space-between-horizontal-xs: 4px;
    --spacing-space-between-horizontal-sm: 8px;
    --spacing-space-between-horizontal-md: 12px;
    --spacing-space-between-horizontal-lg: 16px;
    --spacing-space-between-horizontal-xl: 24px;

    --spacing-space-between-vertical-2xs: 4px;
    --spacing-space-between-vertical-xs: 4px;
    --spacing-space-between-vertical-md: 8px;
    --spacing-space-between-vertical-lg: 16px;
    --spacing-space-between-vertical-xl: 24px;

    /* Height tokens - tablet */
    --height-md: 16px;
    --height-lg: 24px;
    --height-xl: 32px;
    --height-2xl: 40px;
    --height-3xl: 48px;
    --height-4xl: 56px;
    --height-5xl: 64px;

    /* Icon size tokens - tablet */
    --icon-size-md: 16px;
    --icon-size-lg: 16px;
    --icon-size-xl: 20px;

    /* Outline tokens - tablet */
    --outline-slight: 0.5px;
    --outline-regular: 1px;
    --outline-considerate: 1.5px;
    --outline-exaggerated: 2px;

    /* Width tokens - tablet */
    --component-sidenav-width: 72px;

    /* Component tokens - tablet */
    --component-card-width-stacked: 320px;
    --component-card-width-horizontal: 744px;
    --component-card-height-vertical: 84px;

    /* TopNavBar component tokens - tablet */
    --component-topnav-width: 768px;
    --margin-topnav: 24px;

    /* SideSheet component tokens - tablet */
    --component-sidesheet-radius: 4px;

    /* NavDrawer component tokens - tablet */
    --component-navdrawer-padding: 24px;
  }
}

/* Laptop breakpoint */
@media (min-width: 1200px) {
  :root {
    /* Radius tokens - laptop */
    --radius-2xs: 4px;
    --radius-xs: 8px;
    --radius-sm: 12px;
    --radius-md: 16px;
    --radius-fully-rounded: 999px;
    --radius-hex-web-cards: 8px;

    /* Spacing tokens - laptop */
    --spacing-padding-top-bottom-2xs: 4px;
    --spacing-padding-top-bottom-xs: 8px;
    --spacing-padding-top-bottom-sm: 12px;
    --spacing-padding-top-bottom-md: 16px;
    --spacing-padding-top-bottom-lg: 24px;
    --spacing-padding-top-bottom-xl: 32px;

    --spacing-padding-left-right-2xs: 4px;
    --spacing-padding-left-right-xs: 8px;
    --spacing-padding-left-right-sm: 12px;
    --spacing-padding-left-right-md: 16px;
    --spacing-padding-left-right-lg: 24px;
    --spacing-padding-left-right-xl: 32px;

    --spacing-space-between-horizontal-2xs: 4px;
    --spacing-space-between-horizontal-xs: 8px;
    --spacing-space-between-horizontal-sm: 12px;
    --spacing-space-between-horizontal-md: 16px;
    --spacing-space-between-horizontal-lg: 16px;
    --spacing-space-between-horizontal-xl: 24px;

    --spacing-space-between-vertical-2xs: 4px;
    --spacing-space-between-vertical-xs: 8px;
    --spacing-space-between-vertical-md: 16px;
    --spacing-space-between-vertical-lg: 16px;
    --spacing-space-between-vertical-xl: 24px;

    /* Height tokens - laptop */
    --height-md: 16px;
    --height-lg: 24px;
    --height-xl: 32px;
    --height-2xl: 40px;
    --height-3xl: 48px;
    --height-4xl: 56px;
    --height-5xl: 64px;

    /* Icon size tokens - laptop */
    --icon-size-md: 16px;
    --icon-size-lg: 20px;
    --icon-size-xl: 24px;

    /* Outline tokens - laptop */
    --outline-slight: 0.5px;
    --outline-regular: 1px;
    --outline-considerate: 1.5px;
    --outline-exaggerated: 2px;

    /* Width tokens - laptop */
    --component-sidenav-width: 80px;

    /* Component tokens - laptop */
    --component-card-width-stacked: 360px;
    --component-card-width-horizontal: 1100px;
    --component-card-height-vertical: 96px;

    /* TopNavBar component tokens - laptop */
    --component-topnav-width: 1440px;
    --margin-topnav: 60px;

    /* SideSheet component tokens - laptop */
    --component-sidesheet-radius: 4px;

    /* NavDrawer component tokens - laptop */
    --component-navdrawer-padding: 24px;
  }
}

/* Desktop breakpoint */
@media (min-width: 1600px) {
  :root {
    /* Radius tokens - desktop */
    --radius-2xs: 4px;
    --radius-xs: 8px;
    --radius-sm: 12px;
    --radius-md: 16px;
    --radius-fully-rounded: 999px;
    --radius-hex-web-cards: 8px;

    /* Spacing tokens - desktop */
    --spacing-padding-top-bottom-2xs: 4px;
    --spacing-padding-top-bottom-xs: 8px;
    --spacing-padding-top-bottom-sm: 12px;
    --spacing-padding-top-bottom-md: 16px;
    --spacing-padding-top-bottom-lg: 24px;
    --spacing-padding-top-bottom-xl: 32px;

    --spacing-padding-left-right-2xs: 4px;
    --spacing-padding-left-right-xs: 8px;
    --spacing-padding-left-right-sm: 12px;
    --spacing-padding-left-right-md: 16px;
    --spacing-padding-left-right-lg: 24px;
    --spacing-padding-left-right-xl: 32px;

    --spacing-space-between-horizontal-2xs: 4px;
    --spacing-space-between-horizontal-xs: 8px;
    --spacing-space-between-horizontal-sm: 12px;
    --spacing-space-between-horizontal-md: 16px;
    --spacing-space-between-horizontal-lg: 24px;
    --spacing-space-between-horizontal-xl: 32px;

    --spacing-space-between-vertical-2xs: 4px;
    --spacing-space-between-vertical-xs: 8px;
    --spacing-space-between-vertical-md: 16px;
    --spacing-space-between-vertical-lg: 24px;
    --spacing-space-between-vertical-xl: 32px;

    /* Height tokens - desktop */
    --height-md: 16px;
    --height-lg: 24px;
    --height-xl: 32px;
    --height-2xl: 40px;
    --height-3xl: 48px;
    --height-4xl: 56px;
    --height-5xl: 64px;

    /* Icon size tokens - desktop */
    --icon-size-md: 16px;
    --icon-size-lg: 20px;
    --icon-size-xl: 24px;

    /* Outline tokens - desktop */
    --outline-slight: 0.5px;
    --outline-regular: 1px;
    --outline-considerate: 1.5px;
    --outline-exaggerated: 2px;

    /* Width tokens - desktop */
    --component-sidenav-width: 80px;

    /* Component tokens - desktop */
    --component-card-width-stacked: 360px;
    --component-card-width-horizontal: 1100px;
    --component-card-height-vertical: 96px;

    /* TopNavBar component tokens - desktop */
    --component-topnav-width: 1920px;
    --margin-topnav: 60px;

    /* SideSheet component tokens - desktop */
    --component-sidesheet-radius: 4px;

    /* NavDrawer component tokens - desktop */
    --component-navdrawer-padding: 24px;
  }
}
