// Basic size variants
type SizeVariant = {
  medium: number;
  small: number;
  large: number;
};

export type SizeTokens = {
  icon: SizeVariant;
  padding: {
    topBottom: {
      '2xs': SizeVariant;
      xs: SizeVariant;
      sm?: SizeVariant;
      md: SizeVariant;
      lg: SizeVariant;
      xl?: SizeVariant;
      '2xl'?: SizeVariant;
    };
    leftRight: {
      '2xs': SizeVariant;
      xs: SizeVariant;
      sm?: SizeVariant;
      md: SizeVariant;
      lg: SizeVariant;
      xl?: SizeVariant;
      '2xl'?: SizeVariant;
    };
  };
  spaceBetween: {
    horizontal: {
      '2xs': SizeVariant;
      xs: SizeVariant;
      md: SizeVariant;
      lg: SizeVariant;
      '2xl'?: SizeVariant;
    };
    vertical: {
      '2xs': SizeVariant;
      xs: SizeVariant;
      md: SizeVariant;
      lg: SizeVariant;
      '2xl'?: SizeVariant;
    };
  };
  radius: {
    xs: SizeVariant;
    sm: SizeVariant;
    md: SizeVariant;
    fullyRounded: SizeVariant;
  };
  height: {
    md: SizeVariant;
    lg: SizeVariant;
    xl: SizeVariant;
    '2xl': SizeVariant;
  };
  width: {
    xl: SizeVariant;
    '2xl': SizeVariant;
  };
  typescale: {
    labelSmall: {
      size: SizeVariant;
      height: SizeVariant;
    };
    labelMedium: {
      size: SizeVariant;
      height: SizeVariant;
    };
    bodySmall: {
      size: SizeVariant;
      height: SizeVariant;
    };
    bodyMedium: {
      size: SizeVariant;
      height: SizeVariant;
    };
  };
  outline: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  nestedLevel: {
    fixed: SizeVariant;
    lg: SizeVariant;
  };
};

const sizeTokens: SizeTokens = {
  icon: {
    small: 20,
    medium: 24,
    large: 28,
  },
  padding: {
    topBottom: {
      '2xs': {
        small: 4,
        medium: 4,
        large: 8,
      },
      xs: {
        small: 4,
        medium: 8,
        large: 12,
      },
      sm: {
        small: 8,
        medium: 12,
        large: 16,
      },
      md: {
        small: 8,
        medium: 16,
        large: 24,
      },
      lg: {
        small: 12,
        medium: 24,
        large: 36,
      },
    },
    leftRight: {
      '2xs': {
        small: 4,
        medium: 4,
        large: 4,
      },
      xs: {
        small: 8,
        medium: 8,
        large: 12,
      },
      md: {
        small: 8,
        medium: 16,
        large: 24,
      },
      lg: {
        small: 12,
        medium: 24,
        large: 36,
      },
      xl: {
        small: 16,
        medium: 32,
        large: 48,
      },
      '2xl': {
        small: 24,
        medium: 40,
        large: 60,
      },
    },
  },
  spaceBetween: {
    horizontal: {
      '2xs': {
        small: 4,
        medium: 4,
        large: 8,
      },
      xs: {
        small: 4,
        medium: 8,
        large: 12,
      },
      md: {
        small: 8,
        medium: 16,
        large: 24,
      },
      lg: {
        small: 12,
        medium: 24,
        large: 32,
      },
      '2xl': {
        small: 24,
        medium: 40,
        large: 48,
      },
    },
    vertical: {
      '2xs': {
        small: 4,
        medium: 4,
        large: 8,
      },
      xs: {
        small: 4,
        medium: 8,
        large: 12,
      },
      md: {
        small: 8,
        medium: 16,
        large: 24,
      },
      lg: {
        small: 12,
        medium: 24,
        large: 36,
      },
    },
  },
  radius: {
    xs: {
      small: 8,
      medium: 8,
      large: 8,
    },
    sm: {
      small: 8,
      medium: 12,
      large: 12,
    },
    md: {
      small: 16,
      medium: 16,
      large: 16,
    },
    fullyRounded: {
      small: 999,
      medium: 999,
      large: 999,
    },
  },
  height: {
    md: {
      small: 12,
      medium: 16,
      large: 24,
    },
    lg: {
      small: 16,
      medium: 24,
      large: 28,
    },
    xl: {
      small: 24,
      medium: 32,
      large: 40,
    },
    '2xl': {
      small: 32,
      medium: 40,
      large: 48,
    },
  },
  width: {
    xl: {
      small: 24,
      medium: 32,
      large: 40,
    },
    '2xl': {
      small: 32,
      medium: 40,
      large: 48,
    },
  },
  typescale: {
    labelSmall: {
      size: {
        small: 12,
        medium: 14,
        large: 16,
      },
      height: {
        small: 16,
        medium: 19,
        large: 20,
      },
    },
    labelMedium: {
      size: {
        small: 14,
        medium: 16,
        large: 18,
      },
      height: {
        small: 18,
        medium: 20,
        large: 24,
      },
    },
    bodyMedium: {
      size: {
        small: 14,
        medium: 16,
        large: 18,
      },
      height: {
        small: 18,
        medium: 20,
        large: 24,
      },
    },
    bodySmall: {
      size: {
        small: 12,
        medium: 14,
        large: 16,
      },
      height: {
        small: 16,
        medium: 18,
        large: 20,
      },
    },
  },
  outline: {
    sm: 0.5,
    md: 1,
    lg: 1.5,
    xl: 2,
  },
  nestedLevel: {
    fixed: {
      small: 24,
      medium: 24,
      large: 24,
    },
    lg: {
      small: 20,
      medium: 24,
      large: 32,
    },
  },
};

export default sizeTokens;
