import { render, screen } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { Skeleton } from './Skeleton.tsx';

vi.mock('@pigment-css/react', async () => {
  const actual = await import('@pigment-css/react');
  return {
    ...actual,
    keyframes: vi.fn(() => 'mocked-keyframe'),
  };
});

describe('<Skeleton />', () => {
  it('should render normal', () => {
    render(<Skeleton data-testid="Skeleton-root" />);
    expect(screen.getByTestId('Skeleton-root')).toBeInTheDocument();
  });

  it('should get fitContent class when passed children and no width', () => {
    render(
      <Skeleton data-testid="Skeleton-root">
        <div data-testid="Skeleton-children">
          <p>Hello</p>
        </div>
      </Skeleton>,
    );
    expect(screen.getByTestId('Skeleton-root')).toHaveClass('NovaSkeleton-withChildren');
    expect(screen.getByTestId('Skeleton-root')).toHaveClass('NovaSkeleton-fitContent');
  });

  it('should get heightAuto class when passed children and no height', () => {
    render(
      <Skeleton data-testid="Skeleton-root">
        <div data-testid="Skeleton-children">
          <p>Hello</p>
        </div>
      </Skeleton>,
    );
    expect(screen.getByTestId('Skeleton-root')).toHaveClass('NovaSkeleton-heightAuto');
  });
});
