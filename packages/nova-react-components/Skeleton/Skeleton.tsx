'use client';

import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { keyframes, styled } from '@pigment-css/react';
import { SkeletonOwnerState, SkeletonProps } from './Skeleton.types';
import { getSkeletonUtilityClass } from './Skeleton.classes';
import clsx from 'clsx';

const useUtilityClasses = (ownerState: SkeletonOwnerState) => {
  const { variant, animation, hasChildren, hasWidth, hasHeight } = ownerState;

  const slots = {
    root: [
      'root',
      variant,
      animation,
      hasChildren && 'withChildren',
      hasChildren && !hasWidth && 'fitContent',
      hasChildren && !hasHeight && 'heightAuto',
    ],
  };

  return composeClasses(slots, getSkeletonUtilityClass, {});
};

const pulseKeyframe = keyframes`
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
`;

const waveKeyframe = keyframes`
  0% {
    transform: translateX(-100%);
  }

  50% {
    /* +0.5s of delay between each loop */
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
`;

const SkeletonRoot = styled('span')<SkeletonOwnerState>(({ theme }) => ({
  display: 'block',
  backgroundColor: theme.vars.palette.surfaceContainerHighest, // TODO: confirm the color with design team
  height: '1.2em',

  variants: [
    {
      props: { variant: 'text' },
      style: {
        marginTop: 0,
        marginBottom: 0,
        height: 'auto',
        transformOrigin: '0 55%',
        transform: 'scale(1, 0.60)',
        borderRadius: `4px / ${Math.round((4 / 0.6) * 10) / 10}px`,
        '&:empty:before': {
          content: '"\\00a0"',
        },
      },
    },
    {
      props: { variant: 'circular' },
      style: { borderRadius: '50%' },
    },
    {
      props: { variant: 'rounded' },
      style: { borderRadius: '4px' },
    },
    {
      props: { hasChildren: true },
      style: {
        '& > *': {
          visibility: 'hidden',
        },
      },
    },
    {
      props: { hasChildren: true, hasWidth: false },
      style: {
        maxWidth: 'fit-content',
      },
    },
    {
      props: { hasChildren: true, hasHeight: false },
      style: {
        height: 'auto',
      },
    },
    {
      props: { animation: 'pulse' },
      style: {
        animation: `${pulseKeyframe} 2s ease-in-out 0.5s infinite`,
      },
    },
    {
      props: { animation: 'wave' },
      style: {
        position: 'relative',
        overflow: 'hidden',
        /* Fix bug in Safari https://bugs.webkit.org/show_bug.cgi?id=68196 */
        WebkitMaskImage: '-webkit-radial-gradient(white, black)',
        '&::after': {
          animation: `${waveKeyframe} 2s linear 0.5s infinite`,
          background: `linear-gradient(
                90deg,
                transparent,
                rgba(0, 0, 0, 0.08), 
                transparent
              )`, // TODO: confirm the color with design team
          content: '""',
          position: 'absolute',
          transform: 'translateX(-100%)' /* Avoid flash during server-side hydration */,
          bottom: 0,
          left: 0,
          right: 0,
          top: 0,
        },
      },
    },
  ],
}));

export const Skeleton = React.forwardRef((props: SkeletonProps, ref: React.ForwardedRef<Element>) => {
  const {
    animation = 'pulse',
    className,
    component = 'span',
    height,
    style,
    variant = 'text',
    width,
    ...other
  } = props;

  const ownerState = {
    ...props,
    animation,
    component,
    variant,
    hasChildren: Boolean(other.children),
    hasWidth: Boolean(width),
    hasHeight: Boolean(height),
  };

  const classes = useUtilityClasses(ownerState);

  const slotRootProps = useSlotProps({
    elementType: SkeletonRoot,
    externalSlotProps: {},
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    className: clsx(classes.root, className),
  });

  return (
    <SkeletonRoot
      {...slotRootProps}
      style={{
        width,
        height,
        ...style,
      }}
    />
  );
});
