import { OverrideProps } from '@mui/types';

export interface SkeletonTypeMap<P = object, D extends React.ElementType = 'span'> {
  props: P & {
    /**
     * The animation.
     * If `false` the animation effect is disabled.
     * @default 'pulse'
     */
    animation?: 'pulse' | 'wave' | false;
    /**
     * Optional children to infer width and height from.
     */
    children?: React.ReactNode;
    /**
     * Height of the skeleton.
     * Useful when you don't want to adapt the skeleton to a text element but for instance a card.
     */
    height?: number | string;

    /**
     * The type of content that will be rendered.
     * @default 'text'
     */
    variant?: 'text' | 'rectangular' | 'rounded' | 'circular';
    /**
     * Width of the skeleton.
     * Useful when the skeleton is inside an inline element with no width of its own.
     */
    width?: number | string;
  };
  defaultComponent: D;
}

export type SkeletonProps<
  D extends React.ElementType = SkeletonTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<SkeletonTypeMap<P, D>, D>;

export interface SkeletonOwnerState extends SkeletonProps {
  hasChildren: boolean;
  hasWidth: boolean;
  hasHeight: boolean;
}
