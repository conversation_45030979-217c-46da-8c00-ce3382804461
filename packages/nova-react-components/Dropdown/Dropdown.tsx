'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import capitalize from '@mui/utils/capitalize';
import useForkRef from '@mui/utils/useForkRef';
import clsx from 'clsx';
import { DropdownOwnerState, DropdownProps } from './Dropdown.types';
import dropdownClasses, { getDropdownUtilityClass } from './Dropdown.classes';
import { SelectProvider, SelectValue, useSelect } from '../internal/hooks/useSelect';
import FormControlContext from '../FormControl/FormControlContext';
import { SelectOption } from '../internal/hooks/useOption';
import { MenuListRoot as StyledList } from '../Menu/Menu';
import { Popper, PopperOwnProps, PopperProps } from '../Popper';
import GroupListContext from '../List/GroupListContext';
import ListProvider from '../List/ListProvider';
import KeyboardArrowDown from '../internal/svg-icons/KeyboardArrowDown';
import KeyboardArrowUp from '../internal/svg-icons/KeyboardArrowUp';
import ErrorIcon from '../internal/svg-icons/Error';
import typographyClasses from '../Typography/Typography.classes';

function defaultRenderValue<Value>(selectedOptions: SelectOption<Value> | SelectOption<Value>[] | null) {
  if (Array.isArray(selectedOptions)) {
    return <React.Fragment>{selectedOptions.map((o) => o.label).join(', ')}</React.Fragment>;
  }

  return selectedOptions?.label ?? '';
}

const iconVariants = (theme: any) => [
  {
    props: { size: 'small' as const },
    style: {
      fontSize: theme.vars.sys.size.icon.small,
      [`& svg`]: {
        fontSize: theme.vars.sys.size.icon.small,
      },
      [`& .${typographyClasses.root}`]: {
        fontSize: theme.vars.sys.size.typescale.labelMedium.size.small,
      },
    },
  },
  {
    props: { size: 'medium' as const },
    style: {
      fontSize: theme.vars.sys.size.icon.medium,
      [`& svg`]: {
        fontSize: theme.vars.sys.size.icon.medium,
      },
      [`& .${typographyClasses.root}`]: {
        fontSize: theme.vars.sys.size.typescale.labelMedium.size.medium,
      },
    },
  },
  {
    props: { size: 'large' as const },
    style: {
      fontSize: theme.vars.sys.size.icon.large,
      [`& svg`]: {
        fontSize: theme.vars.sys.size.icon.large,
      },
      [`& .${typographyClasses.root}`]: {
        fontSize: theme.vars.sys.size.typescale.labelMedium.size.large,
      },
    },
  },
];

const defaultModifiers: PopperProps['modifiers'] = [
  {
    name: 'offset',
    options: {
      offset: [0, 4],
    },
  },
  {
    // popper will have the same width as root element when open
    name: 'equalWidth',
    enabled: true,
    phase: 'beforeWrite',
    requires: ['computeStyles'],
    fn: ({ state }) => {
      state.styles.popper.width = `${state.rects.reference.width}px`;
    },
  },
];

const useUtilityClasses = (ownerState: DropdownOwnerState<any, boolean>) => {
  const { disabled, error, focusVisible, size, open, multiple } = ownerState;

  const slots = {
    root: [
      'root',
      disabled && 'disabled',
      focusVisible && 'focusVisible',
      open && 'expanded',
      size && `size${capitalize(size)}`,
      multiple && 'multiple',
      error && 'error',
    ],
    button: ['button'],
    startDecorator: ['startDecorator'],
    endDecorator: ['endDecorator'],
    indicator: ['indicator', open && 'expanded'],
    listbox: ['listbox', open && 'expanded', disabled && 'disabled'],
    errorStateIcon: ['errorStateIcon'],
  };

  return composeClasses(slots, getDropdownUtilityClass, {});
};

const DropdownRoot = styled('div')<DropdownOwnerState<any, boolean>>(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  flexWrap: 'wrap',
  border: '1px solid',
  borderColor: theme.vars.palette.outlineVariant,
  boxSizing: 'border-box',
  borderRadius: theme.vars.sys.size.radius.xs.medium,
  gap: theme.vars.sys.size.spaceBetween.horizontal['2xs'].medium,
  cursor: 'pointer',
  color: theme.vars.palette.onSurfaceVariant,
  backgroundColor: theme.vars.palette.surfaceContainerHigh,
  '&:hover': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },
  '&:active': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurfaceVariant} ${theme.vars.palette.stateLayers.pressOnSurfaceVariant})`,
  },
  [`&.${dropdownClasses.focusVisible}`]: {
    borderColor: theme.vars.palette.primary,
  },
  variants: [
    {
      props: { filled: true },
      style: {
        color: theme.vars.palette.onSurface,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: { error: true, disabled: false },
      style: {
        borderColor: theme.vars.palette.error,
        '&:hover': {
          borderColor: theme.vars.palette.error,
        },
      },
    },
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
        border: 'unset',
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
        },
        cursor: 'default',
        pointerEvents: 'none',
      },
    },
    {
      props: { readOnly: true },
      style: {
        color: theme.vars.palette.onSurfaceVariant,
        backgroundColor: theme.vars.palette.surfaceContainerHighest,
        border: theme.vars.palette.outlineVariant,
        '&:hover': {
          backgroundColor: theme.vars.palette.surfaceContainerHighest,
        },
        cursor: 'default',
        pointerEvents: 'none',
      },
    },
    {
      props: { open: true },
      style: {
        borderColor: theme.vars.palette.primary,
        '&:hover': {
          // backgroundColor: theme.vars.palette.surfaceContainerHigh,
          borderColor: theme.vars.palette.primary,
        },
      },
    },
    {
      props: { error: true, disabled: false },
      style: {
        borderColor: theme.vars.palette.error,
        '&:hover': {
          borderColor: theme.vars.palette.error,
        },
      },
    },
    {
      props: { fullWidth: true },
      style: {
        width: '100%',
      },
    },
    {
      props: { size: 'small' },
      style: {
        minHeight: theme.vars.sys.size.height['2xl'].small,
        paddingInline: theme.vars.sys.size.padding.leftRight.xs.small,
      },
    },
    {
      props: { size: 'medium' },
      style: {
        minHeight: theme.vars.sys.size.height['2xl'].medium,
        paddingInline: theme.vars.sys.size.padding.leftRight.xs.medium,
      },
    },
    {
      props: { size: 'large' },
      style: {
        minHeight: theme.vars.sys.size.height['2xl'].large,
        paddingInline: theme.vars.sys.size.padding.leftRight.xs.large,
      },
    },
  ],
}));

const DropdownButton = styled('button')<DropdownOwnerState<any, boolean>>(({ theme }) => ({
  border: 0,
  outline: 0,
  background: 'none',
  padding: 0,
  fontSize: 'inherit',
  color: 'inherit',
  alignSelf: 'stretch',
  // make children horizontally aligned
  display: 'flex',
  alignItems: 'center',
  flex: 1,
  fontFamily: 'inherit',
  whiteSpace: 'nowrap',
  cursor: 'pointer',
  overflow: 'hidden', // prevent the scrollbar for long text
  variants: [
    {
      props: { size: 'small' },
      style: {
        fontSize: theme.vars.sys.size.typescale.labelMedium.size.small,
        lineHeight: theme.vars.sys.size.typescale.labelMedium.height.small,
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: theme.vars.sys.size.typescale.labelMedium.size.medium,
        lineHeight: theme.vars.sys.size.typescale.labelMedium.height.medium,
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: theme.vars.sys.size.typescale.labelMedium.size.large,
        lineHeight: theme.vars.sys.size.typescale.labelMedium.height.large,
      },
    },
  ],
  '&::before': {
    content: '""',
    display: 'block',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: theme.vars.sys.size.radius.xs.medium,
  },
}));

const DropdownList = styled(StyledList)<DropdownOwnerState<any, boolean>>(({ theme }) => ({}));

const DropdownStartDecorator = styled('span')<DropdownOwnerState<any, boolean>>(({ theme }) => ({
  display: 'inherit',
  alignItems: 'center',
  variants: [
    ...iconVariants(theme),
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
  ],
}));

const DropdownEndDecorator = styled('span')<DropdownOwnerState<any, boolean>>(({ theme }) => ({
  display: 'inherit',
  alignItems: 'center',
  variants: [
    ...iconVariants(theme),
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
  ],
}));

const DropdownIndicator = styled('span')<DropdownOwnerState<any, boolean>>(({ theme }) => ({
  display: 'inherit',
  alignItems: 'center',
  variants: [
    ...iconVariants(theme),
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
  ],
}));

export const ErrorStateIcon = styled('div')<DropdownOwnerState<any, boolean>>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  '& svg': {
    color: theme.vars.palette.error,
  },
  variants: [...iconVariants(theme)],
}));

const DropdownInner = <Value, Multiple extends boolean = false>(
  props: DropdownProps<Value, Multiple>,
  ref: React.ForwardedRef<HTMLDivElement>,
) => {
  const {
    children,
    component,
    action,
    autoFocus,
    defaultValue,
    defaultListboxOpen = false,
    disabled: disabledProp = false,
    error: errorProp = false,
    readOnly: readOnlyProp = false,
    showErrorIcon = true,
    getSerializedValue,
    placeholder,
    listboxId,
    listboxOpen: listboxOpenProp,
    onChange,
    onListboxOpenChange,
    onClose,
    renderValue: renderValueProp,
    required = false,
    value: valueProp,
    size: sizeProp = 'medium',
    startDecorator,
    endDecorator,
    // props to forward to the button (all handlers should go through slotProps.button)
    'aria-describedby': ariaDescribedby,
    'aria-label': ariaLabel,
    'aria-labelledby': ariaLabelledby,
    id,
    name,
    multiple = false as Multiple,
    indicator: indicatorProp,
    slots = {},
    slotProps = {},
    ...rest
  } = props;

  const formControl = React.useContext(FormControlContext);
  const disabled = props.disabled ?? formControl?.disabled ?? disabledProp;
  const error = props.error ?? formControl?.error ?? errorProp;
  const size = props.size ?? formControl?.size ?? sizeProp;
  const readOnly = props.readOnly ?? readOnlyProp;
  const renderValue: (option: SelectValue<SelectOption<Value>, Multiple>) => React.ReactNode =
    renderValueProp ?? defaultRenderValue;
  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);

  const rootRef = React.useRef<HTMLElement>(null);
  const buttonRef = React.useRef<HTMLElement>(null);
  const handleRef = useForkRef(ref, rootRef);

  React.useImperativeHandle(
    action,
    () => ({
      focusVisible: () => {
        buttonRef.current?.focus();
      },
    }),
    [],
  );

  React.useEffect(() => {
    setAnchorEl(rootRef.current);
  }, []);

  React.useEffect(() => {
    if (autoFocus) {
      buttonRef.current!.focus();
    }
  }, [autoFocus]);

  const handleOpenChange = React.useCallback(
    (isOpen: boolean) => {
      onListboxOpenChange?.(isOpen);
      if (!isOpen) {
        onClose?.();
      }
    },
    [onClose, onListboxOpenChange],
  );

  const {
    buttonActive,
    buttonFocusVisible,
    contextValue,
    getButtonProps,
    getListboxProps,
    getHiddenInputProps,
    getOptionMetadata,
    open: listboxOpen,
    value,
  } = useSelect<Value, Multiple>({
    buttonRef,
    defaultOpen: defaultListboxOpen,
    defaultValue,
    disabled: disabled || readOnly,
    getSerializedValue,
    listboxId,
    multiple,
    name,
    required,
    onChange,
    onOpenChange: handleOpenChange,
    open: listboxOpenProp,
    value: valueProp,
  });

  const selectedOption = React.useMemo(() => {
    let selectedOptionsMetadata: SelectValue<SelectOption<Value>, Multiple>;
    if (multiple) {
      selectedOptionsMetadata = (value as Value[])
        .map((v) => getOptionMetadata(v))
        .filter((o) => o !== undefined) as SelectValue<SelectOption<Value>, Multiple>;
    } else {
      selectedOptionsMetadata = (getOptionMetadata(value as Value) ?? null) as SelectValue<
        SelectOption<Value>,
        Multiple
      >;
    }

    return selectedOptionsMetadata;
  }, [getOptionMetadata, value, multiple]);

  let displayValue = placeholder;
  let filled = false;

  if (
    (Array.isArray(selectedOption) && selectedOption.length > 0) ||
    (!Array.isArray(selectedOption) && !!selectedOption)
  ) {
    displayValue = renderValue(selectedOption);
    filled = true;
  }

  const indicator = React.useMemo(() => {
    if (indicatorProp) {
      return typeof indicatorProp === 'function' ? indicatorProp(listboxOpen) : indicatorProp;
    } else {
      return listboxOpen ? <KeyboardArrowUp color="inherit" /> : <KeyboardArrowDown color="inherit" />;
    }
  }, [indicatorProp, listboxOpen]);

  const ownerState: DropdownOwnerState<Value, Multiple> = {
    ...props,
    active: buttonActive,
    defaultListboxOpen,
    disabled,
    focusVisible: buttonFocusVisible,
    open: listboxOpen,
    renderValue,
    value,
    size,
    error,
    readOnly,
    filled,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? DropdownRoot;
  const SlotButton = slots.button ?? DropdownButton;
  const SlotList = slots.listbox ?? DropdownList;
  const SlotStartDecorator = slots.startDecorator ?? DropdownStartDecorator;
  const SlotEndDecorator = slots.endDecorator ?? DropdownEndDecorator;
  const SlotIndicator = slots.indicator ?? DropdownIndicator;
  const SlotErrorStateIcon = slots.errorStateIcon ?? ErrorStateIcon;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  const slotButtonProps = useSlotProps({
    elementType: SlotButton,
    externalSlotProps: slotProps.button,
    getSlotProps: getButtonProps,
    ownerState,
    className: classes.button,
    additionalProps: {
      'aria-describedby': ariaDescribedby,
      'aria-label': ariaLabel,
      'aria-labelledby': ariaLabelledby ?? formControl?.labelId,
      'aria-required': required ? 'true' : undefined,
      id: id ?? formControl?.htmlFor,
      name,
    },
  });

  const slotListProps = useSlotProps({
    elementType: SlotList,
    externalSlotProps: slotProps.listbox,
    ownerState,
    className: classes.listbox,
    getSlotProps: getListboxProps,
    additionalProps: {
      anchorEl,
      open: listboxOpen,
      placement: 'bottom' as const,
      keepMounted: true,
    },
  });

  const slotStartDecoratorProps = useSlotProps({
    elementType: SlotStartDecorator,
    externalSlotProps: slotProps.startDecorator,
    ownerState,
    className: classes.startDecorator,
  });

  const slotEndDecoratorProps = useSlotProps({
    elementType: SlotEndDecorator,
    externalSlotProps: slotProps.endDecorator,
    ownerState,
    className: classes.endDecorator,
  });

  const slotIndicatorProps = useSlotProps({
    elementType: SlotIndicator,
    externalSlotProps: slotProps.indicator,
    ownerState,
    className: classes.indicator,
  });

  const slotErrorStateIconProps = useSlotProps({
    elementType: SlotErrorStateIcon,
    externalSlotProps: slotProps.errorStateIcon,
    ownerState,
    className: classes.errorStateIcon,
  });

  const { modifiers = [] } = (slotProps.listbox || {}) as Omit<PopperOwnProps, 'slots' | 'slotProps' | 'open'>;

  const mergedModifiers = React.useMemo(() => [...defaultModifiers, ...(modifiers || [])], [modifiers]);

  return (
    <React.Fragment>
      <SlotRoot {...slotRootProps}>
        {startDecorator && <SlotStartDecorator {...slotStartDecoratorProps}>{startDecorator}</SlotStartDecorator>}
        <SlotButton {...slotButtonProps}>{displayValue}</SlotButton>
        {showErrorIcon && error && !(disabled || readOnly) && (
          <SlotErrorStateIcon {...slotErrorStateIconProps}>
            <ErrorIcon />
          </SlotErrorStateIcon>
        )}
        {endDecorator && <SlotEndDecorator {...slotEndDecoratorProps}>{endDecorator}</SlotEndDecorator>}

        {indicator && <SlotIndicator {...slotIndicatorProps}>{indicator}</SlotIndicator>}

        <input {...getHiddenInputProps()} />
      </SlotRoot>
      {anchorEl && (
        <SlotList
          {...slotListProps}
          className={clsx(slotListProps.className)}
          modifiers={mergedModifiers}
          {...(!props.slots?.listbox && {
            as: Popper,
            slots: { root: slotListProps.as || 'ul' },
          })}
        >
          <SelectProvider value={contextValue}>
            <GroupListContext.Provider value="select">
              <ListProvider nested>{children}</ListProvider>
            </GroupListContext.Provider>
          </SelectProvider>
        </SlotList>
      )}
    </React.Fragment>
  );
};

// eslint-disable-next-line react/display-name
export const Dropdown = React.forwardRef(DropdownInner) as <Value, Multiple extends boolean = false>(
  props: DropdownProps<Value, Multiple> & React.RefAttributes<HTMLDivElement>,
) => React.ReactElement;
