import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent, getByRole } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Autocomplete } from './Autocomplete';
import { TextField } from '../TextField';

afterEach(() => {
  cleanup();
});

describe('<Autocomplete />', () => {
  describe('combobox', () => {
    it('should clear the input when blur', () => {
      render(<Autocomplete options={[]} renderInput={(params) => <TextField {...params} />} />);
      const textbox = screen.getByRole('combobox');
      fireEvent.focus(textbox);
      fireEvent.change(textbox, { target: { value: 'test' } });
      expect(textbox).toHaveValue('test');
      fireEvent.blur(textbox);
      expect(textbox).toHaveValue('');
    });
    it('should apply the icon classes', () => {
      render(
        <Autocomplete
          value="one"
          data-testid="autocomplete"
          options={['one', 'two', 'three']}
          renderInput={(params) => <TextField {...params} />}
        />,
      );
      expect(screen.getByTestId('autocomplete')).toHaveClass('NovaAutocomplete-hasClearIcon');
      expect(screen.getByTestId('autocomplete')).toHaveClass('NovaAutocomplete-hasPopupIcon');
    });
  });

  describe('prop: loading', () => {
    it('should show the loading indicator when open', () => {
      render(
        <Autocomplete
          open
          loading
          options={[]}
          slotProps={{ loading: { 'data-testid': 'NovaAutocomplete-loading' } }}
          renderInput={(params) => <TextField {...params} />}
        />,
      );
      const textbox = screen.getByRole('combobox');
      fireEvent.focus(textbox);
      expect(screen.getByTestId('NovaAutocomplete-loading')).toHaveTextContent('Loading…');
    });

    it('should show the loading option when open', () => {
      render(<Autocomplete open loading options={[]} renderInput={(params) => <TextField {...params} />} />);
      const textbox = screen.getByRole('combobox');
      fireEvent.focus(textbox);
      expect(screen.getByText('Loading…')).toBeInTheDocument();
    });
  });

  describe('prop: noOptionsText', () => {
    it('should show the no options text', () => {
      render(
        <Autocomplete
          open
          noOptionsText="No options"
          options={[]}
          slotProps={{ noOptions: { 'data-testid': 'NovaAutocomplete-noOptions' } }}
          renderInput={(params) => <TextField {...params} />}
        />,
      );
      expect(screen.getByTestId('NovaAutocomplete-noOptions')).toHaveTextContent('No options');
    });
  });

  describe('prop: autoHighlight', () => {
    it('should set the focus on the first item', () => {
      render(
        <Autocomplete
          open
          autoHighlight
          options={['one', 'two', 'three']}
          renderInput={(params) => <TextField {...params} />}
        />,
      );
      const textbox = screen.getByRole('combobox');
      fireEvent.focus(textbox);
      expect(screen.getByRole('option', { name: 'one' })).toHaveClass('Mui-focused');
    });

    it('should keep the highlight on the first item', () => {
      render(
        <Autocomplete
          open
          autoHighlight
          options={['one', 'two', 'three']}
          renderInput={(params) => <TextField {...params} />}
        />,
      );
      const textbox = screen.getByRole('combobox');
      fireEvent.focus(textbox);
      fireEvent.change(textbox, { target: { value: 'two' } });
      expect(screen.getByRole('option', { name: 'two' })).toHaveClass('Mui-focused');
    });
  });

  describe('prop: limitTags', () => {
    it('show all items on focus', () => {
      render(
        <Autocomplete
          multiple
          limitTags={2}
          options={['one', 'two', 'three']}
          defaultValue={['one', 'two', 'three']}
          renderInput={(params) => <TextField data-testid="textbox" {...params} />}
        />,
      );
      expect(screen.getByTestId('textbox')).toHaveTextContent('onetwo+1');
    });

    it('show 0 item on close when set 0 to limitTags', () => {
      render(
        <Autocomplete
          multiple
          limitTags={0}
          options={['one', 'two', 'three']}
          defaultValue={['one', 'two', 'three']}
          renderInput={(params) => <TextField data-testid="textbox" {...params} />}
        />,
      );

      expect(screen.getByTestId('textbox')).toHaveTextContent('+3');
      const textbox = screen.getByRole('combobox');
      fireEvent.focus(textbox);
      expect(screen.getByTestId('textbox')).toHaveTextContent('onetwothree');
    });
  });

  describe('prop: filterSelectedOptions', () => {
    it('should not show the selected options', () => {
      render(
        <Autocomplete
          filterSelectedOptions
          options={['one', 'two', 'three']}
          renderInput={(params) => <TextField {...params} />}
        />,
      );
      const textbox = screen.getByRole('combobox');
      fireEvent.focus(textbox);
      fireEvent.keyDown(textbox, { key: 'Enter' });
      expect(screen.queryByRole('option', { name: 'one' })).not.toBeInTheDocument();
    });
  });

  describe('prop: autoSelect', () => {
    it('should add new value when autoSelect on blur', () => {
      const handleChange = vi.fn();
      const options = ['one', 'two'];
      render(
        <Autocomplete
          autoSelect
          value={options[0]}
          openOnFocus
          options={options}
          onChange={handleChange}
          renderInput={(params) => <TextField {...params} autoFocus />}
        />,
      );

      const textbox = screen.getByRole('combobox');
      fireEvent.change(textbox, { target: { value: 't' } });
      fireEvent.keyDown(textbox, { key: 'ArrowDown' });
      fireEvent.blur(textbox);
      expect(handleChange).toBeCalledTimes(1);
    });

    it('should add new value when autoSelect freeSolo on blur', () => {
      const handleChange = vi.fn();
      render(
        <Autocomplete
          autoSelect
          freeSolo
          onChange={handleChange}
          options={[]}
          renderInput={(params) => <TextField {...params} autoFocus />}
        />,
      );
      const textbox = screen.getByRole('combobox');
      fireEvent.change(textbox, { target: { value: 't' } });
      fireEvent.blur(textbox);
      expect(handleChange).toBeCalledTimes(1);
      expect(textbox).toHaveValue('t');
    });
  });

  describe('prop: multiple', () => {
    it('should remove the last option', () => {
      const handleChange = vi.fn();
      const options = ['one', 'two'];
      const { getAllByTestId } = render(
        <Autocomplete
          options={[]}
          defaultValue={options}
          onChange={handleChange}
          renderInput={(params) => <TextField {...params} />}
          multiple
        />,
      );
      fireEvent.click(getAllByTestId('CloseOutlinedIcon')[1]);
      expect(handleChange).toBeCalledTimes(1);
    });

    it('deletes a focused tag when pressing the delete key', () => {
      const handleChange = vi.fn();
      const options = ['one', 'two'];
      render(
        <Autocomplete
          options={[]}
          defaultValue={options}
          onChange={handleChange}
          renderInput={(params) => <TextField {...params} />}
          multiple
        />,
      );
      const textbox = screen.getByRole('combobox');
      const [firstSelectedValue, secondSelectedValue] = screen.getAllByRole('button');
      // check that no tags get deleted when the tag is not a focused tag
      fireEvent.keyDown(textbox, { key: 'Delete' });
      expect(handleChange).toBeCalledTimes(0);

      fireEvent.keyDown(textbox, { key: 'ArrowLeft' });
      fireEvent.keyDown(textbox, { key: 'Delete' });
      expect(handleChange).toBeCalledTimes(1);
    });

    it('has no textbox value', () => {
      render(
        <Autocomplete
          options={['one', 'two', 'three']}
          renderInput={(params) => <TextField {...params} />}
          multiple
          value={['one', 'two']}
        />,
      );
      expect(screen.getByRole('combobox')).to.have.property('value', '');
    });
  });

  describe('prop: clearOnEscape', () => {
    it('should clear the input when escape is pressed', () => {
      render(
        <Autocomplete
          options={['one', 'two', 'three']}
          renderInput={(params) => <TextField {...params} />}
          multiple
          value={['one', 'two']}
          clearOnEscape
        />,
      );
      const textbox = screen.getByRole('combobox');
      fireEvent.focus(textbox);
      fireEvent.keyDown(textbox, { key: 'Escape' });
      expect(screen.getByRole('combobox')).toHaveValue('');
    });
  });

  // prop: clearOnBlur
  describe('prop: clearOnBlur', () => {
    it('should clear the input when blur', () => {
      render(<Autocomplete options={['one', 'two']} renderInput={(params) => <TextField {...params} />} clearOnBlur />);
      const textbox = screen.getByRole('combobox');
      fireEvent.focus(textbox);
      fireEvent.change(textbox, { target: { value: 'test' } });
      fireEvent.blur(textbox);
      expect(screen.getByRole('combobox')).toHaveValue('');
    });
  });

  describe('prop: openOnFocus', () => {
    it('should open the dropdown when focus', () => {
      render(<Autocomplete openOnFocus options={['one', 'two']} renderInput={(params) => <TextField {...params} />} />);
      const textbox = screen.getByRole('combobox');
      fireEvent.focus(textbox);
      expect(screen.getByRole('option', { name: 'two' })).toBeInTheDocument();
    });
  });

  describe('prop: disabled', () => {
    it('should not open the dropdown when focus', () => {
      render(<Autocomplete disabled options={['one', 'two']} renderInput={(params) => <TextField {...params} />} />);
      const textbox = screen.getByRole('combobox');
      expect(textbox).toHaveClass('Mui-disabled');
    });
  });

  describe('prop: disableClearable', () => {
    it('should not show the clear icon', () => {
      render(
        <Autocomplete disableClearable options={['one', 'two']} renderInput={(params) => <TextField {...params} />} />,
      );
      expect(screen.queryByRole('button', { name: 'Clear' })).not.toBeInTheDocument();
    });
  });

  describe('prop: options', () => {
    it('should show the options', () => {
      render(<Autocomplete open options={['one', 'two']} renderInput={(params) => <TextField {...params} />} />);
      expect(screen.getByRole('option', { name: 'one' })).toBeInTheDocument();
    });
  });

  describe('prop: controlled', () => {
    it('controls the input value', () => {
      const handleChange = vi.fn();
      function MyComponent() {
        const [, setInputValue] = React.useState('');
        const handleInputChange = (event, value) => {
          handleChange(value);
          setInputValue(value);
        };
        return (
          <Autocomplete
            options={[]}
            inputValue=""
            onInputChange={handleInputChange}
            renderInput={(params) => <TextField {...params} autoFocus />}
          />
        );
      }
      render(<MyComponent />);

      // call handleChange
      expect(handleChange).toBeCalledTimes(0);

      fireEvent.change(screen.getByRole('combobox'), { target: { value: 'a' } });
      expect(handleChange).toBeCalledTimes(1);
    });
    it('should fire the input change event before the change event', () => {
      const handleChange = vi.fn();
      const handleInputChange = vi.fn();
      render(
        <Autocomplete
          options={[]}
          inputValue=""
          onInputChange={handleInputChange}
          onChange={handleChange}
          renderInput={(params) => <TextField {...params} />}
        />,
      );
      fireEvent.change(screen.getByRole('combobox'), { target: { value: 'a' } });
      expect(handleInputChange).toBeCalledTimes(1);
    });
  });

  describe('prop: freeSolo', () => {
    it('pressing twice enter should not call onChange listener twice', () => {
      const handleChange = vi.fn();
      const options = [{ name: 'foo' }];
      render(
        <Autocomplete
          freeSolo
          onChange={handleChange}
          open
          options={options}
          getOptionLabel={(option: any) => option.name}
          renderInput={(params) => <TextField {...params} autoFocus />}
        />,
      );
      const textbox = screen.getByRole('combobox');
      fireEvent.keyDown(textbox, { key: 'ArrowDown' });
      fireEvent.keyDown(textbox, { key: 'Enter' });
      expect(handleChange).toBeCalledTimes(1);
      fireEvent.keyDown(textbox, { key: 'Enter' });
      expect(handleChange).toBeCalledTimes(1);
    });
    it('should not delete exiting tag when try to add it twice', () => {
      const handleChange = vi.fn();
      const options = ['one', 'two'];
      const { container } = render(
        <Autocomplete
          defaultValue={options}
          options={options}
          onChange={handleChange}
          freeSolo
          renderInput={(params) => <TextField {...params} autoFocus />}
          multiple
        />,
      );
      const textbox = screen.getByRole('combobox');
      fireEvent.change(textbox, { target: { value: 'three' } });
      fireEvent.keyDown(textbox, { key: 'Enter' });
      expect(container.querySelectorAll('[class*="NovaChip-root"]')).to.have.length(3);
      fireEvent.change(textbox, { target: { value: 'three' } });
      fireEvent.keyDown(textbox, { key: 'Enter' });
      expect(container.querySelectorAll('[class*="NovaChip-root"]')).to.have.length(3);
    });
  });

  describe('prop: onChange', () => {
    it('provides a reason and details on option creation', () => {
      const handleChange = vi.fn();
      const options = ['one', 'two', 'three'];
      render(
        <Autocomplete
          freeSolo
          onChange={handleChange}
          options={options}
          renderInput={(params) => <TextField {...params} autoFocus />}
        />,
      );
      const textbox = screen.getByRole('combobox');
      fireEvent.change(textbox, { target: { value: options[2] } });
      fireEvent.keyDown(textbox, { key: 'Enter' });
      expect(handleChange).toBeCalledTimes(1);
      expect(handleChange.mock.calls[0][1]).to.equal(options[2]);
      expect(handleChange.mock.calls[0][2]).to.equal('createOption');
      expect(handleChange.mock.calls[0][3]).to.deep.equal({ option: options[2] });
    });
    it('provides a reason and details on option selection', () => {
      const handleChange = vi.fn();
      const options = ['one', 'two', 'three'];
      render(
        <Autocomplete
          onChange={handleChange}
          options={options}
          renderInput={(params) => <TextField {...params} autoFocus />}
        />,
      );
      const textbox = screen.getByRole('combobox');

      fireEvent.keyDown(textbox, { key: 'ArrowDown' });
      fireEvent.keyDown(textbox, { key: 'ArrowDown' });
      fireEvent.keyDown(textbox, { key: 'Enter' });

      expect(handleChange).toBeCalledTimes(1);
      expect(handleChange.mock.calls[0][1]).to.equal(options[0]);
      expect(handleChange.mock.calls[0][2]).to.equal('selectOption');
      expect(handleChange.mock.calls[0][3]).to.deep.equal({ option: options[0] });
    });
  });

  describe('prop: onInputChange', () => {
    it('provides a reason on input change', async () => {
      const handleInputChange = vi.fn();
      const options = [{ name: 'foo' }];
      render(
        <Autocomplete
          onInputChange={handleInputChange}
          options={options}
          getOptionLabel={(option: any) => option.name}
          renderInput={(params) => <TextField {...params} autoFocus />}
        />,
      );
      fireEvent.change(screen.getByRole('combobox'), { target: { value: 'a' } });

      expect(handleInputChange).toBeCalledTimes(1);
      expect(handleInputChange.mock.calls[0][1]).to.equal('a');
      expect(handleInputChange.mock.calls[0][2]).to.equal('input');
    });
  });

  describe('prop: blurOnSelect', () => {
    it('[blurOnSelect=true] should blur the input when clicking', () => {
      const options = [{ name: 'foo' }];
      render(
        <Autocomplete
          openOnFocus
          options={options}
          getOptionLabel={(option: any) => option.name}
          renderInput={(params) => <TextField {...params} autoFocus />}
          blurOnSelect
        />,
      );
      const textbox = screen.getByRole('combobox');
      const firstOption = screen.getByRole('option');
      expect(textbox).toHaveFocus();
      fireEvent.click(firstOption);
      expect(textbox).not.toHaveFocus();
    });
  });

  describe('prop: getOptionLabel', () => {
    it('should return the label of the option', () => {
      render(
        <Autocomplete
          open
          options={[0, 10, 20]}
          getOptionLabel={(option: any) => (option === 0 ? 'Any' : option.toString())}
          renderInput={(params) => <TextField {...params} />}
          value={0}
        />,
      );
      const options = screen.getAllByRole('option');
      expect(options).to.have.length(3);
    });
  });

  describe('prop: readOnly', () => {
    it('should not open the dropdown when focus', () => {
      render(<Autocomplete readOnly options={['one', 'two']} renderInput={(params) => <TextField {...params} />} />);
      const textbox = screen.getByRole('combobox');
      expect(textbox).toHaveAttribute('readonly');
    });
  });
});
