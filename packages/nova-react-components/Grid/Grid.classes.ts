import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface GridClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if 'container={true}'. */
  container: string;
}

export type GridClassKey = keyof GridClasses;

export function getGridUtilityClass(slot: string): string {
  return generateUtilityClass('NovaGrid', slot);
}

const gridClasses: GridClasses = generateUtilityClasses('NovaGrid', ['root', 'container']);

export default gridClasses;
