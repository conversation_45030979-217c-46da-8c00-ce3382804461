import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Grid } from './Grid';

vi.mock('@pigment-css/react/Grid', () => {
  return {
    default: (props) => <div {...props} />,
  };
});

afterEach(() => {
  cleanup();
});

describe('Grid', () => {
  it('should render normal', () => {
    render(<Grid data-testid="NovaGrid-root" />);
    expect(screen.getByTestId('NovaGrid-root')).toHaveClass('NovaGrid-root');
  });
});
