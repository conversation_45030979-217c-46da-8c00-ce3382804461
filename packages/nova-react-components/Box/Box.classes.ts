import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface BoxClasses {
  /** Class name applied to the root element. */
  root: string;
}

export type BoxClassKey = keyof BoxClasses;

export function getBoxUtilityClass(slot: string): string {
  return generateUtilityClass('NovaBox', slot);
}

const boxClasses: BoxClasses = generateUtilityClasses('NovaBox', ['root']);

export default boxClasses;
