'use client';
import * as React from 'react';

export type RtlProviderProps = React.PropsWithChildren<{
  rtl?: boolean;
}>;

const RtlContext = React.createContext({
  rtl: false,
});

function RtlProvider({ rtl, children }: RtlProviderProps) {
  return <RtlContext.Provider value={{ rtl: rtl ?? false }}>{children}</RtlContext.Provider>;
}

export const useRtl = () => {
  const { rtl } = React.useContext(RtlContext);
  return rtl;
};

export default RtlProvider;
