'use client';
import * as React from 'react';
import clsx from 'clsx';
import { Transition } from 'react-transition-group';
import useTimeout from '@mui/utils/useTimeout';
import composeClasses from '@mui/utils/composeClasses';
import { styled } from '@pigment-css/react';
import { duration, getTransitionProps, getAutoHeightDuration } from '../transitions/utils';
import { getCollapseUtilityClass } from './Collapse.classes';
import { CollapseOwnerState, CollapseProps, CollapseTypeMap } from './Collapse.types';
import { OverridableComponent } from '@mui/types';
import useForkRef from '@mui/utils/useForkRef';
import { useDefaultProps } from '@mui/system/DefaultPropsProvider';

const useUtilityClasses = (ownerState: CollapseOwnerState) => {
  const { orientation, classes } = ownerState;

  const slots = {
    root: ['root', `${orientation}`],
    entered: ['entered'],
    hidden: ['hidden'],
    wrapper: ['wrapper', `${orientation}`],
    wrapperInner: ['wrapperInner', `${orientation}`],
  };

  return composeClasses(slots, getCollapseUtilityClass, classes);
};

const CollapseRoot = styled('div', {
  name: 'NovaCollapse',
  slot: 'Root',
  overridesResolver: (props, styles) => {
    const { ownerState } = props;
    return [
      styles.root,
      styles[ownerState.orientation],
      ownerState.state === 'entered' && styles.entered,
      ownerState.state === 'exited' && !ownerState.in && ownerState.collapsedSize === '0px' && styles.hidden,
    ];
  },
})<CollapseOwnerState>(({ theme }) => ({
  height: 0,
  overflow: 'hidden',
  transition: 'height 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms',
  variants: [
    {
      props: {
        orientation: 'horizontal',
      },
      style: {
        height: 'auto',
        width: 0,
        transition: 'width 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms',
      },
    },
    {
      props: {
        state: 'entered',
      },
      style: {
        height: 'auto',
        overflow: 'visible',
      },
    },
    {
      props: {
        state: 'entered',
        orientation: 'horizontal',
      },
      style: {
        width: 'auto',
      },
    },
    {
      props: ({ ownerState }) => ownerState.state === 'exited' && !ownerState.in && ownerState.collapsedSize === '0px',
      style: {
        visibility: 'hidden',
      },
    },
  ],
}));

const CollapseWrapper = styled('div', {
  name: 'NovaCollapse',
  slot: 'Wrapper',
  overridesResolver: (_, styles) => styles.wrapper,
})<CollapseOwnerState>({
  // Hack to get children with a negative margin to not falsify the height computation.
  display: 'flex',
  width: '100%',
  variants: [
    {
      props: {
        orientation: 'horizontal',
      },
      style: {
        width: 'auto',
        height: '100%',
      },
    },
  ],
});

const CollapseWrapperInner = styled('div', {
  name: 'NovaCollapse',
  slot: 'WrapperInner',
  overridesResolver: (_, styles) => styles.wrapperInner,
})<CollapseOwnerState>({
  width: '100%',
  variants: [
    {
      props: {
        orientation: 'horizontal',
      },
      style: {
        width: 'auto',
        height: '100%',
      },
    },
  ],
});

// eslint-disable-next-line react/display-name
export const Collapse = React.forwardRef((props: CollapseProps, ref: React.ForwardedRef<Element>) => {
  const {
    addEndListener,
    children,
    className,
    collapsedSize: collapsedSizeProp = '0px',
    component,
    easing,
    in: inProp,
    onEnter,
    onEntered,
    onEntering,
    onExit,
    onExited,
    onExiting,
    orientation = 'vertical',
    style,
    timeout = duration.standard,
    TransitionComponent = Transition,
    ...other
  } = useDefaultProps({ props, name: 'NovaCollapse' });

  const ownerState = {
    ...props,
    orientation,
    collapsedSize: collapsedSizeProp,
  };

  const classes = useUtilityClasses(ownerState);

  const timer = useTimeout();
  const wrapperRef = React.useRef<HTMLDivElement>(null);
  const autoTransitionDuration = React.useRef<number>();
  const collapsedSize = typeof collapsedSizeProp === 'number' ? `${collapsedSizeProp}px` : collapsedSizeProp;
  const isHorizontal = orientation === 'horizontal';
  const size = isHorizontal ? 'width' : 'height';

  const nodeRef = React.useRef<HTMLElement>(null);
  const handleRef = useForkRef(ref, nodeRef);

  const normalizedTransitionCallback =
    (callback?: (node: HTMLElement, isAppearing?: boolean) => void) => (maybeIsAppearing?: boolean) => {
      if (callback) {
        const node = nodeRef.current;

        // onEnterXxx and onExitXxx callbacks have a different arguments.length value.
        if (maybeIsAppearing === undefined) {
          callback(node!);
        } else {
          callback(node!, maybeIsAppearing);
        }
      }
    };

  const getWrapperSize = () =>
    wrapperRef.current ? wrapperRef.current[isHorizontal ? 'clientWidth' : 'clientHeight'] : 0;

  const handleEnter = normalizedTransitionCallback((node: HTMLElement, isAppearing?: boolean) => {
    if (wrapperRef.current && isHorizontal) {
      // Set absolute position to get the size of collapsed content
      wrapperRef.current.style.position = 'absolute';
    }
    node.style[size] = collapsedSize;

    if (onEnter) {
      onEnter(node, isAppearing);
    }
  });

  const handleEntering = normalizedTransitionCallback((node: HTMLElement, isAppearing: boolean) => {
    const wrapperSize = getWrapperSize();

    if (wrapperRef.current && isHorizontal) {
      // After the size is read reset the position back to default
      wrapperRef.current.style.position = '';
    }

    if (timeout === 'auto') {
      const duration2 = getAutoHeightDuration(wrapperSize);
      node.style.transitionDuration = `${duration2}ms`;
      autoTransitionDuration.current = duration2;
    } else {
      const { duration: transitionDuration, easing: transitionTimingFunction } = getTransitionProps(
        { style, timeout: timeout as number | { enter?: number; exit?: number }, easing },
        {
          mode: 'enter',
        },
      );

      node.style.transitionDuration =
        typeof transitionDuration === 'string' ? transitionDuration : `${transitionDuration}ms`;
      node.style.transitionTimingFunction = transitionTimingFunction;
    }

    node.style[size] = `${wrapperSize}px`;

    if (onEntering) {
      onEntering(node, isAppearing);
    }
  });

  const handleEntered = normalizedTransitionCallback((node: HTMLElement, isAppearing?: boolean) => {
    node.style[size] = 'auto';

    if (onEntered) {
      onEntered(node, isAppearing);
    }
  });

  const handleExit = normalizedTransitionCallback((node: HTMLElement) => {
    node.style[size] = `${getWrapperSize()}px`;

    if (onExit) {
      onExit(node);
    }
  });

  const handleExited = normalizedTransitionCallback(onExited);

  const handleExiting = normalizedTransitionCallback((node: HTMLElement) => {
    const wrapperSize = getWrapperSize();

    if (timeout === 'auto') {
      // TODO: rename getAutoHeightDuration to something more generic (width support)
      // Actually it just calculates animation duration based on size
      const duration2 = getAutoHeightDuration(wrapperSize);
      node.style.transitionDuration = `${duration2}ms`;
      autoTransitionDuration.current = duration2;
    } else {
      const { duration: transitionDuration, easing: transitionTimingFunction } = getTransitionProps(
        { style, timeout: timeout as number | { enter?: number; exit?: number }, easing },
        {
          mode: 'exit',
        },
      );

      node.style.transitionDuration =
        typeof transitionDuration === 'string' ? transitionDuration : `${transitionDuration}ms`;
      node.style.transitionTimingFunction = transitionTimingFunction;
    }

    node.style[size] = collapsedSize;

    if (onExiting) {
      onExiting(node);
    }
  });

  const handleAddEndListener = (next: () => void) => {
    if (timeout === 'auto') {
      timer.start(autoTransitionDuration.current || 0, next);
    }
    if (addEndListener) {
      // Old call signature before `react-transition-group` implemented `nodeRef`
      addEndListener(nodeRef.current!, next);
    }
  };

  return (
    <TransitionComponent
      in={inProp}
      onEnter={handleEnter}
      onEntered={handleEntered}
      onEntering={handleEntering}
      onExit={handleExit}
      onExited={handleExited}
      onExiting={handleExiting}
      addEndListener={handleAddEndListener}
      nodeRef={nodeRef}
      timeout={timeout === 'auto' ? null : timeout}
      {...other}
    >
      {(state: string, childProps: any) => (
        <CollapseRoot
          as={component}
          className={clsx(
            classes.root,
            {
              [classes.entered]: state === 'entered',
              [classes.hidden]: state === 'exited' && !inProp && collapsedSize === '0px',
            },
            className,
          )}
          style={{
            [isHorizontal ? 'minWidth' : 'minHeight']: collapsedSize,
            ...style,
          }}
          ref={handleRef}
          {...childProps}
          // `ownerState` is set after `childProps` to override any existing `ownerState` property in `childProps`
          // that might have been forwarded from the Transition component.
          ownerState={{ ...ownerState, state }}
        >
          <CollapseWrapper ownerState={{ ...ownerState, state }} className={classes.wrapper} ref={wrapperRef}>
            <CollapseWrapperInner ownerState={{ ...ownerState, state }} className={classes.wrapperInner}>
              {children}
            </CollapseWrapperInner>
          </CollapseWrapper>
        </CollapseRoot>
      )}
    </TransitionComponent>
  );
}) as OverridableComponent<CollapseTypeMap>;
