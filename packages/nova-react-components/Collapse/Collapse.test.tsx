/// <reference types="@testing-library/jest-dom" />
import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Collapse } from './Collapse';

// Mock the react-transition-group
vi.mock('react-transition-group', () => ({
  Transition: ({ children, in: inProp, onEnter, onEntered, onExit, onExited, ...props }: any) => {
    const state = inProp ? 'entered' : 'exited';

    React.useEffect(() => {
      if (inProp && onEnter) {
        onEnter();
      }
      if (inProp && onEntered) {
        onEntered();
      }
      if (!inProp && onExit) {
        onExit();
      }
      if (!inProp && onExited) {
        onExited();
      }
    }, [inProp, onEnter, onEntered, onExit, onExited]);

    return children(state, props);
  },
}));

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Collapse', () => {
  it('renders children when in is true', () => {
    render(
      <Collapse in>
        <div data-testid="collapse-content">Collapse content</div>
      </Collapse>,
    );

    expect(screen.getByTestId('collapse-content')).toBeInTheDocument();
    expect(screen.getByText('Collapse content')).toBeInTheDocument();
  });

  it('renders children when in is false', () => {
    render(
      <Collapse in={false}>
        <div data-testid="collapse-content">Collapse content</div>
      </Collapse>,
    );

    expect(screen.getByTestId('collapse-content')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(
      <Collapse in className="custom-collapse">
        <div>Content</div>
      </Collapse>,
    );

    const collapseRoot = screen.getByText('Content').closest('[class*="NovaCollapse-root"]');
    expect(collapseRoot).toHaveClass('custom-collapse');
  });

  it('renders with vertical orientation by default', () => {
    render(
      <Collapse in>
        <div>Content</div>
      </Collapse>,
    );

    const collapseRoot = screen.getByText('Content').closest('[class*="NovaCollapse-root"]');
    expect(collapseRoot).toHaveClass('NovaCollapse-vertical');
  });

  it('renders with horizontal orientation', () => {
    render(
      <Collapse in orientation="horizontal">
        <div>Content</div>
      </Collapse>,
    );

    const collapseRoot = screen.getByText('Content').closest('[class*="NovaCollapse-root"]');
    expect(collapseRoot).toHaveClass('NovaCollapse-horizontal');
  });

  it('applies collapsedSize as string', () => {
    render(
      <Collapse in={false} collapsedSize="20px">
        <div>Content</div>
      </Collapse>,
    );

    const collapseRoot = screen.getByText('Content').closest('[class*="NovaCollapse-root"]');
    expect(collapseRoot).toHaveStyle('min-height: 20px');
  });

  it('applies collapsedSize as number', () => {
    render(
      <Collapse in={false} collapsedSize={30}>
        <div>Content</div>
      </Collapse>,
    );

    const collapseRoot = screen.getByText('Content').closest('[class*="NovaCollapse-root"]');
    expect(collapseRoot).toHaveStyle('min-height: 30px');
  });

  it('applies minWidth for horizontal orientation', () => {
    render(
      <Collapse in={false} orientation="horizontal" collapsedSize="50px">
        <div>Content</div>
      </Collapse>,
    );

    const collapseRoot = screen.getByText('Content').closest('[class*="NovaCollapse-root"]');
    expect(collapseRoot).toHaveStyle('min-width: 50px');
  });

  it('calls onEnter callback when entering', () => {
    const onEnter = vi.fn();

    render(
      <Collapse in onEnter={onEnter}>
        <div>Content</div>
      </Collapse>,
    );

    expect(onEnter).toHaveBeenCalled();
  });

  it('calls onEntered callback when entered', () => {
    const onEntered = vi.fn();

    render(
      <Collapse in onEntered={onEntered}>
        <div>Content</div>
      </Collapse>,
    );

    expect(onEntered).toHaveBeenCalled();
  });

  it('calls onExit callback when exiting', () => {
    const onExit = vi.fn();

    render(
      <Collapse in={false} onExit={onExit}>
        <div>Content</div>
      </Collapse>,
    );

    expect(onExit).toHaveBeenCalled();
  });

  it('calls onExited callback when exited', () => {
    const onExited = vi.fn();

    render(
      <Collapse in={false} onExited={onExited}>
        <div>Content</div>
      </Collapse>,
    );

    expect(onExited).toHaveBeenCalled();
  });

  it('renders with custom component', () => {
    const CustomComponent = React.forwardRef<HTMLDivElement, any>(({ children, ...props }, ref) => (
      <div ref={ref} data-testid="custom-component" {...props}>
        {children}
      </div>
    ));

    render(
      <Collapse in component={CustomComponent}>
        <div>Content</div>
      </Collapse>,
    );

    expect(screen.getByTestId('custom-component')).toBeInTheDocument();
  });

  it('applies entered class when state is entered', () => {
    render(
      <Collapse in>
        <div>Content</div>
      </Collapse>,
    );

    const collapseRoot = screen.getByText('Content').closest('[class*="NovaCollapse-root"]');
    expect(collapseRoot).toHaveClass('NovaCollapse-entered');
  });

  it('applies hidden class when exited and collapsedSize is 0px', () => {
    render(
      <Collapse in={false} collapsedSize="0px">
        <div>Content</div>
      </Collapse>,
    );

    const collapseRoot = screen.getByText('Content').closest('[class*="NovaCollapse-root"]');
    expect(collapseRoot).toHaveClass('NovaCollapse-hidden');
  });

  it('does not apply hidden class when collapsedSize is not 0px', () => {
    render(
      <Collapse in={false} collapsedSize="20px">
        <div>Content</div>
      </Collapse>,
    );

    const collapseRoot = screen.getByText('Content').closest('[class*="NovaCollapse-root"]');
    expect(collapseRoot).not.toHaveClass('NovaCollapse-hidden');
  });

  it('handles timeout prop as number', () => {
    render(
      <Collapse in timeout={500}>
        <div>Content</div>
      </Collapse>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('handles timeout prop as object', () => {
    render(
      <Collapse in timeout={{ enter: 300, exit: 500 }}>
        <div>Content</div>
      </Collapse>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('handles auto timeout', () => {
    render(
      <Collapse in timeout="auto">
        <div>Content</div>
      </Collapse>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('forwards ref to the root element', () => {
    const ref = React.createRef<HTMLDivElement>();

    render(
      <Collapse in ref={ref}>
        <div>Content</div>
      </Collapse>,
    );

    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });

  it('spreads additional props to root element', () => {
    render(
      <Collapse in data-testid="collapse-root" id="test-collapse">
        <div>Content</div>
      </Collapse>,
    );

    const collapseRoot = screen.getByTestId('collapse-root');
    expect(collapseRoot).toHaveAttribute('id', 'test-collapse');
  });

  it('applies easing prop', () => {
    const easing = { enter: 'ease-in', exit: 'ease-out' };

    render(
      <Collapse in easing={easing}>
        <div>Content</div>
      </Collapse>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });
});
