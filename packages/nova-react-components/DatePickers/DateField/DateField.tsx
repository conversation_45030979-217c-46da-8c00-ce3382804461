'use client';
import React from 'react';
import { unstable_useId as useId, unstable_composeClasses as composeClasses } from '@mui/utils';
import clsx from 'clsx';
import { TextField } from '../../TextField';
import { DateFieldOwnerState, DateFieldProps } from './DateField.types';
import { CalendarIcon, ClearIcon } from '../icons';
import { getDateFieldUtilityClass } from './DateField.classes';
import { useFieldIntegration } from '../hooks/useFieldHelpers/useFieldIntegration';

// Utility function to create CSS classes for the component
const useUtilityClasses = (ownerState: DateFieldOwnerState) => {
  const { error, disabled } = ownerState;

  const slots = {
    root: ['root', error && 'error', disabled && 'disabled'],
    input: ['input'],
  };

  return composeClasses(slots, getDateFieldUtilityClass, {});
};

// eslint-disable-next-line react/display-name
export const DateField: React.FC<DateFieldProps> = (props) => {
  const {
    className,
    component = 'div',
    slots = {},
    slotProps = {},
    label,
    helperText,
    id: idOverride,
    name,
    value: valueProp,
    defaultValue,
    onChange,
    disabled = false,
    error: errorProp = false,
    required = false,
    readOnly = false,
    format = 'MM/DD/YYYY',
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    clearable = true,
    size = 'medium',
    endDecorator,
    ...rest
  } = props;

  // Use our integrated hook
  const {
    rootRef,
    inputRef,
    inputValue,
    error: fieldError,
    handleInputClick,
    handleInputFocus,
    handleInputBlur,
    handleInputChange,
    handleKeyDown,
    handlePaste,
    handleClear,
    rootAriaAttributes,
  } = useFieldIntegration({
    value: valueProp,
    defaultValue,
    onChange,
    format,
    minDate,
    maxDate,
    shouldDisableDate,
    disableFuture,
    disablePast,
    disabled,
    readOnly,
    required,
  });

  const id = useId(idOverride);
  const classes = useUtilityClasses({ ...props, disabled, error: errorProp || !!fieldError });

  return (
    <TextField
      component={component}
      id={id}
      name={name}
      label={label}
      value={inputValue}
      placeholder={format}
      helperText={fieldError || helperText}
      disabled={disabled}
      error={errorProp || !!fieldError}
      required={required}
      readOnly={readOnly}
      endDecorator={
        <>
          {valueProp && !readOnly && !disabled && clearable && (
            <div onClick={handleClear} className="DateField-clearButton">
              <ClearIcon />
            </div>
          )}
          {endDecorator}
        </>
      }
      className={clsx(classes.root, className)}
      onClick={handleInputClick}
      onFocus={handleInputFocus}
      onBlur={handleInputBlur}
      onKeyDown={handleKeyDown}
      onChange={handleInputChange}
      onPaste={handlePaste}
      autoComplete="off"
      slotProps={{
        input: {
          ref: inputRef,
          className: classes.input,
        },
      }}
      sx={[...(Array.isArray(rest.sx) ? rest.sx : [rest.sx].filter(Boolean))]}
      {...rootAriaAttributes}
      {...rest}
      ref={rootRef}
    />
  );
};
