import { PickerDateType } from './pickers';

/**
 * The type that the `value` and `defaultValue` props can receive on non-range components (date, time and date-time).
 */
export type PickerValue = PickerDateType | null;

/**
 * The type that the `value` and `defaultValue` props can receive on range components (date-range, time-range and date-time-range).
 */
export type PickerRangeValue = [PickerDateType | null, PickerDateType | null];

export type PickerValidValue = PickerValue | PickerRangeValue;
