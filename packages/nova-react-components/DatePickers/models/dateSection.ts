export type FieldSectionType =
  | 'year'
  | 'month'
  | 'day'
  | 'weekDay'
  | 'hours'
  | 'minutes'
  | 'seconds'
  | 'meridiem'
  | 'empty';

export type FieldSectionContentType = 'digit' | 'digit-with-letter' | 'letter';

export interface FieldFormatTokenInfo {
  type: FieldSectionType;
  contentType: FieldSectionContentType;
  maxLength?: number;
}

export type FieldFormatTokenMap = {
  [formatToken: string]: FieldSectionType | FieldFormatTokenInfo;
};

export interface FieldSectionData {
  type: FieldSectionType;
  value: string;
  displayValue: string;
  placeholder: string;
  maxLength: number;
  startIndex: number;
  endIndex: number;
  separator: string;
  token: string;
  invalid: boolean;
  contentType?: FieldSectionContentType;
  modified?: boolean;
  format?: string;
}

/**
 * The type of value used in date/time fields
 */
export type FieldValueType = 'date' | 'time' | 'datetime';

/**
 * Represents a section value extracted from a field
 */
export interface FieldValueSection {
  type: FieldSectionType;
  value: string | null;
}

/**
 * Section configuration for display in UI
 */
export interface DisplayedSection {
  type: FieldSectionType;
  format?: string;
  placeholder?: string;
  maxLength?: number;
}
