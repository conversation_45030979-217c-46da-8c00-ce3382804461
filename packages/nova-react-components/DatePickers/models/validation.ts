import { PickerDateType } from './pickers';

/**
 * Makes specified keys in a type required.
 *
 * @template T - The original type.
 * @template K - The keys to make required.
 */

export type MakeRequired<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

/**
 * Validation error types applicable to both date and time validation
 */
type CommonDateTimeValidationError = 'invalidDate' | 'disableFuture' | 'disablePast' | null;

export type DateValidationError =
  | CommonDateTimeValidationError
  | 'shouldDisableDate'
  | 'shouldDisableMonth'
  | 'shouldDisableYear'
  | 'minDate'
  | 'maxDate';

export type TimeValidationError =
  | CommonDateTimeValidationError
  | 'minutesStep'
  | 'minTime'
  | 'maxTime'
  | 'shouldDisableTime-hours'
  | 'shouldDisableTime-minutes'
  | 'shouldDisableTime-seconds';

export type DateTimeValidationError = DateValidationError | TimeValidationError;

interface FutureAndPastValidationProps {
  /**
   * If `true`, disable values before the current date for date components, time for time components and both for date time components.
   * @default false
   */
  disablePast?: boolean;
  /**
   * If `true`, disable values after the current date for date components, time for time components and both for date time components.
   * @default false
   */
  disableFuture?: boolean;
}
/**
 * Validation props common to all the date views.
 * All these props have a default value when used inside a Field / Picker / Calendar.
 */
export interface BaseDateValidationProps extends FutureAndPastValidationProps {
  /**
   * Maximal selectable date.
   * @default 2099-12-31
   */
  maxDate?: PickerDateType;
  /**
   * Minimal selectable date.
   * @default 1900-01-01
   */
  minDate?: PickerDateType;
}

/**
 * Props used to validate a date value (validates day + month + year).
 */
export interface DayValidationProps {
  /**
   * Disable specific date.
   *
   * Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.
   *
   * @param {PickerValidDate} day The date to test.
   * @returns {boolean} If `true` the date will be disabled.
   */
  shouldDisableDate?: (day: PickerDateType) => boolean;
}

/**
 * Props used to validate a month value
 */
export interface MonthValidationProps {
  /**
   * Disable specific month.
   * @param {PickerValidDate} month The month to test.
   * @returns {boolean} If `true`, the month will be disabled.
   */
  shouldDisableMonth?: (month: PickerDateType) => boolean;
}

/**
 * Props used to validate a year value
 */
export interface YearValidationProps {
  /**
   * Disable specific year.
   * @param {PickerValidDate} year The year to test.
   * @returns {boolean} If `true`, the year will be disabled.
   */
  shouldDisableYear?: (year: PickerDateType) => boolean;
}

/**
 * Props used to validate a date time value.
 */
export interface DateTimeValidationProps {
  /**
   * Minimal selectable moment of time with binding to date, to set min time in each day use `minTime`.
   */
  minDateTime?: PickerDateType;
  /**
   * Maximal selectable moment of time with binding to date, to set max time in each day use `maxTime`.
   */
  maxDateTime?: PickerDateType;
}

/**
 * Validation props used by the Date Picker, Date Field and Date Calendar components.
 */
export interface ExportedValidateDateProps
  extends DayValidationProps,
    MonthValidationProps,
    YearValidationProps,
    BaseDateValidationProps {}
