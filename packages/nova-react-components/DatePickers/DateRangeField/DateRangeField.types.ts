import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import { DateFieldProps } from '../DateField/DateField.types';
import { PickerDateType } from '../models/pickers';
import { PickerRangeValue } from '../utils/dateRangeUtils';
import { SxProps } from '../../types/theme';

export interface DateRangeFieldComponentProps extends Omit<DateFieldProps, 'value' | 'defaultValue' | 'onChange'> {
  /**
   * The selected date range.
   */
  value?: PickerRangeValue;

  /**
   * The default selected date range.
   */
  defaultValue?: PickerRangeValue;

  /**
   * Callback fired when the value changes.
   * @param {PickerRangeValue} dateRange The new date range.
   */
  onChange?: (dateRange: PickerRangeValue) => void;

  /**
   * The character to use as the value separator in the input.
   * @default ' - '
   */
  separator?: string;

  /**
   * Label for the start date field.
   * @default 'Start date'
   */
  startLabel?: string;

  /**
   * Label for the end date field.
   * @default 'End date'
   */
  endLabel?: string;

  /**
   * Placeholder for the start date field.
   */
  startPlaceholder?: string;

  /**
   * Placeholder for the end date field.
   */
  endPlaceholder?: string;

  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
}

export interface DateRangeFieldSlots {
  /**
   * Custom component for the text field.
   * @default DateField
   */
  textField?: React.ElementType;
}

export interface DateRangeFieldSlotProps {
  textField?: SlotComponentProps<React.ElementType, object, DateRangeFieldOwnerState>;
}

export interface DateRangeFieldTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    DateRangeFieldComponentProps & {
      /**
       * The slots for customizing the component appearance.
       */
      slots?: DateRangeFieldSlots;

      /**
       * The props used for each slot.
       */
      slotProps?: DateRangeFieldSlotProps;
    };
  defaultComponent: D;
}

export type DateRangeFieldProps<D extends React.ElementType = DateRangeFieldTypeMap['defaultComponent']> =
  OverrideProps<DateRangeFieldTypeMap<object, D>, D> & {
    component?: D;
  };

export interface DateRangeFieldOwnerState extends DateRangeFieldProps {}
