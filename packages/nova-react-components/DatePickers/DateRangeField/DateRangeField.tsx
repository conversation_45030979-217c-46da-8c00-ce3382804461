'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { DateRangeFieldOwnerState, DateRangeFieldProps, DateRangeFieldTypeMap } from './DateRangeField.types';
import { getDateRangeFieldUtilityClass } from './DateRangeField.classes';
import { useUtils } from '../hooks/useUtils';
import { PickerRangeValue } from '../utils/dateRangeUtils';
import { PickerDateType } from '../models/pickers';
import { TextField } from '../../TextField';

const useUtilityClasses = (ownerState: DateRangeFieldOwnerState) => {
  const { disabled, error } = ownerState;

  const slots = {
    input: ['input'],
    disabled: disabled ? ['disabled'] : [],
    error: error ? ['error'] : [],
  };

  return composeClasses(slots, getDateRangeFieldUtilityClass, {});
};

const Root = styled('div')(({ theme }) => ({
  width: '100%',
}));

export const DateRangeField = React.forwardRef(function DateRangeField(
  props: DateRangeFieldProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const {
    className,
    value,
    defaultValue,
    onChange,
    format = 'MM/DD/YYYY',
    separator = ' - ',
    startLabel = 'Start date',
    endLabel = 'End date',
    startPlaceholder,
    endPlaceholder,
    label,
    size = 'medium',
    placeholder,
    disabled = false,
    error = false,
    readOnly = false,
    required = false,
    helperText,
    onClick,
    onKeyDown,
    onFocus,
    onBlur,
    fullWidth = true,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const utils = useUtils();
  const handleRef = useForkRef(ref, null);

  // Ensure displayValue is a valid array
  const ensureValidRange = (rangeValue: any): PickerRangeValue => {
    if (!rangeValue) return [null, null];
    if (!Array.isArray(rangeValue)) return [null, null];
    if (rangeValue.length !== 2) return [rangeValue[0] || null, null];
    return rangeValue as PickerRangeValue;
  };

  // Use null as fallback for each date in the range
  const displayValue = ensureValidRange(value || defaultValue);

  // State to track validation errors
  const [validationError, setValidationError] = React.useState<string | null>(null);

  // Helper function to safely parse a date (string or date object)
  const parseDateSafely = (date: any): PickerDateType | null => {
    if (!date) return null;

    try {
      // If date is a string, parse it to a date object
      if (typeof date === 'string') {
        const parsed = utils.date(date);
        if (!utils.isValid(parsed)) {
          return null;
        }
        return parsed;
      }

      // If already a date object, verify it's valid
      if (utils.isValid(date)) {
        return date;
      }

      return null;
    } catch (error) {
      console.error(`Error parsing date: ${date}`, error);
      return null;
    }
  };

  // Parse displayValue for validation
  const parsedStartDate = parseDateSafely(displayValue[0]);
  const parsedEndDate = parseDateSafely(displayValue[1]);

  // Validate range (start date should come before end date)
  React.useEffect(() => {
    if (parsedStartDate && parsedEndDate && utils.isAfter(parsedStartDate, parsedEndDate)) {
      setValidationError('Start date must be before end date');
    } else {
      setValidationError(null);
    }
  }, [parsedStartDate, parsedEndDate, utils]);

  const ownerState = { ...props, error: error || !!validationError };
  const classes = useUtilityClasses(ownerState);

  const SlotTextField = slots.textField ?? TextField;

  // Format the date range for display
  const getFormattedRange = (range: PickerRangeValue): string => {
    if (!range || (!range[0] && !range[1])) return '';

    // Helper function to safely format a date (string or date object)
    const formatDateSafely = (date: any): string => {
      if (!date) return '';

      try {
        // If date is a string, parse it to a date object first
        const parsedDate = typeof date === 'string' ? utils.date(date) : date;
        if (!utils.isValid(parsedDate)) {
          return '';
        }
        return utils.formatByString(parsedDate, format);
      } catch (error) {
        console.error(`Error formatting date: ${date}`, error);
        return '';
      }
    };

    const startStr = formatDateSafely(range[0]);
    const endStr = formatDateSafely(range[1]);

    if (!startStr && !endStr) return '';
    if (!startStr) return endStr;
    if (!endStr) return startStr;

    return `${startStr}${separator}${endStr}`;
  };

  const formattedValue = getFormattedRange(displayValue);

  // Handle click with information about which part was clicked
  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (disabled || readOnly) return;

    // Call the original onClick if provided
    if (onClick) {
      onClick(event);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (disabled || readOnly) return;

    // Call the original onKeyDown if provided
    if (onKeyDown) {
      onKeyDown(event);
    }
  };

  const textFieldProps = useSlotProps({
    elementType: SlotTextField,
    externalSlotProps: slotProps.textField,
    externalForwardedProps: other,
    additionalProps: {
      value: formattedValue,
      disabled,
      error: error || !!validationError,
      required,
      fullWidth,
      size,
      label: label || `${startLabel} ${separator} ${endLabel}`,
      placeholder: placeholder || `${startPlaceholder || startLabel}${separator}${endPlaceholder || endLabel}`,
      helperText: validationError || helperText, // Show validation error if present
      onClick: handleClick,
      onKeyDown: handleKeyDown,
      onFocus,
      onBlur,
      ref: handleRef,
      autoComplete: 'off',
    },
    ownerState,
    className: classes.input,
  });

  return <SlotTextField {...textFieldProps} />;
}) as OverridableComponent<DateRangeFieldTypeMap>;
