import {
  unstable_generateUtilityClass as generateUtilityClass,
  unstable_generateUtilityClasses as generateUtilityClasses,
} from '@mui/utils';

export interface DatePickerDayClasses {
  /** Styles applied to the root element */
  root: string;
  /** Styles applied to the root element when selected */
  selected: string;
  /** Styles applied to the root element when disabled */
  disabled: string;
  /** Styles applied to the root element when it's today */
  today: string;
  /** Styles applied to the root element when it's outside the current month */
  outsideCurrentMonth: string;
  /** Styles applied to the root element when it has no margin */
  noMargin: string;
}

export type DatePickerDayClassKey = keyof DatePickerDayClasses;

/**
 * Generates a utility class for the DatePickerDay component
 * @param {string} slot - The class slot name
 * @returns {string} The generated class name
 */
export function getDatePickerDayUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDatePickerDay', slot);
}

/**
 * Generated utility classes for the DatePickerDay component
 */
export const datePickerDayClasses: DatePickerDayClasses = generateUtilityClasses('NovaDatePickerDay', [
  'root',
  'selected',
  'disabled',
  'today',
  'outsideCurrentMonth',
  'noMargin',
]);
