'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import clsx from 'clsx';
import { useUtils } from '../hooks/useUtils';
import { DatePickerDayProps, DatePickerDayOwnerState } from './DatePickerDay.types';
import { datePickerDayClasses, getDatePickerDayUtilityClass } from './datePickerDayClasses';

const useUtilityClasses = (ownerState: DatePickerDayOwnerState) => {
  const {
    isSelected,
    isDisabled,
    isToday,
    isOutsideCurrentMonth,
    disableMargin,
    disableHighlightToday,
    showDaysOutsideCurrentMonth,
  } = ownerState;

  // Hide days outside current month if not shown
  const isHidden = isOutsideCurrentMonth && !showDaysOutsideCurrentMonth;

  const slots = {
    root: [
      'root',
      isSelected && !isHidden && 'selected',
      isDisabled && 'disabled',
      !disableHighlightToday && isToday && 'today',
      isOutsideCurrentMonth && showDaysOutsideCurrentMonth && 'outsideCurrentMonth',
      !disableMargin && 'noMargin',
    ],
  };

  // Use custom class name generator if provided
  return composeClasses(slots, getDatePickerDayUtilityClass, {});
};

// Create a basic styled button that applies the core styles
const DatePickerDayRoot = styled('button')(({ theme }) => ({
  width: 'auto',
  height: 'auto',
  aspectRatio: '1/1',
  display: 'flex',
  flex: 1,
  justifyContent: 'center',
  alignItems: 'center',
  border: 'none',
  borderRadius: '50%',
  cursor: 'pointer',
  backgroundColor: 'transparent',
  position: 'relative',
  padding: 0,
  margin: 0,
  color: theme.vars.palette.onSurface,
  ...theme.typography.bodySmall,

  // Hover state
  '&:hover:not(:disabled)': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },

  // Active state
  '&:active:not(:disabled)': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },

  // Focus state
  '&:focus:not(:disabled)': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },

  // Selected state
  [`&.${datePickerDayClasses.selected}`]: {
    backgroundColor: theme.vars.palette.primary,
    color: theme.vars.palette.onPrimary,
    '&:hover:not(:disabled)': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnPrimary})`,
    },
    '&:active:not(:disabled)': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnPrimary})`,
    },
    '&:focus:not(:disabled)': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnPrimary})`,
    },
  },

  // Today state
  [`&.${datePickerDayClasses.today}`]: {
    border: `1px solid ${theme.vars.palette.primary}`,
    '&:hover': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverOnPrimary})`,
    },
    '&:active': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressOnSurface})`,
    },
    '&:focus': {
      border: 'none',
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
    },
  },

  // Outside month state
  [`&.${datePickerDayClasses.outsideCurrentMonth}`]: {
    color: theme.vars.palette.onSurfaceVariant,
    opacity: 0.6,
  },

  // Disabled state
  '&:disabled': {
    color: theme.vars.palette.onSurfaceVariant,
    cursor: 'default',
    opacity: 0.38,
  },

  // Focus visible
  '&:focus-visible': {
    outlineOffset: '2px',
    outline: `1px solid ${theme.vars.palette.secondary}`,
  },

  // No margin
  [`&.${datePickerDayClasses.noMargin}`]: {
    margin: 0,
  },
}));

/**
 * Specialized component for rendering days in date pickers.
 * A date picker day button component that displays a single day.
 */
export const DatePickerDay = React.forwardRef<HTMLButtonElement, DatePickerDayProps>(
  function DatePickerDay(props, ref) {
    const {
      className,
      day,
      disabled = false,
      disableHighlightToday = false,
      disableMargin = false,
      isFirstVisibleCell,
      isLastVisibleCell,
      onDaySelect,
      outsideCurrentMonth = false,
      selected = false,
      showDaysOutsideCurrentMonth = false,
      children,
      ...other
    } = props;

    const utils = useUtils();

    // Determine if the day is today
    const now = utils.date();
    const isToday = utils.isSameDay(day, now);

    // Build the owner state for styling
    const ownerState: DatePickerDayOwnerState = {
      day,
      isSelected: selected,
      isDisabled: disabled,
      isToday,
      isOutsideCurrentMonth: outsideCurrentMonth,
      disableMargin,
      disableHighlightToday,
      showDaysOutsideCurrentMonth,
      isFirstVisibleCell: Boolean(isFirstVisibleCell),
      isLastVisibleCell: Boolean(isLastVisibleCell),
    };

    const classes = useUtilityClasses(ownerState);

    // Don't render button if it's hidden
    const isHidden = ownerState.isOutsideCurrentMonth && !ownerState.showDaysOutsideCurrentMonth;
    if (isHidden) {
      return null;
    }

    // Handler for day selection
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      event.preventDefault();
      if (onDaySelect && !disabled) {
        onDaySelect(day);
      }
    };

    return (
      <DatePickerDayRoot
        ref={ref}
        className={clsx(classes.root, className)}
        disabled={disabled}
        type="button"
        tabIndex={selected ? 0 : -1}
        onClick={handleClick}
        aria-selected={selected}
        {...other}
      >
        {children || (day && utils.format(day, 'dayOfMonth'))}
      </DatePickerDayRoot>
    );
  },
);
