'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { Typography } from '../../Typography';
import { IconButton } from '../../IconButton';
import { EditIcon, CalendarIcon } from '../icons';
import { PickerViewHeaderOwnerState, PickerViewHeaderProps, PickerViewHeaderTypeMap } from './PickerViewHeader.types';
import { getPickerViewHeaderUtilityClass } from './PickerViewHeader.classes';
import { useUtils } from '../hooks/useUtils';
import { PickerDateType } from '../models/pickers';

const useUtilityClasses = (ownerState: PickerViewHeaderOwnerState) => {
  const { disabled } = ownerState;

  const slots = {
    root: ['root'],
    disabled: disabled ? ['disabled'] : [],
  };

  return composeClasses(slots, getPickerViewHeaderUtilityClass, {});
};

const Root = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  gap: '16px',
  paddingInline: '24px',
  paddingBlockStart: '16px',
  paddingBlockEnd: '8px',
  width: '100%',
}));

const DateContainer = styled('div')({
  display: 'flex',
  width: '100%',
  justifyContent: 'space-between',
  alignItems: 'center',
});

const Label = styled(Typography)(({ theme }) => ({
  color: theme.vars.palette.onSurfaceVariant,
}));

const SelectedDate = styled(Typography)(({ theme }) => ({
  color: theme.vars.palette.onSurface,
  ...theme.typography.titleLarge,
  fontWeight: 400,
}));

export const PickerViewHeader = React.forwardRef(function PickerViewHeader(
  props: PickerViewHeaderProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const {
    view,
    views,
    onViewChange,
    disabled = false,
    readOnly = false,
    days,
    label = 'Select date',
    isEditing = false,
    onSwitchDateField,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const utils = useUtils();

  const handleRef = useForkRef(ref, null);

  const ownerState = {
    ...props,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? Root;

  const rootProps = useSlotProps({
    elementType: Root,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  const handleEdit = () => {
    if (onSwitchDateField) {
      onSwitchDateField();
    }
  };

  // Process date or date range using reduce
  const formattedDate = (() => {
    if (!Array.isArray(days)) return '';
    const validDates = days.filter((date): date is PickerDateType => date !== null);
    if (validDates.length === 0) return 'Enter date';
    if (validDates.length === 1) return utils.format(validDates[0], 'normalDateWithWeekday') || 'Enter date';
    return validDates.map((date) => utils.format(date, 'shortDate')).join(' – ') || 'Enter dates';
  })();

  return (
    <SlotRoot {...rootProps}>
      <Label variant="labelMedium">{label}</Label>
      <DateContainer>
        <SelectedDate variant="titleLarge">{formattedDate}</SelectedDate>
        <IconButton
          onClick={handleEdit}
          disabled={disabled || readOnly}
          size="medium"
          variant="standard"
          aria-label="Edit date"
        >
          {!isEditing ? <EditIcon /> : <CalendarIcon />}
        </IconButton>
      </DateContainer>
    </SlotRoot>
  );
}) as OverridableComponent<PickerViewHeaderTypeMap>;
