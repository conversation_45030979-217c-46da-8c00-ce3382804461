import React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { SxProps } from '../../types/theme';
import { PickerDateType } from '../models/pickers';

export type DatePickerView = 'day' | 'month' | 'year';

export type PickerViewHeaderSlot = 'root';

export interface PickerViewHeaderSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type PickerViewHeaderSlotsAndSlotProps = CreateSlotsAndSlotProps<
  PickerViewHeaderSlots,
  {
    root: SlotProps<'div', object, PickerViewHeaderOwnerState>;
  }
>;

export interface PickerViewHeaderTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    PickerViewHeaderSlotsAndSlotProps & {
      /**
       * The current view of the picker.
       */
      view: DatePickerView;
      /**
       * Array of views available for the picker.
       */
      views: readonly DatePickerView[];
      /**
       * Callback fired when changing the view.
       */
      onViewChange: (view: DatePickerView) => void;
      /**
       * If `true`, the component is disabled.
       * @default false
       */
      disabled?: boolean;
      /**
       * If `true`, the component is in readonly mode.
       * @default false
       */
      readOnly?: boolean;
      /**
       * The selected date to display.
       */
      days?: PickerDateType[];
      /**
       * The label to display.
       * @default 'Enter date'
       */
      label?: string | React.ReactNode;
      /**
       * If `true`, the component is in editing mode.
       * @default false
       */
      isEditing?: boolean;
      /**
       * Callback fired when edit button is clicked.
       */
      onSwitchDateField: () => void;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type PickerViewHeaderProps<
  D extends React.ElementType = PickerViewHeaderTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<PickerViewHeaderTypeMap<P, D>, D>;

export interface PickerViewHeaderOwnerState extends PickerViewHeaderProps {}
