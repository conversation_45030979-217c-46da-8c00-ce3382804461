import { PickerDateType } from '../models/pickers';
import { DatePickerVariant } from '../types';

export interface ExportedRangeCalendarHeaderProps extends Omit<React.HTMLProps<HTMLDivElement>, 'ref' | 'as'> {
  /**
   * The number of calendars rendered.
   */
  calendars: 1 | 2 | 3;
  /**
   * Month used for this header.
   */
  month: PickerDateType;
  /**
   * Index of the month used for this header.
   */
  monthIndex: number;
}

export interface RangeCalendarHeaderProps extends ExportedRangeCalendarHeaderProps {
  onMonthChange: (date: PickerDateType) => void;
  maxDate: PickerDateType;
  minDate: PickerDateType;
  disabled: boolean;
  readOnly: boolean;
  disablePast?: boolean;
  disableFuture?: boolean;
  variant: DatePickerVariant;
}
