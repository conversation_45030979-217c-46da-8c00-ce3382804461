import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface YearCalendarClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the component when disabled. */
  disabled: string;
}

export type YearCalendarClassKey = keyof YearCalendarClasses;

export function getYearCalendarUtilityClass(slot: string): string {
  return generateUtilityClass('NovaYearCalendar', slot);
}

const yearCalendarClasses: YearCalendarClasses = generateUtilityClasses('NovaYearCalendar', ['root', 'disabled']);

export default yearCalendarClasses;
