import React, { useCallback, useEffect } from 'react';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { debounce } from '../../utils/debunce';
import dockedDatePickerClasses from '../../DockedDatePicker/DockedDatePicker.classes';
import { PickerDateType } from '../../models/pickers';
import { useNow, useUtils } from '../../hooks/useUtils';
import { YearCalendarProps } from '../YearCalendar.types';

const ListContainer = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  width: '100%',
  borderRadius: '4px',
  overflow: 'auto',
  maxHeight: '300px',
}));

const YearItem = styled('button')(({ theme }) => ({
  padding: '12px 16px',
  cursor: 'pointer',
  ...theme.typography.bodyLarge,
  color: theme.vars.palette.onSurface,
  border: 'none',
  backgroundColor: 'transparent',
  width: '100%',
  textAlign: 'left',
  '&:hover:not(:disabled)': {
    color: theme.vars.palette.onSurface,
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },

  '&.selected': {
    backgroundColor: theme.vars.palette.primary,
    color: theme.vars.palette.onPrimary,
  },

  '&:disabled': {
    opacity: 0.5,
    cursor: 'not-allowed',
  },

  '&:focus-visible': {
    outline: `2px solid ${theme.vars.palette.primary}`,
    outlineOffset: '-2px',
  },
}));

export const YearListView = React.memo<YearCalendarProps>(
  ({
    date,
    viewDate,
    onChange,
    onYearRangeChange,
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    disabled = false,
    readOnly = false,
    shouldDisableYear,
  }) => {
    const now = useNow();
    const utils = useUtils();

    const [focusedYear, setFocusedYear] = React.useState<number | null>(date ? date.year() : now.year());

    const [years, setYears] = React.useState(() => {
      // Use the min/max date from context (they have defaults: 1900-01-01 and 2099-12-31)
      const startYear = utils.getYear(minDate);
      const endYear = utils.getYear(maxDate);

      return Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i);
    });

    const isYearDisabled = React.useCallback(
      (dateToValidate: PickerDateType) => {
        if (disablePast && utils.isBeforeYear(dateToValidate, now)) {
          return true;
        }
        if (disableFuture && utils.isAfterYear(dateToValidate, now)) {
          return true;
        }
        if (minDate && utils.isBeforeYear(dateToValidate, minDate)) {
          return true;
        }
        if (maxDate && utils.isAfterYear(dateToValidate, maxDate)) {
          return true;
        }

        if (!shouldDisableYear) {
          return false;
        }

        const yearToValidate = utils.startOfYear(dateToValidate);
        return shouldDisableYear(yearToValidate);
      },
      [disableFuture, disablePast, maxDate, minDate, now, shouldDisableYear, utils],
    );

    const handleScroll = useCallback(
      debounce((e: React.UIEvent<HTMLDivElement>) => {
        const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
        const threshold = 80;

        if (scrollTop < threshold) {
          const newStartYear = years[0] - 5;
          setYears((prev) => Array.from({ length: 5 }, (_, i) => newStartYear + i).concat(prev));
          onYearRangeChange(viewDate.year(newStartYear));
        }

        if (scrollHeight - scrollTop - clientHeight < threshold) {
          const newEndYear = years[years.length - 1] + 5;
          setYears((prev) => prev.concat(Array.from({ length: 5 }, (_, i) => newEndYear + i)));
          onYearRangeChange(viewDate.year(newEndYear));
        }
      }, 150),
      [years, viewDate, onYearRangeChange],
    );

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent<HTMLButtonElement>, year: number) => {
        e.preventDefault();
        const yearIndex = years.indexOf(year);

        switch (e.key) {
          case 'Enter':
          case ' ':
            if (!disabled && !readOnly) {
              onChange(year);
            }
            break;
          case 'ArrowDown':
            if (yearIndex + 1 < years.length) {
              setFocusedYear(years[yearIndex + 1]);
            }
            break;
          case 'ArrowUp':
            if (yearIndex - 1 >= 0) {
              setFocusedYear(years[yearIndex - 1]);
            }
            break;
          case 'ArrowRight':
            if (yearIndex + 1 < years.length) {
              setFocusedYear(years[yearIndex + 1]);
            }
            break;
          case 'ArrowLeft':
            if (yearIndex - 1 >= 0) {
              setFocusedYear(years[yearIndex - 1]);
            }
            break;
          case 'Home':
            setFocusedYear(years[0]);
            break;
          case 'End':
            setFocusedYear(years[years.length - 1]);
            break;
          case 'PageUp':
            if (yearIndex - 5 >= 0) {
              setFocusedYear(years[yearIndex - 5]);
            } else {
              setFocusedYear(years[0]);
            }
            break;
          case 'PageDown':
            if (yearIndex + 5 < years.length) {
              setFocusedYear(years[yearIndex + 5]);
            } else {
              setFocusedYear(years[years.length - 1]);
            }
            break;
        }
      },
      [years, disabled, readOnly, onChange],
    );

    // Focus the year when it changes
    useEffect(() => {
      if (focusedYear !== null) {
        const element = document.querySelector(`[data-year="${focusedYear}"]`) as HTMLButtonElement;
        element?.focus();
      }
    }, [focusedYear]);

    return (
      <ListContainer
        className={dockedDatePickerClasses.yearView}
        onScroll={handleScroll}
        role="listbox"
        aria-label="Year selection"
      >
        {years.map((year) => {
          const yearDate = viewDate.year(year);
          const isSelected = date && utils.getYear(date) === year;
          const isDisabled = isYearDisabled(yearDate);
          return (
            <YearItem
              key={year}
              data-year={year}
              className={clsx(
                isSelected && ['selected', dockedDatePickerClasses.selectedItem],
                isDisabled && 'disabled',
              )}
              onClick={() => {
                if (!isDisabled && !disabled && !readOnly) {
                  onChange(year);
                }
              }}
              onKeyDown={(e) => handleKeyDown(e, year)}
              onFocus={() => setFocusedYear(year)}
              role="option"
              aria-selected={isSelected}
              disabled={isDisabled || disabled || readOnly}
              tabIndex={isSelected || year === focusedYear ? 0 : -1}
              type="button"
            >
              {year}
            </YearItem>
          );
        })}
      </ListContainer>
    );
  },
);
