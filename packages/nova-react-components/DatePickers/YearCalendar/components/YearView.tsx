import * as React from 'react';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { YearCalendarProps } from '../YearCalendar.types';
import { modalDatePickerClasses } from '../../ModalDatePicker/ModalDatePicker.classes';
import { PickerDateType } from '../../models/pickers';
import { useNow, useUtils } from '../../hooks/useUtils';

const YearGrid = styled('div')(({ theme }) => ({
  display: 'grid',
  justifyItems: 'center',
  gridTemplateColumns: 'repeat(3, 1fr)',
  gap: '8px',
  maxHeight: '340px',
  overflowY: 'auto',
}));

const YearButton = styled('button')(({ theme }) => ({
  padding: '8px',
  border: 'none',
  width: '72px',
  borderRadius: '20px',
  cursor: 'pointer',
  backgroundColor: 'transparent',
  color: theme.vars.palette.onSurface,
  ...theme.typography.bodySmall,
  transition: 'background-color 0.2s ease, color 0.2s ease',
  textAlign: 'center',

  '&:hover': {
    color: theme.vars.palette.onSurface,
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },

  '&.selected': {
    backgroundColor: theme.vars.palette.primary,
    color: theme.vars.palette.onPrimary,
    fontWeight: 500,
  },

  '&:disabled': {
    color: theme.vars.palette.onBackgroundDisabled,
    cursor: 'default',
  },

  '&:focus-visible': {
    outline: `1px solid ${theme.vars.palette.primary}`,
    outlineOffset: '2px',
  },
}));

export const YearView: React.FC<YearCalendarProps> = ({
  date,
  viewDate,
  onChange,
  onYearRangeChange,
  disablePast,
  disableFuture,
  minDate,
  maxDate,
  disabled = false,
  readOnly = false,
}) => {
  const now = useNow();
  const utils = useUtils();

  // Calculate year range based on minDate and maxDate with defaults
  const startYear = utils.getYear(minDate);
  const endYear = utils.getYear(maxDate);
  // Display a reasonable range of years around the current year or selection
  const selectedYear = date ? utils.getYear(date) : now.year();

  const yearsPerRow = 3;
  const totalRows = Math.ceil((endYear - startYear + 1) / yearsPerRow);

  const isYearDisabled = React.useCallback(
    (dateToValidate: PickerDateType) => {
      if (disablePast && utils.isBeforeYear(dateToValidate, now)) {
        return true;
      }
      if (disableFuture && utils.isAfterYear(dateToValidate, now)) {
        return true;
      }
      if (minDate && utils.isBeforeYear(dateToValidate, minDate)) {
        return true;
      }
      if (maxDate && utils.isAfterYear(dateToValidate, maxDate)) {
        return true;
      }

      if (!disabled) {
        return false;
      }
    },
    [disableFuture, disablePast, disabled, maxDate, minDate, now, utils],
  );

  // Handle keyboard navigation
  const handleKeyDown = React.useCallback(
    (event: React.KeyboardEvent) => {
      const { key } = event;

      // Prevent default for arrow keys to avoid scrolling the page
      if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(key)) {
        event.preventDefault();
      }

      let newYear = selectedYear;
      let yearRangeChanged = false;

      switch (key) {
        case 'ArrowLeft':
          // Move one year to the left
          newYear = selectedYear - 1;
          break;
        case 'ArrowRight':
          // Move one year to the right
          newYear = selectedYear + 1;
          break;
        case 'ArrowUp':
          // Move up one row (3 years)
          newYear = selectedYear - yearsPerRow;
          break;
        case 'ArrowDown':
          // Move down one row (3 years)
          newYear = selectedYear + yearsPerRow;
          break;
        case 'Home':
          // Move to the first year in the current view
          newYear = startYear;
          break;
        case 'End':
          // Move to the last year in the current view
          newYear = endYear;
          break;
        case 'PageUp': {
          // Move one page up (showing previous years)
          const prevYearRange = utils.addYears(viewDate, -totalRows * yearsPerRow);
          // Check if entire range would be before minDate
          const isRangeDisabled = minDate && utils.getYear(prevYearRange) < utils.getYear(minDate);

          if (!isRangeDisabled && onYearRangeChange) {
            yearRangeChanged = true;
            onYearRangeChange(prevYearRange);
          }
          break;
        }
        case 'PageDown': {
          // Move one page down (showing next years)
          const nextYearRange = utils.addYears(viewDate, totalRows * yearsPerRow);
          // Check if entire range would be after maxDate
          const isRangeDisabled = maxDate && utils.getYear(nextYearRange) > utils.getYear(maxDate);

          if (!isRangeDisabled && onYearRangeChange) {
            yearRangeChanged = true;
            onYearRangeChange(nextYearRange);
          }
          break;
        }
        default:
          return;
      }

      // If the year changed and it's not a year range change
      if (newYear !== selectedYear && !yearRangeChanged) {
        // Check if year is disabled
        const newYearDate = utils.setYear(viewDate, newYear);
        const isDisabled = isYearDisabled(newYearDate);
        if (!isDisabled && !disabled && !readOnly) {
          onChange(newYear);

          // If year is outside visible range, update the range
          if ((newYear < startYear || newYear > endYear) && onYearRangeChange) {
            onYearRangeChange(newYearDate);
          }
        }
      }
    },
    [
      selectedYear,
      startYear,
      endYear,
      utils,
      viewDate,
      totalRows,
      minDate,
      onYearRangeChange,
      maxDate,
      isYearDisabled,
      disabled,
      readOnly,
      onChange,
    ],
  );

  // Calculate a range of years to display
  const renderYears = () => {
    const years = [];
    for (let year = startYear; year <= endYear; year++) {
      const yearDate = utils.setYear(viewDate, year);
      const isSelected = date && utils.getYear(date) === year;
      const isDisabled = isYearDisabled(yearDate);

      years.push(
        <YearButton
          key={year}
          onClick={() => !isDisabled && onChange(year)}
          onKeyDown={handleKeyDown}
          className={clsx(
            modalDatePickerClasses.yearButton,
            isSelected && ['selected', modalDatePickerClasses.selected],
            isDisabled && modalDatePickerClasses.disabled,
          )}
          disabled={isDisabled || disabled || readOnly}
          type="button"
          aria-label={`Year ${year}`}
          aria-selected={isSelected}
          tabIndex={isSelected ? 0 : -1}
        >
          {year}
        </YearButton>,
      );
    }

    return years;
  };

  return (
    <YearGrid className={modalDatePickerClasses.yearView} role="grid">
      {renderYears()}
    </YearGrid>
  );
};
