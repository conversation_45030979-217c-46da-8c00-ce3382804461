'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { Button } from '../../Button';
import { PickerViewFooterOwnerState, PickerViewFooterProps, PickerViewFooterTypeMap } from './PickerViewFooter.types';
import { getPickerViewFooterUtilityClass } from './PickerViewFooter.classes';

const useUtilityClasses = (ownerState: PickerViewFooterOwnerState) => {
  const { disabled } = ownerState;

  const slots = {
    root: ['root'],
    disabled: disabled ? ['disabled'] : [],
  };

  return composeClasses(slots, getPickerViewFooterUtilityClass, {});
};

const FooterRoot = styled('div')<PickerViewFooterProps>(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  padding: '0px 12px 12px 12px',
  background: ' inherit',
  variants: [
    {
      props: { fullScreen: true },
      style: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        width: '100%',
        borderTop: `1px solid ${theme.vars.palette.outlineVariant}`,
      },
    },
  ],
}));

const LeftActions = styled('div')({
  flex: 1,
  display: 'flex',
  justifyContent: 'flex-start',
});

const RightActions = styled('div')({
  flex: 1,
  display: 'flex',
  justifyContent: 'flex-end',
  gap: '8px',
});

export const PickerViewFooter = React.forwardRef(function PickerViewFooter(
  props: PickerViewFooterProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const {
    onClear,
    onCancel,
    onAccept,
    disabled = false,
    clearText = 'Clear',
    cancelText = 'Cancel',
    okText = 'OK',
    component,
    fullScreen = false,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const handleRef = useForkRef(ref, null);

  const ownerState = {
    ...props,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? FooterRoot;

  const rootProps = useSlotProps({
    elementType: FooterRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  return (
    <SlotRoot {...rootProps}>
      <LeftActions>
        {onClear && (
          <Button variant="text" color="primary" onClick={onClear} disabled={disabled}>
            {clearText}
          </Button>
        )}
      </LeftActions>

      <RightActions>
        {onCancel && (
          <Button variant="text" color="primary" onClick={onCancel} disabled={disabled}>
            {cancelText}
          </Button>
        )}

        <Button variant="text" color="primary" onClick={onAccept} disabled={disabled}>
          {okText}
        </Button>
      </RightActions>
    </SlotRoot>
  );
}) as OverridableComponent<PickerViewFooterTypeMap>;
