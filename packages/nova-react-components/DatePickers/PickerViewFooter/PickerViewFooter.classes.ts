import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface PickerViewFooterClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the component when disabled. */
  disabled: string;
}

export type PickerViewFooterClassKey = keyof PickerViewFooterClasses;

export function getPickerViewFooterUtilityClass(slot: string): string {
  return generateUtilityClass('NovaPickerViewFooter', slot);
}

const pickerViewFooterClasses: PickerViewFooterClasses = generateUtilityClasses('NovaPickerViewFooter', [
  'root',
  'disabled',
]);

export default pickerViewFooterClasses;
