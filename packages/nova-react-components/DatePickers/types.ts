import { ExportedDateCalendarProps } from './DateCalendar/DateCalendar.types';
import { PickerDateType } from './models/pickers';

/**
 * Available view types for Date Picker components
 */
export type DatePickerView = 'day' | 'month' | 'year';

export type TimeView = 'hours' | 'minutes' | 'seconds';

export type TimeViewWithMeridiem = TimeView | 'meridiem';

export type DateOrTimeViewWithMeridiem = DatePickerView | TimeViewWithMeridiem;

/**
 * Animation slide direction for date picker transitions
 */
export type SlideDirection = 'right' | 'left';

/**
 * Appearance variant for date picker components
 */
export type DatePickerVariant = 'modal' | 'docked';

/**
 * View style configuration for date picker components
 */
export type ViewStyle = 'grid' | 'list';

/**
 * Base interface for all picker props
 */
export interface BasePickerProps extends ExportedDateCalendarProps {
  /**
   * The selected date.
   */
  value?: PickerDateType | null;

  /**
   * The default value. Use when the component is not controlled.
   */
  defaultValue?: PickerDateType | null;

  /**
   * Callback fired when the value changes.
   * @param {PickerDateType} date - The new date value.
   */
  onChange?: (date: PickerDateType) => void;

  /**
   * First day of the week. 0 = Sunday, 1 = Monday, etc.
   * @default 0
   */
  firstDayOfWeek?: number;

  /**
   * Callback when the picker is opened.
   */
  onOpen?: () => void;

  /**
   * Callback when the picker is closed.
   */
  onClose?: () => void;

  /**
   * If true, the picker will be displayed in an error state.
   * @default false
   */
  error?: boolean;

  /**
   * Format string for displaying the date (uses dayjs format strings).
   * @default 'DD/MM/YYYY'
   */
  format?: string;

  /**
   * If `true`, the picker closes after a date is selected.
   */
  closeOnSelect?: boolean;

  /**
   * Callback when view changes.
   * @param {DatePickerView} view - The new active view
   */
  onViewChange?: (view: DatePickerView) => void;

  /**
   * Available views for the date picker.
   * @default ['day', 'month', 'year']
   */
  views?: DatePickerView[];
}

/**
 * Interface for date validation results
 */
export interface DateValidationResult {
  /** Whether the date is valid according to all constraints */
  isValid: boolean;

  /** The parsed date if valid, or null */
  date: PickerDateType | null;

  /** Error message if validation failed */
  errorMessage: string | null;
}
