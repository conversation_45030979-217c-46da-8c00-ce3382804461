import { ReactNode } from 'react';
import { TextFieldProps } from '../../TextField';
import { BasePickerProps } from '../types';

/**
 * Props for the ModalDatePicker component
 */
export interface ModalDatePickerProps
  extends Omit<TextFieldProps, 'value' | 'defaultValue' | 'onChange' | 'autoComplete'>,
    BasePickerProps {
  /**
   * Placement of the popup.
   * @default 'bottom-start'
   */
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';

  /**
   * Text for clear button.
   * @default 'Clear'
   */
  clearText?: string;

  /**
   * Text for cancel button.
   * @default 'Cancel'
   */
  cancelText?: string;

  /**
   * Text for apply button.
   * @default 'OK'
   */
  okText?: string;

  /**
   * If true, focuses the calendar on open.
   * @default false
   */
  autoFocus?: boolean;
}
