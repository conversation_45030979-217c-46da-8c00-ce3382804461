'use client';
import React, { useCallback, useState } from 'react';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { Dialog } from '../../Dialog';
import { CalendarIcon } from '../icons';
import { ModalDatePickerProps } from './ModalDatePicker.types';
import { Divider } from '../../Divider';
import { DateCalendar } from '../DateCalendar';
import { PickerViewHeader } from '../PickerViewHeader';
import { PickerViewFooter } from '../PickerViewFooter';
import { useDefaultDates } from '../hooks/useUtils';
import { DateField } from '../DateField';
import { IconButton } from '../../IconButton';
import { modalDatePickerClasses } from './ModalDatePicker.classes';
import { unstable_createUseMediaQuery as createUseMediaQuery } from '@mui/system/useMediaQuery';
import { Box } from '../../Box';
import { useDatePicker } from '../hooks/useDatePicker';

const useMediaQuery = createUseMediaQuery();

const Root = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
}));

export const ModalDatePicker = React.forwardRef<HTMLDivElement, ModalDatePickerProps>((props, ref) => {
  // Spread props with defaults
  const defaultDate = useDefaultDates();
  const {
    value,
    defaultValue,
    onChange,
    label = 'Select date',
    placeholder,
    disabled = false,
    error = false,
    helperText,
    format = 'MM/DD/YYYY',
    fullWidth = false,
    required = false,
    readOnly = false,
    size = 'medium',
    minDate = defaultDate.minDate,
    maxDate = defaultDate.maxDate,
    disableFuture = false,
    disablePast = false,
    shouldDisableDate,
    views = ['day', 'year'],
    firstDayOfWeek = 0, // 0 = Sunday, 1 = Monday, etc.
    onViewChange,
    className,
    autoFocus = false,
    closeOnSelect = false,
    endDecorator = (
      <div className="DateField-calendarIcon">
        <CalendarIcon />
      </div>
    ),
    ...textFieldProps
  } = props;

  const [isEditing, setIsEditing] = useState(false);

  // Use the date picker hook
  const {
    dateState,
    open,
    handleOpen,
    currentView,
    availableViews,
    handleViewDateChange,
    handleDateChange,
    handleInputChange,
    handleViewChange,
    handleAccept,
    handleCancel,
  } = useDatePicker({
    value,
    defaultValue,
    onChange,
    views,
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    format,
    disabled,
    readOnly,
    autoFocus,
    closeOnSelect,
  });

  const handlePickerIconClicked = useCallback(() => {
    handleOpen();
  }, [handleOpen]);

  const handleSwitchDateField = () => {
    setIsEditing((editing) => !editing);
  };

  const isSmall = useMediaQuery('(max-width:768px)', { noSsr: true });

  const openPickerButton = (
    <IconButton
      className="DateField-calendarIcon"
      variant="standard"
      onClick={handlePickerIconClicked}
      disabled={disabled || readOnly}
    >
      {endDecorator || <CalendarIcon />}
    </IconButton>
  );

  const baseFieldProps = {
    value: dateState.tempDate,
    onChange: handleInputChange,
    label,
    placeholder,
    format,
    disabled,
    fullWidth,
    required,
    readOnly,
    size,
    helperText,
    disableFuture,
    disablePast,
    maxDate,
    minDate,
    clearable: false,
  };

  const fieldProps = {
    onClick: handleOpen,
    className: modalDatePickerClasses.inputField,
    endDecorator: openPickerButton,
    ...baseFieldProps,
  };

  return (
    <Root
      ref={ref}
      className={clsx(
        modalDatePickerClasses.root,
        className,
        disabled && modalDatePickerClasses.disabled,
        error && modalDatePickerClasses.error,
      )}
    >
      <DateField {...textFieldProps} {...fieldProps} />

      <Dialog.Root open={open} onClose={handleCancel} fullScreen={isSmall} aria-labelledby="date-picker-dialog-title">
        <PickerViewHeader
          days={[dateState.tempDate]}
          className={modalDatePickerClasses.calendarHeader}
          label={label}
          view={currentView}
          views={availableViews}
          onViewChange={handleViewChange}
          disabled={disabled}
          readOnly={readOnly}
          isEditing={isEditing}
          onSwitchDateField={handleSwitchDateField}
        />
        <Divider />

        {isEditing && (
          <Box sx={{ display: 'flex', gap: '8px', paddingInline: '24px', paddingBlock: '16px' }}>
            <DateField {...baseFieldProps} label="Date" sx={{ width: '260px' }} />
          </Box>
        )}
        {!isEditing && (
          <DateCalendar
            className={modalDatePickerClasses.calendarContainer}
            view={currentView}
            date={dateState.tempDate}
            viewDate={dateState.selectedDate}
            onViewDateChange={handleViewDateChange}
            onDateChange={handleDateChange}
            onViewChange={handleViewChange}
            disableFuture={disableFuture}
            disablePast={disablePast}
            shouldDisableDate={shouldDisableDate}
            minDate={minDate}
            maxDate={maxDate}
            disabled={disabled}
            readOnly={readOnly}
            variant={'modal'}
            views={views}
          />
        )}
        {currentView === 'year' && <Divider />}

        {(currentView === 'day' || currentView === 'year') && (
          <PickerViewFooter
            className={modalDatePickerClasses.footer}
            onAccept={handleAccept}
            onCancel={handleCancel}
            fullScreen={isSmall && !isEditing}
            disabled={disabled || readOnly}
          />
        )}
      </Dialog.Root>
    </Root>
  );
});
