import { PickerDateType } from '../models/pickers';
import { AdapterFormats, DateAdapter } from '../models/adapter';

/**
 * A range value with start and end dates
 */
export type PickerRangeValue = [PickerDateType | null, PickerDateType | null];

/**
 * Non-nullable range value type for type assertion
 */
export type PickerNonNullableRangeValue = [PickerDateType, PickerDateType];

/**
 * The position in the range being selected (start or end)
 */
export type RangePosition = 'start' | 'end';

/**
 * Checks if a range is valid (both dates are set and properly ordered)
 */
export function isRangeValid(utils: DateAdapter, range: PickerRangeValue): range is PickerNonNullableRangeValue {
  return Boolean(range[0] && range[1]) && !utils.isBefore(range[1], range[0]);
}

/**
 * Checks if a date is the start of a range
 */
export function isStartOfRange(utils: DateAdapter, day: PickerDateType, range: PickerRangeValue): boolean {
  return isRangeValid(utils, range) && utils.isSameDay(day, range[0]);
}

/**
 * Checks if a date is the end of a range
 */
export function isEndOfRange(utils: DateAdapter, day: PickerDateType, range: PickerRangeValue): boolean {
  return isRangeValid(utils, range) && utils.isSameDay(day, range[1]);
}

/**
 * Checks if a date is within a range (inclusive of bounds)
 */
export function isWithinRange(utils: DateAdapter, day: PickerDateType, range: PickerRangeValue): boolean {
  if (!isRangeValid(utils, range)) {
    return false;
  }

  return (
    (utils.isAfterDay(day, range[0]) && utils.isBeforeDay(day, range[1])) ||
    utils.isSameDay(day, range[0]) ||
    utils.isSameDay(day, range[1])
  );
}

/**
 * Format a date range using the adapter's format method
 */
export function formatRange(
  utils: DateAdapter,
  range: PickerRangeValue,
  formatKey: keyof AdapterFormats,
): string | null {
  if (!isRangeValid(utils, range)) {
    return null;
  }

  return `${utils.format(range[0], formatKey)} - ${utils.format(range[1], formatKey)}`;
}

interface CalculateRangeChangeOptions {
  /**
   * The utilities object for date manipulation
   */
  utils: DateAdapter;
  /**
   * The current range value
   */
  range: PickerRangeValue;
  /**
   * The newly selected date
   */
  newDate: PickerDateType;
  /**
   * Whether the user is selecting the start or end of the range
   */
  rangePosition: RangePosition;
}

interface CalculateRangeChangeResult {
  /**
   * The new range value after the selection
   */
  newRange: PickerRangeValue;
  /**
   * The next position that should be selected (start or end)
   */
  nextSelection: RangePosition;
}

/**
 * Calculate the new range value based on the current selection
 */
export function calculateRangeChange({
  utils,
  range,
  newDate,
  rangePosition,
}: CalculateRangeChangeOptions): CalculateRangeChangeResult {
  // Get properly typed start and end values
  const start = range[0];
  const end = range[1];

  // Selecting start of range
  if (rangePosition === 'start') {
    // If selecting a new start date
    return {
      newRange: [newDate, end],
      nextSelection: 'end',
    };
  }

  // Selecting end of range
  if (rangePosition === 'end') {
    // If selecting a new end date
    return {
      newRange: [start, newDate],
      nextSelection: 'start',
    };
  }

  // Fallback (shouldn't happen)
  return {
    newRange: range,
    nextSelection: rangePosition,
  };
}

/**
 * Calculate the preview range when hovering over a date
 */
export function calculateRangePreview({
  utils,
  range,
  newDate,
  rangePosition,
}: CalculateRangeChangeOptions): PickerRangeValue {
  const [start, end] = range;

  // When selecting the start, the preview should be from the hover date to the existing end
  if (rangePosition === 'start') {
    // If there's no end date, the preview is just the start date
    if (!end) {
      return [newDate, null];
    }

    // If the new start date is after the end date, there's no valid preview
    if (utils.isAfter(newDate, end)) {
      return [null, null];
    }

    // Preview from the hovered date to the existing end
    return [newDate, end];
  }

  // When selecting the end, the preview should be from the existing start to the hover date
  if (rangePosition === 'end') {
    // If there's no start date, the preview is just the end date
    if (!start) {
      return [null, newDate];
    }

    // If the new end date is before the start date, there's no valid preview
    if (utils.isBefore(newDate, start)) {
      return [null, null];
    }

    // Preview from the existing start to the hovered date
    return [start, newDate];
  }

  // Fallback (shouldn't happen)
  return [null, null];
}
