import { useRef } from 'react';
import { PickerDateType } from '../models/pickers';
import { usePickerContext } from '../PickerContext';

export const useUtils = () => usePickerContext().utils;

export interface DefaultDates {
  /**
   * Default minimum date for date pickers
   * @default '1900-01-01'
   */
  minDate: PickerDateType;

  /**
   * Default maximum date for date pickers
   * @default '2099-12-31'
   */
  maxDate: PickerDateType;
}

/**
 * Hook that returns default min and max dates for date pickers
 * Uses the utils to create these default dates
 */
export const useDefaultDates = (): DefaultDates => {
  const utils = useUtils();

  return {
    minDate: utils.date('1900-01-01'),
    maxDate: utils.date('2099-12-31'),
  };
};

export const useNow = (): PickerDateType => {
  const utils = useUtils();

  const now = useRef<PickerDateType>(undefined);
  if (now.current === undefined) {
    now.current = utils.date(undefined);
  }
  return now.current!;
};
