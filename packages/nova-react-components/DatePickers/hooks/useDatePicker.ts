import * as React from 'react';
import { unstable_useEventCallback as useEventCallback } from '@mui/utils';
import { PickerDateType } from '../models/pickers';
import { useDateValidation } from './useDateValidation';
import { useControlledValue } from './useControlledValue';
import { useOpenState } from './useOpenState';
import { useViews } from './useViews';
import { DatePickerView } from '../types';
import { useUtils } from './useUtils';

export interface UseDatePickerParams {
  /**
   * The selected date.
   */
  value?: PickerDateType;

  /**
   * The default selected date.
   */
  defaultValue?: PickerDateType;

  /**
   * Callback fired when the date changes.
   */
  onChange?: (date: PickerDateType) => void;

  /**
   * Controls whether the popup is open.
   */
  open?: boolean;

  /**
   * Callback fired when the popup is opened.
   */
  onOpen?: () => void;

  /**
   * Callback fired when the popup is closed.
   */
  onClose?: () => void;

  /**
   * If `true`, the component is disabled.
   */
  disabled?: boolean;

  /**
   * If `true`, the component is read-only.
   */
  readOnly?: boolean;

  /**
   * Minimum selectable date.
   */
  minDate?: any;

  /**
   * Maximum selectable date.
   */
  maxDate?: any;

  /**
   * If `true`, disable dates after the current date.
   */
  disableFuture?: boolean;

  /**
   * If `true`, disable dates before the current date.
   */
  disablePast?: boolean;

  /**
   * Function that returns a boolean if the given date should be disabled.
   */
  shouldDisableDate?: (date: any) => boolean;

  /**
   * Format of the date when rendered in the input.
   */
  format?: string;

  /**
   * If `true`, the picker closes after a date is selected.
   */
  closeOnSelect?: boolean;

  /**
   * If `true`, focuses the calendar on open.
   */
  autoFocus?: boolean;

  /**
   * Available views for the date picker.
   */
  views?: readonly DatePickerView[] | DatePickerView[];

  /**
   * Callback fired when the view changes.
   */
  onViewChange?: (view: DatePickerView) => void;
}

export interface UseDatePickerResult {
  // Date state
  dateState: {
    selectedDate: PickerDateType;
    tempDate: PickerDateType;
  };
  setDateState: React.Dispatch<
    React.SetStateAction<{
      selectedDate: PickerDateType;
      tempDate: PickerDateType;
    }>
  >;

  // Open state
  open: boolean;
  handleOpen: (event?: React.MouseEvent<HTMLElement>) => void;
  handleClose: () => void;
  anchorEl: HTMLElement | null;

  // View state
  currentView: DatePickerView;
  availableViews: readonly DatePickerView[];

  // Options
  disabled: boolean;
  readOnly: boolean;
  closeOnSelect: boolean;

  // Validation
  validateDate: (date: any) => boolean;

  // Date change handlers
  handleViewDateChange: (date: PickerDateType) => void;
  handleDateChange: (date: PickerDateType) => void;
  handleInputChange: (date: PickerDateType) => void;
  handleViewChange: (view: DatePickerView) => void;
  // Action handlers
  handleAccept: () => void;
  handleCancel: () => void;
  handleClear: () => void;
}

// Default views
const DEFAULT_VIEWS: readonly DatePickerView[] = ['day', 'month', 'year'];

/**
 * Hook to manage DatePicker state and logic.
 *
 * @param props DatePicker props
 * @returns State and handlers for the date picker
 */
export function useDatePicker(props: UseDatePickerParams): UseDatePickerResult {
  const {
    value: valueProp,
    defaultValue,
    onChange,
    open: openProp,
    onOpen,
    onClose,
    disabled = false,
    readOnly = false,
    minDate,
    maxDate,
    disableFuture = false,
    disablePast = false,
    shouldDisableDate,
    format = 'MM/DD/YYYY',
    closeOnSelect = true,
    autoFocus = false,
    views = DEFAULT_VIEWS,
    onViewChange,
  } = props;

  const utils = useUtils();

  // Use the controlled value hook for the date
  const [selectedDate, setSelectedDate] = useControlledValue<PickerDateType>(valueProp, defaultValue);

  // State for date picker
  const [dateState, setDateState] = React.useState(() => ({
    selectedDate: selectedDate || null,
    tempDate: selectedDate || null,
  }));

  // Use the open state hook
  const {
    open,
    anchorEl,
    handleOpen,
    handleClose: baseHandleClose,
  } = useOpenState({
    open: openProp,
    onOpen,
    onClose,
    disabled,
    readOnly,
    autoFocus,
  });

  // Use the view management hook
  const {
    view: currentView,
    setView: setCurrentView,
    views: availableViews,
  } = useViews({
    openTo: 'day',
    views,
    onViewChange,
  });

  // Use the date validation hook
  const { validateDate } = useDateValidation({
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    format,
  });

  // Handle view date change
  const handleViewDateChange = useEventCallback((date: PickerDateType) => {
    // Date is valid, proceed normally
    setDateState((prev) => ({
      ...prev,
      selectedDate: date,
    }));
    onChange?.(date);
  });

  // Handle date change with validation
  const handleDateChange = useEventCallback((date: PickerDateType) => {
    // Validate the date - will update validation error state if invalid
    if (!validateDate(date)) {
      return; // Don't proceed if invalid
    }
    // Date is valid, proceed normally
    setDateState((prev) => ({
      ...prev,
      tempDate: date,
    }));
    onChange?.(date);

    // Close picker on selection if configured
    if (date && closeOnSelect) {
      handleClose();
    }
  });

  // Handle input field change
  const handleInputChange = useEventCallback((value: PickerDateType) => {
    // Valid date, update state
    setDateState((prev) => ({
      ...prev,
      tempDate: value,
      selectedDate: value,
    }));
    onChange?.(value);
  });

  // Handle accepting the selected date
  const handleAccept = useEventCallback(() => {
    if (!utils.isSameDay(dateState.tempDate, dateState.selectedDate)) {
      setDateState((prev) => ({
        ...prev,
        selectedDate: dateState.tempDate,
      }));
      onChange?.(dateState.tempDate);
    }
    baseHandleClose();
  });

  // Handle canceling the selection
  const handleCancel = useEventCallback(() => {
    setDateState((prev) => ({
      ...prev,
      tempDate: dateState.selectedDate,
    }));
    onChange?.(dateState.selectedDate);
    baseHandleClose();
  });

  // Handle clearing the selection
  const handleClear = useEventCallback(() => {
    setDateState((prev) => ({
      ...prev,
      tempDate: null,
      selectedDate: null,
    }));
    onChange?.(null);
    baseHandleClose();
  });

  // Custom handleClose that wraps the base handleClose
  const handleClose = useEventCallback(() => {
    baseHandleClose();
  });

  const handleViewChange = useEventCallback((view: DatePickerView) => {
    setCurrentView(view);
  });

  return {
    dateState,
    setDateState,
    open,
    currentView,
    handleOpen,
    handleClose,
    anchorEl,
    availableViews,
    disabled,
    readOnly,
    closeOnSelect,
    validateDate,
    handleViewDateChange,
    handleDateChange,
    handleInputChange,
    handleAccept,
    handleCancel,
    handleClear,
    handleViewChange,
  };
}
