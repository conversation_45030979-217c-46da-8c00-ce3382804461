import { PickerDateType } from '../models/pickers';
import { usePickerContext } from '../PickerContext';
import { validateDate, ValidateDateProps } from '../utils/validateDate';

export const useIsDateDisabled = ({
  shouldDisableDate,
  shouldDisableMonth,
  shouldDisableYear,
  minDate,
  maxDate,
  disableFuture,
  disablePast,
}: ValidateDateProps) => {
  const adapter = usePickerContext();

  return React.useCallback(
    (day: PickerDateType | null) =>
      validateDate({
        adapter,
        value: day,
        props: {
          shouldDisableDate,
          shouldDisableMonth,
          shouldDisableYear,
          minDate,
          maxDate,
          disableFuture,
          disablePast,
        },
      }) !== null,
    [adapter, shouldDisableDate, shouldDisableMonth, shouldDisableYear, minDate, maxDate, disableFuture, disablePast],
  );
};
