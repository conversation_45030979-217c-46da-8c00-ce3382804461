import React, { useCallback, useState, useRef, useMemo } from 'react';
import { useDateSections } from './useDateSections';
import { useDateFieldState, DateFieldStateProps } from './useDateFieldState';
import { useDateFieldDOMGetters } from './useDateFieldDOMGetters';
import { useSelectionSynchronization } from './useSelectionSynchronization';
import { useDateFieldAccessibility } from './useDateFieldAccessibility';
import { useEnhancedEffect } from '../../utils/domUtils';
import { addPositionPropertiesToSections, getInputValueFromSections, cleanString } from '../../utils/sectionUtils';

/**
 * Comprehensive hook that integrates all DateField functionality in MUI-style
 * This combines DOM handling, accessibility, selection sync, and state management
 */
export function useFieldIntegration(
  props: DateFieldStateProps & {
    disabled?: boolean;
    readOnly?: boolean;
    required?: boolean;
  },
) {
  // Destructure the props
  const { disabled = false, readOnly = false, required = false, format, ...dateFieldProps } = props;

  // Create refs for DOM elements
  const rootRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const actualInputRef = useRef<HTMLInputElement | null>(null);

  // Track focus state
  const [isFocused, setIsFocused] = useState(false);

  // Use the DateFieldState hook for value management
  const {
    inputValue: rawInputValue,
    error: fieldError,
    handleInputChange: originalHandleInputChange,
    handleClear: originalHandleClear,
    updateValueFromValueStr,
    isValid,
    date: dateValue,
  } = useDateFieldState({
    format,
    ...dateFieldProps,
  });

  // Use the DateSections hook for section management
  const {
    sections,
    setSections: updateSections,
    activeSection,
    setActiveSection,
    moveToPreviousSection,
    moveToNextSection,
    processCharacterInput,
    handleClipboardPaste,
  } = useDateSections({
    format,
    value: rawInputValue,
    onChange: (newValue) => {
      const syntheticEvent = {
        target: { value: newValue },
      } as React.ChangeEvent<HTMLInputElement>;
      originalHandleInputChange(syntheticEvent);
    },
  });

  // Custom handleClear that also updates the sections
  const handleClear = useCallback(() => {
    // First call the original clear function
    originalHandleClear();

    // Then clear all sections by updating them with empty values
    const emptySections = sections.map((section) => ({
      ...section,
      value: '',
      displayValue: '',
      modified: false,
    }));

    updateSections(emptySections);
    setActiveSection(null);
  }, [originalHandleClear, sections, updateSections, setActiveSection]);

  // Calculate section positions
  const sectionsWithPositions = addPositionPropertiesToSections(sections);

  // Generate input value - IMPORTANT: This must update immediately when sections change
  const inputValue = useMemo(() => {
    // Always use sections to generate the input value, regardless of focus state
    // This ensures partial values are always displayed
    if (rawInputValue.length > 0) {
      return rawInputValue || '';
    } else {
      return getInputValueFromSections(sectionsWithPositions, { includeRTLCharacters: false });
    }
  }, [rawInputValue, sectionsWithPositions]);

  // Track if we need a forced input update
  const [needsInputUpdate, setNeedsInputUpdate] = useState(false);

  // Add an effect to update sections whenever rawInputValue changes
  useEnhancedEffect(() => {
    if (rawInputValue && !isFocused) {
      // Wait for next tick to ensure we have the latest value
      setTimeout(() => {
        // Mark that we need to update the input value
        setNeedsInputUpdate(true);
      }, 0);
    }
  }, [rawInputValue, isFocused]);

  // Create DOM getters
  const domGetters = useDateFieldDOMGetters(rootRef, inputRef, actualInputRef, sectionsWithPositions);

  // Use selection synchronization
  const { synchronizeSelectionToDOM, synchronizeSelectionFromDOM } = useSelectionSynchronization(
    domGetters,
    sectionsWithPositions,
    activeSection,
    setActiveSection,
  );

  // Get accessibility attributes
  const { rootAriaAttributes, getSectionAriaAttributes } = useDateFieldAccessibility(
    !!fieldError,
    disabled,
    readOnly,
    required,
    activeSection,
  );

  // Use layout effect to sync selection to DOM and handle incomplete inputs
  useEnhancedEffect(() => {
    if ((isFocused && activeSection !== null) || needsInputUpdate) {
      // Force update input value to match sections after a brief delay
      // This ensures our section changes are reflected in the UI
      setTimeout(() => {
        if (needsInputUpdate) {
          setNeedsInputUpdate(false);
        }

        if (isFocused && activeSection !== null) {
          synchronizeSelectionToDOM();
        }
      }, 5);
    }
  }, [isFocused, activeSection, synchronizeSelectionToDOM, sectionsWithPositions, needsInputUpdate]);

  // Add a new useEffect to ensure the input value updates whenever sections change
  useEnhancedEffect(() => {
    if ((isFocused && sectionsWithPositions.length > 0) || needsInputUpdate) {
      setNeedsInputUpdate(false);
    }
  }, [isFocused, sectionsWithPositions, inputRef, rawInputValue, needsInputUpdate]);

  // Click handler
  const handleInputClick = useCallback(
    (e: React.MouseEvent<HTMLInputElement>) => {
      if (disabled || readOnly) {
        return;
      }

      if (!isFocused) {
        setIsFocused(true);
      }

      synchronizeSelectionFromDOM();
    },
    [disabled, readOnly, isFocused, synchronizeSelectionFromDOM],
  );

  // Focus handler
  const handleInputFocus = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      if (disabled || readOnly) {
        return;
      }

      setIsFocused(true);

      // If the input element is not yet known, find it
      if (!actualInputRef.current) {
        const target = e.target.querySelector('input') || e.target;
        if (target instanceof HTMLInputElement) {
          actualInputRef.current = target;
        }
      }

      // Always select the first section when focusing an empty field
      if (activeSection === null) {
        // Make sure sections are available before setting active section
        if (sectionsWithPositions.length > 0) {
          setActiveSection(0);
          // Use a timeout to ensure DOM is ready for selection
          setTimeout(() => {
            synchronizeSelectionToDOM();
          }, 10);
        } else {
          // If no sections, try reading from DOM after a short delay
          setTimeout(() => {
            synchronizeSelectionFromDOM();
          }, 10);
        }
      } else {
        synchronizeSelectionToDOM();
      }
    },
    [
      disabled,
      readOnly,
      activeSection,
      sectionsWithPositions.length,
      setActiveSection,
      synchronizeSelectionToDOM,
      synchronizeSelectionFromDOM,
    ],
  );

  // Blur handler
  const handleInputBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      // Only blur if focus is moving outside the component
      if (rootRef.current && !rootRef.current.contains(e.relatedTarget as Node)) {
        // No need to call updateValueFromValueStr here since we're keeping the partial input
        // Just update our focus state
        setIsFocused(false);
        setActiveSection(null);
      }
    },
    [rootRef, setActiveSection],
  );

  // Input change handler
  const handleInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (readOnly) {
        return;
      }

      const targetValue = event.target.value;
      if (targetValue === '') {
        handleClear();
        return;
      }

      // Clean the target value (remove Unicode markers)
      const cleanTargetValue = cleanString(targetValue);

      // Extract input data
      const eventData = (event.nativeEvent as any).data;
      const shouldUseEventData = eventData && eventData.length > 1;

      // If no section is selected or we're using event data, parse the whole value
      if (activeSection === null || shouldUseEventData) {
        const success = updateValueFromValueStr(shouldUseEventData ? eventData : targetValue);

        // Ensure the input value is updated even if parsing fails
        if (!success && inputRef.current) {
          // Schedule an update to make sure input reflects what user typed
          setNeedsInputUpdate(true);
        }
        return;
      }

      // MUI-style detection of what character was pressed
      if (sectionsWithPositions.length === 0 || activeSection >= sectionsWithPositions.length) {
        return;
      }

      let keyPressed: string;
      // If the whole input was previously selected
      if (activeSection === 0 && cleanTargetValue.length === 1) {
        keyPressed = cleanTargetValue;
      } else {
        // Compare previous and current values to determine what changed
        const prevInputValue = getInputValueFromSections(sectionsWithPositions, {
          includeRTLCharacters: false,
          cleanStr: true,
        });

        // Find where the difference starts and ends
        let startOfDiffIndex = -1;
        let endOfDiffIndex = -1;

        for (let i = 0; i < Math.max(prevInputValue.length, cleanTargetValue.length); i++) {
          if (startOfDiffIndex === -1 && prevInputValue[i] !== cleanTargetValue[i]) {
            startOfDiffIndex = i;
          }

          if (
            endOfDiffIndex === -1 &&
            prevInputValue[prevInputValue.length - i - 1] !== cleanTargetValue[cleanTargetValue.length - i - 1]
          ) {
            endOfDiffIndex = i;
          }
        }

        if (startOfDiffIndex === -1) {
          return; // No change detected
        }

        const activeSectionWithPosition = sectionsWithPositions[activeSection];

        // Check if the edit occurred outside the active section
        const hasDiffOutsideOfActiveSection =
          startOfDiffIndex < activeSectionWithPosition.startInInput ||
          prevInputValue.length - endOfDiffIndex - 1 > activeSectionWithPosition.endInInput;

        if (hasDiffOutsideOfActiveSection) {
          // Changes outside active section - try to parse the whole value
          const success = updateValueFromValueStr(targetValue);

          // Ensure the input value is updated even if parsing fails
          if (!success && inputRef.current) {
            // Schedule an update to make sure input reflects what user typed
            setNeedsInputUpdate(true);
          }
          return;
        }

        // Extract the key pressed from the changed section
        keyPressed = cleanTargetValue.substring(
          activeSectionWithPosition.startInInput,
          cleanTargetValue.length - (prevInputValue.length - activeSectionWithPosition.endInInput),
        );
      }

      if (keyPressed && keyPressed.length > 0) {
        // Process the character input
        processCharacterInput(keyPressed);
      }
    },
    [
      readOnly,
      activeSection,
      handleClear,
      updateValueFromValueStr,
      processCharacterInput,
      sectionsWithPositions,
      inputRef,
    ],
  );

  // Key down handler
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (disabled || readOnly) return;

      const { key, ctrlKey, metaKey, altKey } = event;

      // Handle navigation keys
      switch (key) {
        case 'ArrowLeft':
          event.preventDefault();
          moveToPreviousSection();
          return;

        case 'ArrowRight':
          event.preventDefault();
          moveToNextSection();
          return;

        case 'Home':
          event.preventDefault();
          setActiveSection(0);
          return;

        case 'End':
          event.preventDefault();
          setActiveSection(sections.length - 1);
          return;

        case 'Escape':
          event.preventDefault();
          setActiveSection(null);
          if (actualInputRef.current) {
            actualInputRef.current.blur();
          }
          return;

        case 'Backspace':
        case 'Delete':
          event.preventDefault();
          if (activeSection !== null && activeSection < sections.length) {
            const newSections = [...sections];
            newSections[activeSection] = {
              ...newSections[activeSection],
              value: '',
            };
            updateSections(newSections);
          }
          return;
      }

      // Handle separator keys
      if (key === '/' || key === '-' || key === '.') {
        event.preventDefault();
        if (activeSection !== null && activeSection < sections.length - 1) {
          moveToNextSection();
        }
        return;
      }

      // Handle printable characters
      if (key.length === 1 && !altKey && !ctrlKey && !metaKey) {
        event.preventDefault();

        if (activeSection === null) {
          setActiveSection(0);
          setTimeout(() => {
            processCharacterInput(key);
          }, 0);
        } else {
          processCharacterInput(key);
        }
      }
    },
    [
      disabled,
      readOnly,
      activeSection,
      sections,
      moveToPreviousSection,
      moveToNextSection,
      setActiveSection,
      updateSections,
      processCharacterInput,
    ],
  );

  // Wrap the handleClipboardPaste for use with React's onPaste event
  const handleDatePaste = useCallback(
    (event: React.ClipboardEvent<HTMLInputElement>) => {
      if (disabled || readOnly) return;

      event.preventDefault();
      const pastedText = event.clipboardData.getData('text');
      if (pastedText) {
        handleClipboardPaste(pastedText);
      }
    },
    [disabled, readOnly, handleClipboardPaste],
  );

  return {
    // Refs
    rootRef,
    inputRef,
    actualInputRef,

    // State values
    inputValue,
    sections,
    activeSection,
    isFocused,
    error: fieldError,
    dateValue,

    // Event handlers
    handleInputClick,
    handleInputFocus,
    handleInputBlur,
    handleInputChange,
    handleKeyDown,
    handleClear,
    handlePaste: handleDatePaste, // Expose the clipboard paste handler with original name for compatibility

    // Utility methods
    updateValueFromValueStr,
    setActiveSection,
    moveToPreviousSection,
    moveToNextSection,

    // Accessibility
    rootAriaAttributes,
    getSectionAriaAttributes,

    // DOM utilities
    domGetters,
    synchronizeSelectionToDOM,
    synchronizeSelectionFromDOM,
  };
}

export default useFieldIntegration;
