import { useState, useEffect, ChangeEvent } from 'react';
import { useUtils } from '../useUtils';
import { PickerDateType } from '../../models/pickers';
import {
  parseDateSections,
  parseFormatIntoSections,
  mergeParsedDateWithReference,
  getSectionsFromFormat,
  extractSectionsFromValueString,
} from '../../utils/sectionUtils';
import { FieldSectionType } from '../../models/dateSection';
import { getSectionBoundaries } from '../../utils/domUtils';

// Define a proper interface
export interface DateFieldStateProps {
  value?: PickerDateType | null;
  defaultValue?: PickerDateType | null;
  onChange?: (date: PickerDateType | null) => void;
  format: string;
  minDate?: PickerDateType;
  maxDate?: PickerDateType;
  shouldDisableDate?: (date: PickerDateType) => boolean;
  disableFuture?: boolean;
  disablePast?: boolean;
}

// Section type definition to match MUI's approach
export interface DateSection {
  type: FieldSectionType;
  value?: string;
  modified?: boolean;
}

// Simplified field section manager
export const fieldValueManager = {
  // Parse a value string into a date
  parseValueStr: (valueStr: string, utils: any, format: string): PickerDateType | null => {
    const trimmedValueStr = valueStr.trim();
    if (!trimmedValueStr) return null;

    try {
      // First try: exact format match
      const parsedDate = utils.parse(trimmedValueStr, format);
      if (parsedDate && utils.isValid(parsedDate)) {
        return parsedDate;
      }

      // Fallback to common formats
      const commonFormats = ['MM/DD/YYYY', 'MM-DD-YYYY', 'YYYY-MM-DD', 'DD/MM/YYYY'];

      for (const fmt of commonFormats) {
        if (fmt === format) continue;

        const altParsedDate = utils.parse(trimmedValueStr, fmt);
        if (altParsedDate && utils.isValid(altParsedDate)) {
          return altParsedDate;
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  },

  // Get sections from date
  getSectionsFromValue: (date: PickerDateType | null, format: string, utils: any): DateSection[] => {
    if (!date) return [];
    const formattedValue = date ? utils.formatByString(date, format) : '';
    return parseDateSections(format, formattedValue);
  },

  // Reliable modification order
  reliableSectionModificationOrder: {
    year: 1,
    month: 2,
    day: 3,
    weekDay: 4,
    hours: 5,
    minutes: 6,
    seconds: 7,
    meridiem: 8,
    empty: 9,
  },

  // Merge date into reference date
  mergeDateIntoReferenceDate: (
    utils: any,
    dateToTransferFrom: PickerDateType,
    sections: DateSection[],
    referenceDate: PickerDateType,
    shouldLimitToEditedSections: boolean = false,
    reliableSectionModificationOrder: Record<string, number>,
  ): PickerDateType => {
    // Sort sections by their modification order
    const sortedSections = [...sections].sort(
      (a, b) => (reliableSectionModificationOrder[a.type] || 99) - (reliableSectionModificationOrder[b.type] || 99),
    );

    // Process date components in order
    return sortedSections.reduce((mergedDate, section) => {
      // Only transfer values from modified sections if shouldLimitToEditedSections is true
      if (!shouldLimitToEditedSections || section.modified) {
        return fieldValueManager.transferDateSectionValue(utils, section, dateToTransferFrom, mergedDate);
      }
      return mergedDate;
    }, referenceDate);
  },

  // Transfer date section value
  transferDateSectionValue: (
    utils: any,
    section: DateSection,
    dateToTransferFrom: PickerDateType,
    dateToTransferTo: PickerDateType,
  ): PickerDateType => {
    switch (section.type) {
      case 'year':
        return utils.setYear(dateToTransferTo, utils.getYear(dateToTransferFrom));

      case 'month':
        return utils.setMonth(dateToTransferTo, utils.getMonth(dateToTransferFrom));

      case 'day':
        return utils.setDate(dateToTransferTo, utils.getDate(dateToTransferFrom));

      case 'weekDay':
        return dateToTransferTo;

      case 'hours':
        return utils.setHours(dateToTransferTo, utils.getHours(dateToTransferFrom));

      case 'minutes':
        return utils.setMinutes(dateToTransferTo, utils.getMinutes(dateToTransferFrom));

      case 'seconds':
        return utils.setSeconds(dateToTransferTo, utils.getSeconds(dateToTransferFrom));

      case 'meridiem': {
        // Handle AM/PM conversion
        const isAM = utils.getHours(dateToTransferFrom) < 12;
        const mergedDateHours = utils.getHours(dateToTransferTo);

        if (isAM && mergedDateHours >= 12) {
          return utils.setHours(dateToTransferTo, mergedDateHours - 12);
        }

        if (!isAM && mergedDateHours < 12) {
          return utils.setHours(dateToTransferTo, mergedDateHours + 12);
        }

        return dateToTransferTo;
      }

      default:
        return dateToTransferTo;
    }
  },
};

export function useDateFieldState(props: DateFieldStateProps) {
  const {
    value: valueProp,
    defaultValue,
    onChange,
    format,
    minDate,
    maxDate,
    shouldDisableDate,
    disableFuture,
    disablePast,
  } = props;

  const utils = useUtils();
  const [date, setDate] = useState<PickerDateType | null>(defaultValue || valueProp || null);
  const [inputValue, setInputValue] = useState<string>(date ? utils.formatByString(date, format) : '');
  const [error, setError] = useState<string | null>(null);
  const [referenceDate, setReferenceDate] = useState<PickerDateType>(date || utils.date());
  const [activeSectionType, setActiveSectionType] = useState<FieldSectionType | null>(null);
  const [characterQuery, setCharacterQuery] = useState<{ value: string; timestamp: number } | null>(null);

  // Handle controlled value changes
  useEffect(() => {
    if (valueProp !== undefined) {
      setDate(valueProp);
      const formattedValue = valueProp ? utils.formatByString(valueProp, format) : '';
      setInputValue(formattedValue);

      if (valueProp && utils.isValid(valueProp)) {
        setReferenceDate(valueProp);
      }
    }
  }, [valueProp, format, utils]);

  // Validate date
  const validate = (newDate: PickerDateType | null): boolean => {
    if (!newDate) return true;

    if (minDate && utils.isBefore(newDate, minDate)) {
      setError('Date is before minimum date');
      return false;
    }

    if (maxDate && utils.isAfter(newDate, maxDate)) {
      setError('Date is after maximum date');
      return false;
    }

    if (shouldDisableDate && shouldDisableDate(newDate)) {
      setError('Date is disabled');
      return false;
    }

    if (
      disableFuture &&
      utils.isAfter(newDate, utils.date(utils.formatByString(utils.date(), 'YYYY-MM-DDTHH:mm:ss.SSS')))
    ) {
      setError('Date is in the future');
      return false;
    }

    if (
      disablePast &&
      utils.isBefore(newDate, utils.date(utils.formatByString(utils.date(), 'YYYY-MM-DDTHH:mm:ss.SSS')))
    ) {
      setError('Date is in the past');
      return false;
    }

    setError(null);
    return true;
  };

  // Parse date string with multiple formats
  const parseDateStr = (valueStr: string, referenceDate: PickerDateType): PickerDateType | null => {
    const cleanValueStr = valueStr.trim();
    if (!cleanValueStr) return null;

    // Try primary format
    let parsedDate = utils.parse(cleanValueStr, format);
    if (parsedDate && utils.isValid(parsedDate)) {
      return parsedDate;
    }

    // Common formats to try
    const fallbackFormats = [
      'MM/DD/YYYY',
      'DD/MM/YYYY',
      'YYYY-MM-DD',
      'YYYY/MM/DD',
      'MM-DD-YYYY',
      'DD-MM-YYYY',
      'MM.DD.YYYY',
      'DD.MM.YYYY',
      'MM/DD/YYYY HH:mm',
      'YYYY-MM-DD HH:mm',
      'MM/DD/YYYY h:mm A',
    ].filter((f) => f !== format);

    // Try each fallback format
    for (const fallbackFormat of fallbackFormats) {
      try {
        parsedDate = utils.parse(cleanValueStr, fallbackFormat);
        if (parsedDate && utils.isValid(parsedDate)) {
          return parsedDate;
        }
      } catch (error) {
        // Continue to next format
      }
    }

    // Try extracting sections
    try {
      const extractedSections = extractSectionsFromValueString(cleanValueStr, format, utils);
      let newDate = utils.date(utils.formatByString(referenceDate, 'YYYY-MM-DDTHH:mm:ss.SSS'));
      let hasValidSection = false;

      if (extractedSections.year) {
        newDate = utils.setYear(newDate, parseInt(extractedSections.year, 10));
        hasValidSection = true;
      }

      if (extractedSections.month) {
        newDate = utils.setMonth(newDate, parseInt(extractedSections.month, 10) - 1);
        hasValidSection = true;
      }

      if (extractedSections.day) {
        newDate = utils.setDate(newDate, parseInt(extractedSections.day, 10));
        hasValidSection = true;
      }

      if (extractedSections.hours) {
        let hours = parseInt(extractedSections.hours, 10);
        if (extractedSections.meridiem && extractedSections.meridiem.toUpperCase() === 'PM' && hours < 12) {
          hours += 12;
        } else if (extractedSections.meridiem && extractedSections.meridiem.toUpperCase() === 'AM' && hours === 12) {
          hours = 0;
        }
        newDate = utils.setHours(newDate, hours);
        hasValidSection = true;
      }

      if (extractedSections.minutes) {
        newDate = utils.setMinutes(newDate, parseInt(extractedSections.minutes, 10));
        hasValidSection = true;
      }

      if (extractedSections.seconds) {
        newDate = utils.setSeconds(newDate, parseInt(extractedSections.seconds, 10));
        hasValidSection = true;
      }

      if (hasValidSection && utils.isValid(newDate)) {
        return newDate;
      }
    } catch (error) {
      // Fall through to next approach
    }

    // Try handling partial input for active section
    if (cleanValueStr.length <= 4 && activeSectionType) {
      return parsePartialInput(cleanValueStr, activeSectionType, referenceDate);
    }

    return null;
  };

  // Parse partial input for a specific section
  const parsePartialInput = (
    value: string,
    sectionType: FieldSectionType,
    referenceDate: PickerDateType,
  ): PickerDateType | null => {
    // Handle meridiem (AM/PM) input
    if (sectionType === 'meridiem') {
      if (/^[aA]/.test(value)) {
        let newDate = utils.date(utils.formatByString(referenceDate, 'YYYY-MM-DDTHH:mm:ss.SSS'));
        const hours = utils.getHours(newDate);
        if (hours >= 12) {
          newDate = utils.setHours(newDate, hours - 12);
        }
        return newDate;
      } else if (/^[pP]/.test(value)) {
        let newDate = utils.date(utils.formatByString(referenceDate, 'YYYY-MM-DDTHH:mm:ss.SSS'));
        const hours = utils.getHours(newDate);
        if (hours < 12) {
          newDate = utils.setHours(newDate, hours + 12);
        }
        return newDate;
      }
      return null;
    }

    // Only process numeric input for numeric sections
    if (!/^\d+$/.test(value) || !['year', 'month', 'day', 'hours', 'minutes', 'seconds'].includes(sectionType)) {
      return null;
    }

    const numValue = parseInt(value, 10);
    if (isNaN(numValue)) {
      return null;
    }

    // Get section boundaries
    const boundaries = getSectionBoundaries(utils);
    try {
      const newDate = utils.date(utils.formatByString(referenceDate, 'YYYY-MM-DDTHH:mm:ss.SSS'));

      switch (sectionType) {
        case 'year': {
          const yearBoundary = boundaries.year({ currentDate: referenceDate });
          if (value.length === 4 && (numValue < yearBoundary.minimum || numValue > yearBoundary.maximum)) {
            return null;
          }
          return utils.setYear(newDate, numValue);
        }

        case 'month': {
          if (value.length === 2 && (numValue < 1 || numValue > 12)) {
            return null;
          }
          return utils.setMonth(newDate, numValue - 1);
        }

        case 'day': {
          const dayBoundary = boundaries.day({ currentDate: referenceDate });
          if (value.length === 2 && (numValue < 1 || numValue > dayBoundary.maximum)) {
            return null;
          }
          return utils.setDate(newDate, numValue);
        }

        case 'hours': {
          const hourBoundary = boundaries.hours({ format });
          if (value.length === 2 && (numValue < hourBoundary.minimum || numValue > hourBoundary.maximum)) {
            return null;
          }
          return utils.setHours(newDate, numValue);
        }

        case 'minutes': {
          if (value.length === 2 && (numValue < 0 || numValue > 59)) {
            return null;
          }
          return utils.setMinutes(newDate, numValue);
        }

        case 'seconds': {
          if (value.length === 2 && (numValue < 0 || numValue > 59)) {
            return null;
          }
          return utils.setSeconds(newDate, numValue);
        }

        default:
          return null;
      }
    } catch (error) {
      return null;
    }
  };

  // Update value from value string
  const updateValueFromValueStr = (valueStr: string): boolean => {
    // Handle empty input
    if (!valueStr.trim()) {
      setDate(null);
      setInputValue('');
      onChange?.(null);
      return false;
    }

    // Always update the input value to show what was entered, even if parsing fails
    setInputValue(valueStr.trim());

    try {
      // Get current reference date
      const currentReferenceDate = date || referenceDate || utils.date();

      // Parse the value string
      const parsedDate = parseDateStr(valueStr, currentReferenceDate);

      // If parsing fails completely, keep the input value but don't update the date
      if (!parsedDate) {
        // Keep showing what the user entered
        setDate(null);
        onChange?.(null);
        return false;
      }

      // Get section types from format
      const formatSectionTypes = getSectionsFromFormat(format) as FieldSectionType[];

      // Merge with reference date to preserve date components not in format
      let mergedDate: PickerDateType;

      // If we're in active section editing mode, only transfer the active section
      if (activeSectionType) {
        // Get sections from both parsed and reference dates
        const sectionsFromParsed = fieldValueManager.getSectionsFromValue(parsedDate, format, utils);
        const sectionsFromReference = fieldValueManager.getSectionsFromValue(currentReferenceDate, format, utils);

        // Mark only the active section as modified
        const modifiedSections = sectionsFromParsed.map((section, i) => {
          const referenceSection = sectionsFromReference[i] || { value: '' };
          const isModified = section.type === activeSectionType;
          return { ...section, modified: isModified };
        });

        mergedDate = fieldValueManager.mergeDateIntoReferenceDate(
          utils,
          parsedDate,
          modifiedSections,
          currentReferenceDate,
          true,
          fieldValueManager.reliableSectionModificationOrder,
        );
      } else {
        // No active section, use standard merging approach
        mergedDate = mergeParsedDateWithReference(utils, parsedDate, currentReferenceDate, formatSectionTypes);
      }

      // Validate and update state
      if (validate(mergedDate)) {
        setDate(mergedDate);
        // Use the formatted date for a complete valid date
        setInputValue(utils.formatByString(mergedDate, format));
        setReferenceDate(mergedDate);
        onChange?.(mergedDate);
        return true;
      }
    } catch (error) {
      setError('Error processing date');
    }

    return false;
  };

  // Update active section type
  const setActiveSection = (sectionType: FieldSectionType | null) => {
    setActiveSectionType(sectionType);
    if (sectionType === null) {
      setCharacterQuery(null);
    }
  };

  // Handle input change with focus on active section
  const handleInputChange = (event: ChangeEvent<HTMLInputElement>, activeSectionIndex?: number) => {
    const newInputValue = event.target.value;
    setInputValue(newInputValue);

    if (!newInputValue) {
      setDate(null);
      onChange?.(null);
      return;
    }

    // Extract either paste data or current input value
    const eventData = (event.nativeEvent as any)?.data;
    const shouldUseEventData = eventData && eventData.length > 1;
    const valueToUse = shouldUseEventData ? eventData : newInputValue;

    // If we know the active section, use it for better input handling
    if (activeSectionIndex !== undefined && activeSectionIndex >= 0) {
      const formatSections = parseFormatIntoSections(format);
      if (formatSections.length > activeSectionIndex) {
        const activeSection = formatSections[activeSectionIndex];
        setActiveSection(activeSection.type as FieldSectionType);
      }
    }

    updateValueFromValueStr(valueToUse);
  };

  // Handle clear
  const handleClear = () => {
    setDate(null);
    setInputValue('');
    setError(null);
    setCharacterQuery(null);
    onChange?.(null);
  };

  return {
    date,
    inputValue,
    error,
    handleInputChange,
    handleClear,
    updateValueFromValueStr,
    setActiveSection,
    isValid: !error,
    characterQuery: characterQuery?.value || null,
  };
}
