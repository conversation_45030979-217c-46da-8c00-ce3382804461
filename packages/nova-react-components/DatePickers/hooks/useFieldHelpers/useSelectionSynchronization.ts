import { useCallback, useEffect } from 'react';
import { DateFieldDOMGetters } from './useDateFieldDOMGetters';
import { FieldSectionData } from '../../models/dateSection';
import { useFieldSelection } from './useFieldSelection';

/**
 * Hook to handle synchronization of DOM selection state with field sections
 */
export const useSelectionSynchronization = (
  domGetters: DateFieldDOMGetters,
  sections: FieldSectionData[],
  activeSection: number | null,
  setActiveSection: (sectionIndex: number | null) => void,
) => {
  // Use the field selection hook to get consistent functions
  const { findActiveSectionByPosition, getSectionFromSelection, getSelectionForSection } = useFieldSelection();

  // Sync selection from DOM to state
  const synchronizeSelectionFromDOM = useCallback(() => {
    const inputEl = domGetters.getInput();
    if (!inputEl) {
      return;
    }

    const selectionStart = inputEl.selectionStart;
    const selectionEnd = inputEl.selectionEnd;

    if (selectionStart === null) {
      return;
    }

    // Use the field selection hook to get section from DOM selection
    const sectionIndex = getSectionFromSelection(sections, selectionStart, selectionEnd);

    if (sectionIndex !== null && sectionIndex !== activeSection) {
      setActiveSection(sectionIndex);
    }
  }, [sections, activeSection, setActiveSection, domGetters, getSectionFromSelection]);

  // Sync selection from state to DOM
  const synchronizeSelectionToDOM = useCallback(() => {
    if (activeSection === null) {
      return;
    }

    const inputEl = domGetters.getInput();
    if (!inputEl) {
      return;
    }

    if (activeSection >= sections.length) {
      return;
    }

    // Use the field selection hook to get DOM selection range for section
    const selectionRange = getSelectionForSection(sections, activeSection);

    try {
      inputEl.setSelectionRange(selectionRange.start, selectionRange.end);
    } catch (e) {
      console.error('Error setting selection range:', e);
    }
  }, [activeSection, sections, domGetters, getSelectionForSection]);

  return {
    synchronizeSelectionFromDOM,
    synchronizeSelectionToDOM,
  };
};
