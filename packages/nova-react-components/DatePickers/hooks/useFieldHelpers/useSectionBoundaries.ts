import { useCallback, useMemo } from 'react';
import { useUtils } from '../useUtils';
import { FieldSectionType } from '../../models/dateSection';
import { PickerDateType } from '../../models/pickers';

/**
 * Enhanced hook for calculating section boundaries with better edge case handling
 * Centralized implementation that replaces duplicate logic in other files
 */
export function useSectionBoundaries() {
  const utils = useUtils();

  /**
   * Get current locale data for various section calculations
   */
  const localeData = useMemo(() => {
    const now = utils.date();
    const firstDayOfYear = utils.startOfYear(now);
    const endOfFebruary = utils.endOfMonth(utils.setMonth(firstDayOfYear, 1));

    return {
      firstDayOfYear,
      lastDayOfMonth: endOfFebruary,
      fullDayFormat: utils.formats.fullDate || 'MMMM D, YYYY', // Adjust based on your adapter's format
      shortMonthFormat: utils.formats.month || 'MMM',
      longMonthFormat: 'MMMM',
      shortWeekdayFormat: 'ddd',
      longWeekdayFormat: 'dddd',
      use24HourFormat: utils.is12HourCycleInCurrentLocale ? !utils.is12HourCycleInCurrentLocale() : true,
    };
  }, [utils]);

  /**
   * Detect if a given format uses leading zeros
   */
  const detectLeadingZeros = useCallback(
    (sectionType: FieldSectionType, format: string): boolean => {
      const now = utils.date();

      switch (sectionType) {
        case 'day': {
          // Check if day 1 is rendered as '01'
          const firstDayOfMonth = utils.startOfMonth(now);
          return utils.formatByString(firstDayOfMonth, format).length > 1;
        }

        case 'month': {
          // Check if month 1 (January) is rendered as '01'
          const january = utils.setMonth(now, 0);
          return (
            utils.formatByString(january, format).length > 1 && /^[0-9]+$/.test(utils.formatByString(january, format))
          );
        }

        case 'year': {
          // Check if year 1 is rendered as '0001'
          // This is an approximation since we can't easily set year to 1
          const formatted = utils.formatByString(now, format);
          // If it's a numeric format and has 2+ digits
          return /^[0-9]+$/.test(formatted) && formatted.length >= 2;
        }

        case 'hours': {
          // Check if hour 1 is rendered as '01'
          const oneAM = utils.setHours(now, 1);
          return utils.formatByString(oneAM, format).length > 1;
        }

        case 'minutes':
        case 'seconds': {
          // Check if minute/second 1 is rendered as '01'
          const time = sectionType === 'minutes' ? utils.setMinutes(now, 1) : utils.setSeconds(now, 1);
          return utils.formatByString(time, format).length > 1;
        }

        default:
          return false;
      }
    },
    [utils],
  );

  /**
   * Calculate minimum and maximum values for a given section type
   * Enhanced with edge case handling
   * Centralized implementation that replaces duplicate logic in other files
   */
  const getSectionBoundaries = useCallback(
    (
      sectionType: FieldSectionType,
      format: string,
      referenceDate: PickerDateType | null,
    ): { minimum: number; maximum: number } => {
      // Default values
      let minimum = 0;
      let maximum = 0;

      switch (sectionType) {
        case 'year':
          // Handles edge cases for 2-digit years vs 4-digit years
          minimum = format.toLowerCase() === 'yy' ? 0 : 0;
          maximum = format.toLowerCase() === 'yy' ? 99 : 9999;
          break;

        case 'month':
          minimum = 1;
          maximum = 12;
          break;

        case 'day': {
          minimum = 1;

          if (referenceDate && utils.isValid(referenceDate)) {
            // If we have a reference date, calculate actual days in month
            maximum = utils.getDaysInMonth(referenceDate);
          } else {
            // Otherwise use 31 as a fallback (will be validated later)
            maximum = 31;
          }
          break;
        }

        case 'weekDay': {
          // Different locales can have different first day of week
          // This is a simplification, as MUI has more sophisticated handling
          minimum = 0;
          maximum = 6;
          break;
        }

        case 'hours': {
          // Check if locale uses 12-hour or 24-hour format
          if (!localeData.use24HourFormat || (/h/i.test(format) && !/H/i.test(format))) {
            // 12-hour format (1-12)
            minimum = 1;
            maximum = 12;
          } else {
            // 24-hour format (0-23)
            minimum = 0;
            maximum = 23;
          }
          break;
        }

        case 'minutes':
        case 'seconds':
          minimum = 0;
          maximum = 59;
          break;

        case 'meridiem':
          // Binary AM/PM (0 = AM, 1 = PM)
          minimum = 0;
          maximum = 1;
          break;

        default:
          minimum = 0;
          maximum = 0;
          break;
      }

      return { minimum, maximum };
    },
    [utils, localeData],
  );

  /**
   * Generates format examples for section placeholders
   */
  const getSectionPlaceholder = useCallback(
    (sectionType: FieldSectionType, format: string): string => {
      switch (sectionType) {
        case 'year':
          return format.length <= 2 ? 'YY' : 'YYYY';

        case 'month':
          return /MMM/i.test(format) ? 'MMM' : 'MM';

        case 'day':
          return 'DD';

        case 'weekDay':
          return /ddd/i.test(format) ? 'DDD' : 'D';

        case 'hours':
          return localeData.use24HourFormat ? 'HH' : 'hh';

        case 'minutes':
          return 'mm';

        case 'seconds':
          return 'ss';

        case 'meridiem':
          return 'AM/PM';

        default:
          return '';
      }
    },
    [localeData],
  );

  /**
   * Format a section value with respect to leading zeros and localization
   */
  const formatSectionValue = useCallback(
    (
      sectionType: FieldSectionType,
      value: number | string,
      format: string,
      options: {
        addLeadingZeros?: boolean;
        useLocale?: boolean;
      } = {},
    ): string => {
      const { addLeadingZeros = true, useLocale = true } = options;

      // Determine if the format uses leading zeros
      const hasLeadingZeros = addLeadingZeros && detectLeadingZeros(sectionType, format);

      // Handle numeric section types
      if (['year', 'month', 'day', 'hours', 'minutes', 'seconds'].includes(sectionType)) {
        let numValue = typeof value === 'string' ? parseInt(value, 10) : value;

        // Guard against NaN
        if (isNaN(numValue)) {
          numValue = 0;
        }

        // Get boundaries for validation
        const { minimum, maximum } = getSectionBoundaries(sectionType, format, null);

        // Ensure value is within boundaries
        numValue = Math.max(minimum, Math.min(numValue, maximum));

        // Format with leading zeros if needed
        let stringValue = String(numValue);

        if (hasLeadingZeros) {
          // Add appropriate leading zeros
          if (sectionType === 'year' && format.length === 4) {
            stringValue = stringValue.padStart(4, '0');
          } else if (['month', 'day', 'hours', 'minutes', 'seconds'].includes(sectionType)) {
            stringValue = stringValue.padStart(2, '0');
          }
        }

        return stringValue;
      }

      // Handle special section types
      if (sectionType === 'meridiem') {
        if (typeof value === 'number') {
          return value === 0 ? 'AM' : 'PM';
        }
        const upperValue = String(value).toUpperCase();
        return upperValue === 'AM' || upperValue === 'PM' ? upperValue : 'AM';
      }

      // Fallback for other types
      return String(value);
    },
    [detectLeadingZeros, getSectionBoundaries],
  );

  /**
   * Find the section at a specific character position
   * Important for accurate selection, especially for year sections
   */
  const findSectionAtPosition = useCallback(
    (sections: Array<any>, position: number): { section: any; sectionIndex: number } | null => {
      // First check if the position is inside any section's content
      for (let i = 0; i < sections.length; i++) {
        const section = sections[i];

        // Skip sections without position data
        if (!('startInInput' in section) || !('endInInput' in section)) {
          continue;
        }

        // Check if position is within the section's content area
        if (position >= section.startInInput && position <= section.endInInput) {
          return { section, sectionIndex: i };
        }
      }

      // If not found in content areas, check if it's in a separator area
      // and in that case, return the section BEFORE the separator
      for (let i = 0; i < sections.length - 1; i++) {
        const currentSection = sections[i];
        const nextSection = sections[i + 1];

        // Check if we have separator info or can infer it
        if (
          'separatorStart' in currentSection &&
          'separatorEnd' in currentSection &&
          currentSection.separatorStart !== undefined
        ) {
          // Use explicit separator boundaries
          if (position >= currentSection.separatorStart && position <= currentSection.separatorEnd) {
            return { section: currentSection, sectionIndex: i };
          }
        } else if ('endInInput' in currentSection && 'startInInput' in nextSection) {
          // Infer separator position is between sections
          if (position > currentSection.endInInput && position < nextSection.startInInput) {
            return { section: currentSection, sectionIndex: i };
          }
        }
      }

      // If position is after all sections, return the last section
      if (sections.length > 0 && position > sections[sections.length - 1].endInInput) {
        return {
          section: sections[sections.length - 1],
          sectionIndex: sections.length - 1,
        };
      }

      // If position is before all sections, return the first section
      if (sections.length > 0 && position < sections[0].startInInput) {
        return {
          section: sections[0],
          sectionIndex: 0,
        };
      }

      return null;
    },
    [],
  );

  /**
   * Get the selection range for a section, ensuring proper boundaries
   */
  const getSectionSelection = useCallback((section: any) => {
    // Ensure we don't include separators in the selection
    if ('startInInput' in section && 'endInInput' in section) {
      return {
        startPosition: section.startInInput,
        endPosition: section.endInInput + 1, // Add 1 to include the last character
      };
    }

    // Fallback
    return {
      startPosition: 0,
      endPosition: 0,
    };
  }, []);

  return {
    getSectionBoundaries,
    getSectionPlaceholder,
    formatSectionValue,
    detectLeadingZeros,
    localeData,
    findSectionAtPosition,
    getSectionSelection,
  };
}

export default useSectionBoundaries;
