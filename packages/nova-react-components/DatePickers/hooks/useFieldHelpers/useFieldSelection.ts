import { useCallback } from 'react';
import { useSectionBoundaries } from './useSectionBoundaries';
import { FieldSectionData } from '../../models/dateSection';

/**
 * Hook to handle synchronization of DOM selection state with field sections
 * Improved to handle year sections properly
 */
export const useFieldSelection = () => {
  const { findSectionAtPosition, getSectionSelection } = useSectionBoundaries();

  /**
   * Find active section by click position or selection
   */
  const findActiveSectionByPosition = useCallback(
    (sections: FieldSectionData[], clickPosition: number) => {
      if (!sections || sections.length === 0) {
        return null;
      }

      // Use the specialized function to find the section at this position
      const result = findSectionAtPosition(sections, clickPosition);
      return result ? result.sectionIndex : null;
    },
    [findSectionAtPosition],
  );

  /**
   * Get current section from selection
   */
  const getSectionFromSelection = useCallback(
    (sections: FieldSectionData[], selStart: number, selEnd: number | null) => {
      if (!sections || sections.length === 0) {
        return null;
      }

      // If single caret position, find section at that position
      if (selEnd === null || selStart === selEnd) {
        return findActiveSectionByPosition(sections, selStart);
      }

      // If selection range, try to match it to a section's boundaries
      // This helps identify year section selections correctly
      for (let i = 0; i < sections.length; i++) {
        const section = sections[i];
        const sectionRange = getSectionSelection(section);

        // Check if selection matches this section's range
        if (selStart === sectionRange.startPosition && selEnd === sectionRange.endPosition) {
          return i;
        }
      }

      // If no exact match, use the start position to find the section
      return findActiveSectionByPosition(sections, selStart);
    },
    [findActiveSectionByPosition, getSectionSelection],
  );

  /**
   * Get selection range for a section
   */
  const getSelectionForSection = useCallback(
    (sections: FieldSectionData[], sectionIndex: number) => {
      if (!sections || sectionIndex === null || sectionIndex < 0 || sectionIndex >= sections.length) {
        return { start: 0, end: 0 };
      }

      const section = sections[sectionIndex];
      const range = getSectionSelection(section);

      return { start: range.startPosition, end: range.endPosition };
    },
    [getSectionSelection],
  );

  return {
    findActiveSectionByPosition,
    getSectionFromSelection,
    getSelectionForSection,
  };
};

export default useFieldSelection;
