import { useCallback } from 'react';
import { useUtils } from '../useUtils';
import { FieldSectionData, FieldSectionType } from '../../models/dateSection';
import { extractSectionsFromValueString, cleanString } from '../../utils/sectionUtils';

interface UseClipboardPasteProps {
  activeSection: number | null;
  sections: FieldSectionData[];
  format: string;
  onChange: (value: string) => void;
  setCharacterQuery: (query: any | null) => void;
  updateSectionValue: (sectionIndex: number, value: string, moveToNextSection: boolean) => void;
  handleSectionPaste?: (text: string) => boolean;
}

/**
 * Enhanced hook for clipboard paste handling, based on MUI's approach but
 * adapted for our implementation with better pattern detection
 */
export function useClipboardPaste({
  activeSection,
  sections,
  format,
  onChange,
  setCharacterQuery,
  updateSectionValue,
  handleSectionPaste,
}: UseClipboardPasteProps) {
  const utils = useUtils();

  /**
   * Determine if pasted text is valid for a specific section type and content type
   */
  const isValidForSection = useCallback((section: FieldSectionData, pastedText: string): boolean => {
    const cleanedText = cleanString(pastedText);
    const sectionType = section.type;
    const contentType = section.contentType;

    // Pattern detection similar to MUI's approach
    const lettersOnly = /^[a-zA-Z]+$/.test(cleanedText);
    const digitsOnly = /^[0-9]+$/.test(cleanedText);
    const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(cleanedText);

    // Check if the pasted value matches the section's expected content type
    if (contentType === 'letter' && lettersOnly) {
      return true;
    }

    if (contentType === 'digit' && digitsOnly) {
      return true;
    }

    if (contentType === 'digit-with-letter' && digitsAndLetterOnly) {
      return true;
    }

    // Special case for meridiem (AM/PM)
    if (sectionType === 'meridiem') {
      const lowerText = cleanedText.toLowerCase();
      return lowerText === 'am' || lowerText === 'pm' || lowerText.startsWith('a') || lowerText.startsWith('p');
    }

    return false;
  }, []);

  /**
   * Try to parse a complete date string from pasted text
   */
  const tryParseFullDate = useCallback(
    (pastedText: string): boolean => {
      if (!pastedText.trim()) {
        return false;
      }

      try {
        // Try to parse using date adapter first
        const parsedDate = utils.parse(pastedText, format);

        if (parsedDate && utils.isValid(parsedDate)) {
          // If it's a valid date in the current format, update with the formatted value
          const formattedValue = utils.formatByString(parsedDate, format);
          onChange(formattedValue);
          return true;
        }

        // Try alternate date formats with different separators
        const commonFormats = ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD', 'MM-DD-YYYY'];
        for (const altFormat of commonFormats) {
          const altParsed = utils.parse(pastedText, altFormat);
          if (altParsed && utils.isValid(altParsed)) {
            const formattedValue = utils.formatByString(altParsed, format);
            onChange(formattedValue);
            return true;
          }
        }

        // Try to extract sections from the string using regex patterns
        const extractedSections = extractSectionsFromValueString(pastedText, format, utils);

        // Check if we have valid sections to work with
        const hasValidSections = Object.values(extractedSections).some((val) => val !== null);

        if (hasValidSections) {
          // Create a date from the extracted sections
          const now = utils.date();
          let result = now;

          if (extractedSections.year !== null) {
            result = utils.setYear(result, parseInt(extractedSections.year, 10));
          }

          if (extractedSections.month !== null) {
            result = utils.setMonth(result, parseInt(extractedSections.month, 10) - 1); // Subtract 1 for 0-based months
          }

          if (extractedSections.day !== null) {
            result = utils.setDate(result, parseInt(extractedSections.day, 10));
          }

          if (extractedSections.hours !== null) {
            let hours = parseInt(extractedSections.hours, 10);

            // Handle meridiem (AM/PM) if present
            if (extractedSections.meridiem !== null) {
              if (extractedSections.meridiem === 'PM' && hours < 12) {
                hours += 12;
              } else if (extractedSections.meridiem === 'AM' && hours === 12) {
                hours = 0;
              }
            }

            result = utils.setHours(result, hours);
          }

          if (extractedSections.minutes !== null) {
            result = utils.setMinutes(result, parseInt(extractedSections.minutes, 10));
          }

          if (extractedSections.seconds !== null) {
            result = utils.setSeconds(result, parseInt(extractedSections.seconds, 10));
          }

          if (utils.isValid(result)) {
            const formattedValue = utils.formatByString(result, format);
            onChange(formattedValue);
            return true;
          }
        }

        // If we can't parse the text as a date or extract sections,
        // treat it as a direct value for the active section if applicable
        if (activeSection !== null && activeSection < sections.length) {
          return false; // Will be handled by section-specific paste
        }

        return false;
      } catch (error) {
        console.error('Error parsing pasted date:', error);
        return false;
      }
    },
    [utils, format, onChange, activeSection, sections.length],
  );

  /**
   * Main handler for clipboard paste events
   */
  const handleClipboardPaste = useCallback(
    (pastedText: string): boolean => {
      // Always clear character query when pasting
      setCharacterQuery(null);

      const cleanedText = cleanString(pastedText);

      if (!cleanedText) {
        return false;
      }

      // First, try to handle as a section-specific paste if we have an active section
      if (activeSection !== null && activeSection < sections.length) {
        const section = sections[activeSection];

        if (isValidForSection(section, cleanedText)) {
          // If we have a custom paste handler, use it first
          if (handleSectionPaste && handleSectionPaste(cleanedText)) {
            return true;
          }

          // Otherwise, handle based on section type
          if (section.type === 'meridiem') {
            const lowerText = cleanedText.toLowerCase();
            if (lowerText === 'am' || lowerText.startsWith('a')) {
              updateSectionValue(activeSection, 'AM', true);
              return true;
            } else if (lowerText === 'pm' || lowerText.startsWith('p')) {
              updateSectionValue(activeSection, 'PM', true);
              return true;
            }
          } else if (section.contentType === 'digit' || section.contentType === 'digit-with-letter') {
            if (/^\d+$/.test(cleanedText)) {
              updateSectionValue(activeSection, cleanedText, true);
              return true;
            }
          } else if (section.contentType === 'letter') {
            updateSectionValue(activeSection, cleanedText, true);
            return true;
          }
        }
      }

      // If we couldn't handle it as a section-specific paste,
      // try to parse it as a complete date
      return tryParseFullDate(cleanedText);
    },
    [
      activeSection,
      sections,
      setCharacterQuery,
      isValidForSection,
      updateSectionValue,
      tryParseFullDate,
      handleSectionPaste,
    ],
  );

  return {
    handleClipboardPaste,
  };
}

export default useClipboardPaste;
