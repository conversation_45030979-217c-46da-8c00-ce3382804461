import * as React from 'react';
import { unstable_useEventCallback as useEventCallback } from '@mui/utils';
import { useUtils } from './useUtils';

export interface UseDateValidationParams {
  /**
   * Minimum selectable date.
   */
  minDate?: any;

  /**
   * Maximum selectable date.
   */
  maxDate?: any;

  /**
   * If `true`, disable dates after the current date.
   */
  disableFuture?: boolean;

  /**
   * If `true`, disable dates before the current date.
   */
  disablePast?: boolean;

  /**
   * Function that returns a boolean if the given date should be disabled.
   */
  shouldDisableDate?: (date: any) => boolean;

  /**
   * Format string for date formatting.
   */
  format?: string;
}

export interface UseDateValidationResult {
  /**
   * Check if a date is valid according to all constraints.
   */
  validateDate: (date: any) => boolean;
}

/**
 * Hook to handle date validation logic.
 *
 * @param params Validation parameters
 * @returns Validation functions
 */
export function useDateValidation(params: UseDateValidationParams): UseDateValidationResult {
  const { minDate, maxDate, disableFuture = false, disablePast = false, shouldDisableDate, format } = params;

  // Get date utility functions
  const utils = useUtils();

  // Get the current date for checking future/past restrictions
  const now = utils.date();

  // Function to check if a date is valid according to all constraints
  const validateDate = useEventCallback((date: any) => {
    // Skip validation for null/undefined dates
    if (!date) {
      return true;
    }

    // Parse date if it's a string
    const dateToCheck = typeof date === 'string' ? utils.parse(date, format) : date;

    // Ensure the date is valid
    if (!utils.isValid(dateToCheck)) {
      return false;
    }

    // Check min date constraint
    if (minDate && utils.isBefore(dateToCheck, minDate)) {
      return false;
    }

    // Check max date constraint
    if (maxDate && utils.isAfter(dateToCheck, maxDate)) {
      return false;
    }

    // Check disable future constraint
    if (disableFuture && utils.isAfter(dateToCheck, now)) {
      return false;
    }

    // Check disable past constraint
    if (disablePast && utils.isBefore(dateToCheck, now)) {
      return false;
    }

    // Check custom disable function
    if (shouldDisableDate && shouldDisableDate(dateToCheck)) {
      return false;
    }

    return true;
  });

  return {
    validateDate,
  };
}
