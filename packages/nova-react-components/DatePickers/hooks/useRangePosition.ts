import * as React from 'react';
import { unstable_useEventCallback as useEventCallback } from '@mui/utils';
import { RangePosition } from '../utils/dateRangeUtils';
import { useControlledValue } from './useControlledValue';

export interface UseRangePositionParams {
  /**
   * Current position in the range (start or end).
   */
  rangePosition?: RangePosition;

  /**
   * Default position in the range.
   * @default 'start'
   */
  defaultRangePosition?: RangePosition;

  /**
   * Callback fired when range position changes.
   */
  onRangePositionChange?: (position: RangePosition) => void;
}

export interface UseRangePositionResult {
  /**
   * Current range position (start or end).
   */
  rangePosition: RangePosition;

  /**
   * Set the current range position.
   */
  setRangePosition: (position: RangePosition) => void;
}

/**
 * Hook to manage the range position state (start or end).
 *
 * @param params Range position params
 * @returns Range position state and setter
 */
export function useRangePosition(params: UseRangePositionParams): UseRangePositionResult {
  const { rangePosition: rangePositionProp, defaultRangePosition = 'start', onRangePositionChange } = params;

  // Use controlled value hook for range position
  const [rangePosition, setRangePositionState] = useControlledValue<RangePosition>(
    rangePositionProp,
    defaultRangePosition,
  );

  // Create event callback for position changes
  const setRangePosition = useEventCallback((newPosition: RangePosition) => {
    setRangePositionState(newPosition);
    onRangePositionChange?.(newPosition);
  });

  return {
    rangePosition,
    setRangePosition,
  };
}
