'use client';
import * as React from 'react';

/**
 * Hook for managing controlled or uncontrolled state.
 *
 * @param controlledValue Value from props (controlled mode if defined)
 * @param defaultValue Default value for uncontrolled mode
 * @returns [currentValue, setValue] tuple
 */
export function useControlledValue<T>(
  controlledValue: T | undefined,
  defaultValue: T | undefined,
): [T | undefined, (newValue: T | undefined) => void] {
  // Track if the component is controlled
  const isControlled = controlledValue !== undefined;

  // Store the controlled flag in a ref to avoid changing behavior during component lifetime
  const isControlledRef = React.useRef(isControlled);

  // Internal state for uncontrolled mode
  const [valueState, setValueState] = React.useState<T | undefined>(defaultValue);

  // Check for control mode changes (development mode warning)
  React.useEffect(() => {
    if (process.env.NODE_ENV !== 'production') {
      if (isControlledRef.current !== isControlled) {
        console.error(
          `A component is changing from ${
            isControlledRef.current ? 'controlled' : 'uncontrolled'
          } to ${isControlled ? 'controlled' : 'uncontrolled'}.`,
          'Components should not switch from controlled to uncontrolled (or vice versa).',
          'Decide between using a controlled or uncontrolled component for the lifetime of the component.',
        );
      }
    }
  }, [isControlled]);

  // Use controlled value or internal state
  const value = isControlled ? controlledValue : valueState;

  // Update function that respects controlled/uncontrolled mode
  const setValue = React.useCallback((newValue: T | undefined) => {
    if (!isControlledRef.current) {
      setValueState(newValue);
    }
  }, []);

  return [value, setValue];
}
