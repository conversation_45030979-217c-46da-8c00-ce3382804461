'use client';
import { useCallback } from 'react';
import { useViews } from './useViews';
import { DatePickerView } from '../types';

export interface UseCalendarContentParams {
  views?: DatePickerView[];
  onViewChange?: (view: DatePickerView) => void;
  openTo?: DatePickerView;
}

export const useCalendarContent = (params: UseCalendarContentParams) => {
  const { views = ['day', 'month', 'year'], onViewChange, openTo = 'day' } = params;

  const {
    view: currentView,
    setView: setCurrentView,
    views: availableViews,
  } = useViews({
    openTo,
    views,
    onViewChange,
  });

  const handleViewChange = useCallback(
    (view: DatePickerView) => {
      setCurrentView(view);
    },
    [setCurrentView],
  );

  return {
    currentView,
    availableViews,
    handleViewChange,
    setCurrentView,
  };
};
