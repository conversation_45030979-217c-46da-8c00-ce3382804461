import { OverridableStringUnion, OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { SxProps } from '../../types/theme';
import { PickerDateType } from '../models/pickers';

export type DatePickerView = 'day' | 'month' | 'year';

export type CalendarHeaderSlot = 'root' | 'monthYearSection' | 'selectorButton';

export interface CalendarHeaderSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the month/year section.
   * @default 'div'
   */
  monthYearSection?: React.ElementType;
  /**
   * The component that renders the selector button.
   * @default 'button'
   */
  selectorButton?: React.ElementType;
}

export interface CalendarHeaderVariantOverrides {}

export type CalendarHeaderSlotsAndSlotProps = CreateSlotsAndSlotProps<
  CalendarHeaderSlots,
  {
    root: SlotProps<'div', object, CalendarHeaderOwnerState>;
    monthYearSection: SlotProps<'div', object, CalendarHeaderOwnerState>;
    selectorButton: SlotProps<'button', object, CalendarHeaderOwnerState>;
  }
>;

export interface CalendarHeaderTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    CalendarHeaderSlotsAndSlotProps & {
      /**
       * The current date displayed in the header.
       */
      date: PickerDateType;

      /**
       * The current view date displayed in the header.
       */
      viewDate: PickerDateType;
      /**
       * The current view of the picker.
       */
      view: DatePickerView;
      /**
       * The variant of the header.
       */
      variant: OverridableStringUnion<'docked' | 'modal', CalendarHeaderVariantOverrides>;
      /**
       * Callback fired when going to the previous month.
       */
      onPreviousMonth: (date: PickerDateType) => void;
      /**
       * Callback fired when going to the next month.
       */
      onNextMonth: (date: PickerDateType) => void;
      /**
       * Callback fired when going to the previous year.
       */
      onPreviousYear: (date: PickerDateType) => void;
      /**
       * Callback fired when going to the next year.
       */
      onNextYear: (date: PickerDateType) => void;
      /**
       * Callback fired when changing the view.
       */
      onViewChange: (view: DatePickerView) => void;
      /**
       * If `true`, the component is disabled.
       * @default false
       */
      disabled?: boolean;
      /**
       * If `true`, the component is read-only.
       * @default false
       */
      readOnly?: boolean;
      /**
       * If `true`, disable values before the current date for date components, time for time components and both for date time components.
       * @default false
       */
      disablePast?: boolean;
      /**
       * If `true`, disable values after the current date for date components, time for time components and both for date time components.
       * @default false
       */
      disableFuture?: boolean;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type CalendarHeaderProps<
  D extends React.ElementType = CalendarHeaderTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<CalendarHeaderTypeMap<P, D>, D>;

export interface CalendarHeaderOwnerState extends CalendarHeaderProps {}
