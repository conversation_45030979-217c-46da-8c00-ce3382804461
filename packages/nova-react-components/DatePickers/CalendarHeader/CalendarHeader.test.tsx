import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import dayjs from 'dayjs/esm';
import { CalendarHeader } from './CalendarHeader';
import { PickerProvider } from '../PickerContext';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<CalendarHeader />', () => {
  const defaultProps = {
    date: dayjs('2023-05-15'),
    viewDate: dayjs('2023-05-15'),
    view: 'day' as const,
    variant: 'docked' as const,
    onPreviousMonth: vi.fn(),
    onNextMonth: vi.fn(),
    onPreviousYear: vi.fn(),
    onNextYear: vi.fn(),
    onViewChange: vi.fn(),
  };

  describe('variants', () => {
    it('should render docked variant', () => {
      const { container } = render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} data-testid="header" />
        </PickerProvider>,
      );
      expect(container.firstChild).toBeInTheDocument();

      // Should have two month/year sections in docked mode
      const sections = container.querySelectorAll('div[class*="monthYearSection"]');
      expect(sections.length).toBe(2);
    });

    it('should render modal variant', () => {
      const { container } = render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} variant="modal" data-testid="header" />
        </PickerProvider>,
      );
      expect(container.firstChild).toBeInTheDocument();

      // Should have only one year/month section visible in modal view
      const sections = container.querySelectorAll('div[class*="monthYearSection"]');
      expect(sections.length).toBe(2);

      // In modal view, we combine month and year in one button
      expect(container.textContent).toContain('May 2023');
    });
  });

  describe('navigation buttons', () => {
    it('should call onPreviousMonth when previous month button is clicked', () => {
      render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} />
        </PickerProvider>,
      );
      const prevMonthButton = screen.getAllByLabelText('Previous month')[0];
      fireEvent.click(prevMonthButton);
      expect(defaultProps.onPreviousMonth).toHaveBeenCalledTimes(1);
    });

    it('should call onNextMonth when next month button is clicked', () => {
      render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} />
        </PickerProvider>,
      );
      const nextMonthButton = screen.getAllByLabelText('Next month')[0];
      fireEvent.click(nextMonthButton);
      expect(defaultProps.onNextMonth).toHaveBeenCalledTimes(1);
    });

    it('should call onPreviousYear when previous year button is clicked', () => {
      render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} />
        </PickerProvider>,
      );
      const prevYearButton = screen.getByLabelText('Previous year');
      fireEvent.click(prevYearButton);
      expect(defaultProps.onPreviousYear).toHaveBeenCalledTimes(1);
    });

    it('should call onNextYear when next year button is clicked', () => {
      render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} />
        </PickerProvider>,
      );
      const nextYearButton = screen.getByLabelText('Next year');
      fireEvent.click(nextYearButton);
      expect(defaultProps.onNextYear).toHaveBeenCalledTimes(1);
    });
  });

  describe('view selection', () => {
    it('should call onViewChange when month selector is clicked', () => {
      render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} />
        </PickerProvider>,
      );
      // Find button with "May" text
      const monthSelector = screen.getByText('May').closest('button');
      fireEvent.click(monthSelector as HTMLElement);
      expect(defaultProps.onViewChange).toHaveBeenCalledWith('month');
    });

    it('should call onViewChange when year selector is clicked', () => {
      render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} />
        </PickerProvider>,
      );
      // Find button with the year
      const yearSelector = screen.getByText('2023').closest('button');
      fireEvent.click(yearSelector as HTMLElement);
      expect(defaultProps.onViewChange).toHaveBeenCalledWith('year');
    });

    it('should handle different view states', () => {
      const { rerender } = render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} view="month" />
        </PickerProvider>,
      );

      // When in month view, clicking month selector should switch back to day view
      const monthSelector = screen.getByText('May').closest('button');
      fireEvent.click(monthSelector as HTMLElement);
      expect(defaultProps.onViewChange).toHaveBeenCalledWith('day');

      // When in year view, clicking year selector should switch back to day view
      rerender(
        <PickerProvider>
          <CalendarHeader {...defaultProps} view="year" />
        </PickerProvider>,
      );
      const yearSelector = screen.getByText('2023').closest('button');
      fireEvent.click(yearSelector as HTMLElement);
      expect(defaultProps.onViewChange).toHaveBeenCalledWith('day');
    });
  });

  describe('disabled state', () => {
    it('should disable navigation buttons when disabled', () => {
      render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} disabled />
        </PickerProvider>,
      );
      const buttons = screen.getAllByRole('button');
      buttons.forEach((button) => {
        expect(button).toBeDisabled();
      });
    });

    it('should disable navigation buttons when readOnly', () => {
      render(
        <PickerProvider>
          <CalendarHeader {...defaultProps} readOnly />
        </PickerProvider>,
      );
      const buttons = screen.getAllByRole('button');
      buttons.forEach((button) => {
        expect(button).toBeDisabled();
      });
    });
  });
});
