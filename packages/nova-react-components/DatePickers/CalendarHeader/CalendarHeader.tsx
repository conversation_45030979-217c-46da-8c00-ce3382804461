'use client';
import React, { useCallback, useMemo } from 'react';
import { OverridableComponent } from '@mui/types';
import {
  unstable_composeClasses as composeClasses,
  unstable_useForkRef as useForkRef,
  unstable_capitalize as capitalize,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { IconButton } from '../../IconButton';
import { ArrowLeftIcon, ArrowRightIcon, ArrowDropDownIcon } from '../icons';
import { Typography } from '../../Typography';
import { CalendarHeaderOwnerState, CalendarHeaderProps, CalendarHeaderTypeMap } from './CalendarHeader.types';
import { getCalendarHeaderUtilityClass } from './CalendarHeader.classes';
import { useNow, useUtils } from '../hooks';

const useUtilityClasses = (ownerState: CalendarHeaderOwnerState) => {
  const { variant, disabled } = ownerState;

  const slots = {
    root: ['root', variant && `variant${capitalize(variant)}`, disabled && 'disabled'],
    monthYearSection: ['monthYearSection'],
    selectorButton: ['selectorButton', ownerState.view === 'month' && 'expanded'],
  };

  return composeClasses(slots, getCalendarHeaderUtilityClass, {});
};

const HeaderContainer = styled('div')<{ variant: 'docked' | 'modal' }>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingBlock: '8px',
  variants: [
    {
      props: { variant: 'docked' },
      style: {
        paddingInline: '4px',
      },
    },
    {
      props: { variant: 'modal' },
      style: {
        paddingInlineStart: '24px',
        paddingInlineEnd: '8px',
      },
    },
  ],
}));

const MonthYearSection = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
}));

interface SelectorButtonProps {
  ownerState: CalendarHeaderOwnerState;
}

const SelectorButton = styled('button')<SelectorButtonProps>(({ theme }) => ({
  background: 'transparent',
  border: 'none',
  padding: '6px 4px',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
  cursor: 'pointer',
  borderRadius: '4px',
  ...theme.typography.labelMedium,
  color: theme.vars.palette.onSurfaceVariant,
  fontWeight: 400,

  '&:disabled': {
    opacity: 0.5,
    cursor: 'default',
  },

  '& svg': {
    width: '24px',
    height: '24px',
    color: 'inherit',
    transition: 'transform 0.2s ease',
    position: 'relative',
  },

  '&[aria-expanded="true"] svg': {
    transform: 'rotate(180deg)',
  },
}));

export const CalendarHeader = React.forwardRef(function CalendarHeader(
  props: CalendarHeaderProps,
  ref: React.ForwardedRef<Element>,
) {
  const {
    date,
    viewDate,
    view,
    variant,
    onPreviousMonth,
    onNextMonth,
    onPreviousYear,
    onNextYear,
    onViewChange,
    disabled = false,
    readOnly = false,
    disableFuture,
    disablePast,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const utils = useUtils();
  const today = useNow();
  const handleRef = useForkRef(ref, null);

  const ownerState = {
    ...props,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? HeaderContainer;
  const SlotMonthYearSection = slots.monthYearSection ?? MonthYearSection;
  const SlotSelectorButton = slots.selectorButton ?? SelectorButton;

  const rootProps = useSlotProps({
    elementType: HeaderContainer,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  const monthYearSectionProps = useSlotProps({
    elementType: MonthYearSection,
    externalSlotProps: slotProps.monthYearSection,
    ownerState,
    className: classes.monthYearSection,
  });

  const selectorButtonProps = useSlotProps({
    elementType: SelectorButton,
    externalSlotProps: slotProps.selectorButton,
    ownerState,
    className: classes.selectorButton,
  });

  // Create a helper function for determining disabled states
  const isNavigationDisabled = useCallback(
    (direction: 'previous' | 'next', unit: 'month' | 'year'): boolean => {
      if (disabled || readOnly) return true;

      const amount = direction === 'previous' ? -1 : 1;
      const addFn = unit === 'month' ? utils.addMonths : utils.addYears;
      const newDate = addFn(viewDate, amount);

      return direction === 'previous'
        ? disablePast && utils.isBefore(newDate, today)
        : disableFuture && utils.isAfter(newDate, today);
    },
    [disableFuture, disablePast, disabled, readOnly, today, utils, viewDate],
  );

  // Create navigation handlers with integrated disable state
  const createNavHandler = useCallback(
    (unit: 'month' | 'year', direction: 'previous' | 'next') => {
      const isDisabled = isNavigationDisabled(direction, unit);

      const handler = () => {
        const amount = direction === 'previous' ? -1 : 1;
        const addFn = unit === 'month' ? utils.addMonths : utils.addYears;
        const newDate = addFn(viewDate, amount);

        const callback =
          unit === 'month'
            ? direction === 'previous'
              ? onPreviousMonth
              : onNextMonth
            : direction === 'previous'
              ? onPreviousYear
              : onNextYear;

        callback(newDate);
      };

      return { handler, isDisabled };
    },
    [
      isNavigationDisabled,
      onNextMonth,
      onNextYear,
      onPreviousMonth,
      onPreviousYear,
      utils.addMonths,
      utils.addYears,
      viewDate,
    ],
  );

  const { handler: handlePreviousMonth, isDisabled: disablePreviousMonth } = useMemo(
    () => createNavHandler('month', 'previous'),
    [createNavHandler],
  );

  const { handler: handleNextMonth, isDisabled: disableNextMonth } = useMemo(
    () => createNavHandler('month', 'next'),
    [createNavHandler],
  );

  const { handler: handlePreviousYear, isDisabled: disablePreviousYear } = useMemo(
    () => createNavHandler('year', 'previous'),
    [createNavHandler],
  );

  const { handler: handleNextYear, isDisabled: disableNextYear } = useMemo(
    () => createNavHandler('year', 'next'),
    [createNavHandler],
  );

  // If we're in the docked variant, we have month and year selectors
  if (variant === 'docked') {
    const disableYear = view === 'month';
    const disableMonth = view === 'year';
    const showArrowButton = view === 'day';

    const handleMonthViewChange = () => {
      const targetView = view === 'month' ? 'day' : 'month';
      onViewChange(targetView);
    };

    const handleYearViewChange = () => {
      const targetView = view === 'year' ? 'day' : 'year';
      onViewChange(targetView);
    };

    return (
      <SlotRoot {...rootProps}>
        {/* Month section with navigation */}
        <SlotMonthYearSection {...monthYearSectionProps}>
          <IconButton
            onClick={handlePreviousMonth}
            disabled={disablePreviousMonth}
            style={{ visibility: showArrowButton ? 'visible' : 'hidden' }}
            variant="standard"
            size="medium"
            aria-label="Previous month"
          >
            <ArrowLeftIcon />
          </IconButton>

          <SlotSelectorButton
            {...selectorButtonProps}
            onClick={handleMonthViewChange}
            disabled={disabled || readOnly || disableMonth}
            aria-haspopup="listbox"
            aria-expanded={view === 'month'}
          >
            <Typography variant="inherit">{viewDate.format('MMM')}</Typography>
            {!disableMonth && <ArrowDropDownIcon />}
          </SlotSelectorButton>

          <IconButton
            onClick={handleNextMonth}
            style={{ visibility: showArrowButton ? 'visible' : 'hidden' }}
            disabled={disableNextMonth}
            variant="standard"
            size="medium"
            aria-label="Next month"
          >
            <ArrowRightIcon />
          </IconButton>
        </SlotMonthYearSection>

        {/* Year section with navigation */}
        <SlotMonthYearSection {...monthYearSectionProps}>
          <IconButton
            onClick={handlePreviousYear}
            disabled={disablePreviousYear}
            style={{ visibility: showArrowButton ? 'visible' : 'hidden' }}
            variant="standard"
            size="medium"
            aria-label="Previous year"
          >
            <ArrowLeftIcon />
          </IconButton>

          <SlotSelectorButton
            {...selectorButtonProps}
            onClick={handleYearViewChange}
            disabled={disabled || readOnly || disableYear}
            aria-haspopup="listbox"
            aria-expanded={view === 'year'}
          >
            <Typography variant="inherit">{viewDate.year()}</Typography>
            <ArrowDropDownIcon style={{ visibility: !disableYear ? 'visible' : 'hidden' }} />
          </SlotSelectorButton>

          <IconButton
            onClick={handleNextYear}
            disabled={disableNextYear}
            style={{ visibility: showArrowButton ? 'visible' : 'hidden' }}
            variant="standard"
            size="medium"
            aria-label="Next year"
          >
            <ArrowRightIcon />
          </IconButton>
        </SlotMonthYearSection>
      </SlotRoot>
    );
  }

  // Modal variant has a simpler header
  const handleViewChange = () => {
    const targetView = view === 'year' ? 'day' : 'year';
    onViewChange(targetView);
  };

  const calendarDate = !viewDate ? 'Select date' : viewDate.format('MMMM') + ' ' + viewDate.year();
  return (
    <SlotRoot {...rootProps}>
      <SlotMonthYearSection {...monthYearSectionProps}>
        <SlotSelectorButton
          {...selectorButtonProps}
          onClick={handleViewChange}
          disabled={disablePreviousMonth}
          aria-haspopup="listbox"
          aria-expanded={view === 'year'}
        >
          <Typography variant="inherit">{calendarDate}</Typography>
          <ArrowDropDownIcon />
        </SlotSelectorButton>
      </SlotMonthYearSection>

      {view === 'day' && (
        <SlotMonthYearSection {...monthYearSectionProps}>
          <IconButton
            onClick={handlePreviousMonth}
            disabled={disabled || readOnly}
            variant="standard"
            size="medium"
            aria-label="Previous month"
          >
            <ArrowLeftIcon fontSize="inherit" />
          </IconButton>

          <IconButton
            onClick={handleNextMonth}
            disabled={disableNextMonth}
            variant="standard"
            size="medium"
            aria-label="Next month"
          >
            <ArrowRightIcon fontSize="inherit" />
          </IconButton>
        </SlotMonthYearSection>
      )}
    </SlotRoot>
  );
}) as OverridableComponent<CalendarHeaderTypeMap>;
