import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import { DatePickerVariant, DatePickerView, ViewStyle } from '../types';
import { PickerDateType } from '../models/pickers';
import { FormProps } from '../models/formProps';
import { ExportedValidateDateProps } from '../models/validation';
import { SxProps } from '../../types/theme';
export interface ExportedDateCalendarProps extends ExportedValidateDateProps, FormProps {
  /**
   * Callback fired on year change.
   * @param {PickerDateType} year The new year.
   */
  onYearChange?: (year: PickerDateType) => void;
  /**
   * Callback fired on month change.
   * @param {PickerDateType} month The new month.
   */
  onMonthChange?: (month: PickerDateType) => void;
}

export interface DateCalendarComponentProps extends ExportedDateCalendarProps {
  /**
   * The currently selected date.
   */
  date: PickerDateType;

  /**
   * The month to display in the calendar.
   */
  viewDate: PickerDateType;

  /**
   * Callback for when a date is selected.
   */
  onDateChange: (date: PickerDateType) => void;

  /**
   * Callback for when the view date is change
   */
  onViewDateChange: (date: PickerDateType) => void;
  /**
   * The current view of the date picker.
   */
  view: DatePickerView;

  /**
   * Callback for when the view changes.
   */
  onViewChange: (view: DatePickerView) => void;

  /**
   * Available views.
   * @default ['day', 'month', 'year']
   */
  views?: DatePickerView[];

  /**
   * The variant of the date picker.
   * @default 'docked'
   */
  variant?: DatePickerVariant;

  /**
   * The style of the calendar view.
   * @default 'grid'
   */
  viewStyle?: ViewStyle;
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
}

export interface DateCalendarSlots {
  /**
   * The component used for the root element.
   * @default 'div'
   */
  root?: React.ElementType;
}

export interface DateCalendarSlotProps {
  root?: SlotComponentProps<'div', object, DateCalendarOwnerState>;
}

export interface DateCalendarTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    DateCalendarComponentProps & {
      /**
       * The slots for customizing the component appearance.
       */
      slots?: DateCalendarSlots;

      /**
       * The props used for each slot.
       */
      slotProps?: DateCalendarSlotProps;
    };
  defaultComponent: D;
}

export type DateCalendarProps<D extends React.ElementType = DateCalendarTypeMap['defaultComponent']> = OverrideProps<
  DateCalendarTypeMap<object, D>,
  D
> & {
  component?: D;
};

export type DateCalendarOwnerState = DateCalendarProps;
