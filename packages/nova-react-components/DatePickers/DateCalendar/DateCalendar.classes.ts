import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface DateCalendarClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the component when disabled. */
  disabled: string;
}

export type DateCalendarClassKey = keyof DateCalendarClasses;

export function getDateCalendarUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDateCalendar', slot);
}

const dateCalendarClasses: DateCalendarClasses = generateUtilityClasses('NovaDateCalendar', ['root', 'disabled']);

export default dateCalendarClasses;
