import * as React from 'react';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { usePickerContext } from '../../PickerContext';
import dockedDatePickerClasses from '../../DockedDatePicker/DockedDatePicker.classes';
import { PickerDateType } from '../../models/pickers';
import { useNow, useUtils } from '../../hooks/useUtils';
import { getMonthsInYear } from '../../utils/dateUtils';

import { MonthCalendarComponentProps } from '../MonthCalendar.types';

const ListContainer = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  width: '100%',
  borderRadius: '4px',
  overflow: 'auto',
  maxHeight: '300px',
}));

const MonthItem = styled('button')(({ theme }) => ({
  padding: '12px 16px',
  cursor: 'pointer',
  ...theme.typography.bodyLarge,
  color: theme.vars.palette.onSurface,
  border: 'none',
  backgroundColor: 'transparent',
  width: '100%',
  textAlign: 'left',
  '&:hover:not(:disabled)': {
    color: theme.vars.palette.onSurface,
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },

  '&.selected': {
    backgroundColor: theme.vars.palette.primary,
    color: theme.vars.palette.onPrimary,
  },

  '&:disabled': {
    opacity: 0.5,
    cursor: 'not-allowed',
  },

  '&:focus-visible': {
    outline: `1px solid ${theme.vars.palette.primary}`,
    outlineOffset: '-2px',
  },
}));

export type MonthListViewProps = Omit<MonthCalendarComponentProps, 'viewStyle'>;
export const MonthListView = React.memo<MonthListViewProps>(
  ({
    date,
    viewDate,
    onChange,
    minDate,
    maxDate,
    disablePast,
    disableFuture,
    disabled = false,
    readOnly = false,
    shouldDisableMonth,
  }) => {
    const now = useNow();
    const utils = useUtils();
    const [focusedMonth, setFocusedMonth] = React.useState<number | null>(date ? date.month() : null);

    const isMonthDisabled = React.useCallback(
      (dateToValidate: PickerDateType) => {
        const firstEnabledMonth = utils.startOfMonth(disablePast && utils.isAfter(now, minDate) ? now : minDate);

        const lastEnabledMonth = utils.startOfMonth(disableFuture && utils.isBefore(now, maxDate) ? now : maxDate);

        const monthToValidate = utils.startOfMonth(dateToValidate);

        if (utils.isBefore(monthToValidate, firstEnabledMonth)) {
          return true;
        }

        if (utils.isAfter(monthToValidate, lastEnabledMonth)) {
          return true;
        }

        if (!shouldDisableMonth) {
          return false;
        }

        return shouldDisableMonth(monthToValidate);
      },
      [disableFuture, disablePast, maxDate, minDate, now, shouldDisableMonth, utils],
    );

    const handleKeyDown = React.useCallback(
      (e: React.KeyboardEvent<HTMLButtonElement>, index: number) => {
        e.preventDefault();

        switch (e.key) {
          case 'Enter':
          case ' ':
            if (!disabled && !readOnly) {
              onChange(index);
            }
            break;
          case 'ArrowDown':
            // Move to next month in list
            if (index + 1 < 12) {
              setFocusedMonth(index + 1);
            }
            break;
          case 'ArrowUp':
            // Move to previous month in list
            if (index - 1 >= 0) {
              setFocusedMonth(index - 1);
            }
            break;
          case 'ArrowRight':
            // Same as ArrowDown for list view
            if (index + 1 < 12) {
              setFocusedMonth(index + 1);
            }
            break;
          case 'ArrowLeft':
            // Same as ArrowUp for list view
            if (index - 1 >= 0) {
              setFocusedMonth(index - 1);
            }
            break;
          case 'Home':
            setFocusedMonth(0);
            break;
          case 'End':
            setFocusedMonth(11);
            break;
          case 'PageUp':
            // Move up 5 months
            if (index - 5 >= 0) {
              setFocusedMonth(index - 5);
            } else {
              setFocusedMonth(0);
            }
            break;
          case 'PageDown':
            // Move down 5 months
            if (index + 5 < 12) {
              setFocusedMonth(index + 5);
            } else {
              setFocusedMonth(11);
            }
            break;
        }
      },
      [disabled, readOnly, onChange],
    );

    // Focus the month when it changes
    React.useEffect(() => {
      if (focusedMonth !== null) {
        const element = document.querySelector(`[data-month-index="${focusedMonth}"]`) as HTMLButtonElement;
        element?.focus();
      }
    }, [focusedMonth]);

    return (
      <ListContainer className={dockedDatePickerClasses.monthView} role="listbox" aria-label="Month selection">
        {getMonthsInYear(utils, viewDate).map((month, index) => {
          const monthName = utils.format(month, 'monthShort');
          const monthNumber = utils.getMonth(month);
          const isSelected = date && utils.getMonth(date) === monthNumber;
          const isDisabled = disabled || isMonthDisabled(month);
          return (
            <MonthItem
              key={index}
              data-month-index={index}
              className={clsx(isSelected && ['selected', dockedDatePickerClasses.selectedItem])}
              onClick={() => {
                if (!disabled && !readOnly) {
                  onChange(index);
                }
              }}
              onKeyDown={(e) => handleKeyDown(e, index)}
              onFocus={() => setFocusedMonth(index)}
              role="option"
              aria-selected={isSelected}
              disabled={isDisabled || readOnly}
              tabIndex={isSelected || index === focusedMonth ? 0 : -1}
              type="button"
            >
              {monthName}
            </MonthItem>
          );
        })}
      </ListContainer>
    );
  },
);
