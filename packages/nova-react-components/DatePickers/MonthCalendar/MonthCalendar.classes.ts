import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface MonthCalendarClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the component when disabled. */
  disabled: string;
}

export type MonthCalendarClassKey = keyof MonthCalendarClasses;

export function getMonthCalendarUtilityClass(slot: string): string {
  return generateUtilityClass('NovaMonthCalendar', slot);
}

const monthCalendarClasses: MonthCalendarClasses = generateUtilityClasses('NovaMonthCalendar', ['root', 'disabled']);

export default monthCalendarClasses;
