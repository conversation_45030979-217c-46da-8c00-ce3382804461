import dayjs, { Dayjs } from 'dayjs/esm';
import customParseFormat from 'dayjs/esm/plugin/customParseFormat';
import localizedFormat from 'dayjs/esm/plugin/localizedFormat';
import localeData from 'dayjs/esm/plugin/localeData';
import isBetween from 'dayjs/esm/plugin/isBetween';
import weekOfYear from 'dayjs/esm/plugin/weekOfYear';
import { AdapterFormats, DateAdapter, DateBuilderReturnType } from '../models/adapter';

// Initialize dayjs plugins
dayjs.extend(customParseFormat);
dayjs.extend(localizedFormat);
dayjs.extend(localeData);
dayjs.extend(isBetween);
dayjs.extend(weekOfYear);

export interface DayjsAdapterOptions {
  locale?: string;
}

declare module '../models/pickers' {
  interface PickerValidDateLookup {
    dayjs: Dayjs;
  }
}

const defaultFormats: AdapterFormats = {
  year: 'YYYY',
  month: 'MMMM',
  monthShort: 'MMM',
  dayOfMonth: 'D',
  dayOfMonthFull: 'Do',
  weekday: 'dddd',
  weekdayShort: 'dd',
  hours24h: 'HH',
  hours12h: 'hh',
  meridiem: 'A',
  minutes: 'mm',
  seconds: 'ss',

  fullDate: 'll',
  keyboardDate: 'L',
  shortDate: 'MMM D',
  normalDate: 'D MMMM',
  normalDateWithWeekday: 'ddd, MMM D',

  fullTime12h: 'hh:mm A',
  fullTime24h: 'HH:mm',

  keyboardDateTime12h: 'L hh:mm A',
  keyboardDateTime24h: 'L HH:mm',
};

export class DayjsAdapter implements DateAdapter<Dayjs> {
  public locale?: string;
  public formats: AdapterFormats;

  constructor({ locale }: DayjsAdapterOptions = {}) {
    this.locale = locale;
    this.formats = defaultFormats;
    if (locale) {
      try {
        dayjs.locale(locale);
      } catch (e) {
        console.warn(`Locale ${locale} not found, using default`);
      }
    }
  }

  date = <T extends string | null | undefined>(value?: T): DateBuilderReturnType<T> => {
    type R = DateBuilderReturnType<T>;
    if (value === null) {
      return null as unknown as R;
    }

    const parsedValue: Dayjs = dayjs(value);

    if (this.locale === undefined) {
      return parsedValue as unknown as R;
    }

    return parsedValue.locale(this.locale || 'en') as unknown as R;
  };

  toJsDate = (value: Dayjs): Date => {
    return value.toDate();
  };

  parse = (value: string, format: string): Dayjs | null => {
    if (value === '') {
      return null;
    }
    return dayjs(value, format, this.locale || 'en');
  };

  // Formatting
  format = (value: Dayjs, formatKey: keyof AdapterFormats): string => {
    return this.formatByString(value, defaultFormats[formatKey]);
  };

  formatByString = (value: Dayjs, formatString: string): string => {
    return value.format(formatString);
  };

  // Date info
  getYear = (value: Dayjs): number => {
    return value.year();
  };

  getMonth = (value: Dayjs): number => {
    return value.month();
  };

  getHours = (value: Dayjs) => {
    return value.hour();
  };

  getMinutes = (value: Dayjs) => {
    return value.minute();
  };

  getSeconds = (value: Dayjs) => {
    return value.second();
  };

  getMilliseconds = (value: Dayjs) => {
    return value.millisecond();
  };

  getDaysInMonth = (value: Dayjs): number => {
    return value.daysInMonth();
  };

  getDate = (value: Dayjs): number => {
    return value.date();
  };

  // Date checking
  isValid = (value: any): boolean => {
    return dayjs(value).isValid();
  };

  isSameDay = (value: Dayjs, comparing: Dayjs): boolean => {
    return value.isSame(comparing, 'day');
  };

  isSameMonth = (value: Dayjs, comparing: Dayjs): boolean => {
    return value.isSame(comparing, 'month');
  };

  isSameYear = (value: Dayjs, comparing: Dayjs): boolean => {
    return value.isSame(comparing, 'year');
  };

  isBefore = (value: Dayjs, comparing: Dayjs): boolean => {
    return value.isBefore(comparing);
  };

  isAfter = (value: Dayjs, comparing: Dayjs): boolean => {
    return value.isAfter(comparing);
  };

  isBeforeDay = (value: Dayjs, comparing: Dayjs): boolean => {
    return value.isBefore(comparing, 'day');
  };

  isAfterDay = (value: Dayjs, comparing: Dayjs): boolean => {
    return value.isAfter(comparing, 'day');
  };

  isBeforeYear = (value: Dayjs, comparing: Dayjs): boolean => {
    return value.isBefore(comparing, 'year');
  };

  isAfterYear = (value: Dayjs, comparing: Dayjs): boolean => {
    return value.isAfter(comparing, 'year');
  };
  // Date math
  add = (value: Dayjs, amount: number, unit: string): Dayjs => {
    return value.add(amount, unit as any);
  };

  subtract = (value: Dayjs, amount: number, unit: string): Dayjs => {
    return value.subtract(amount, unit as any);
  };

  addDays = (value: Dayjs, amount: number): Dayjs => {
    return value.add(amount, 'day');
  };

  addMonths = (value: Dayjs, amount: number): Dayjs => {
    return value.add(amount, 'month');
  };

  addYears = (value: Dayjs, amount: number): Dayjs => {
    return value.add(amount, 'year');
  };

  // Date setters
  setYear = (value: Dayjs, year: number): Dayjs => {
    return value.year(year);
  };

  setMonth = (value: Dayjs, month: number): Dayjs => {
    return value.month(month);
  };

  setDate = (value: Dayjs, date: number): Dayjs => {
    return value.date(date);
  };

  setHours = (value: Dayjs, hours: number) => {
    return value.set('hour', hours);
  };

  setMinutes = (value: Dayjs, minutes: number) => {
    return value.set('minute', minutes);
  };

  setSeconds = (value: Dayjs, seconds: number) => {
    return value.set('second', seconds);
  };

  setMilliseconds = (value: Dayjs, milliseconds: number) => {
    return value.set('millisecond', milliseconds);
  };
  // Defaults
  startOf = (value: Dayjs, unit: string): Dayjs => {
    return value.startOf(unit as any);
  };

  endOf = (value: Dayjs, unit: string): Dayjs => {
    return value.endOf(unit as any);
  };

  startOfDay = (value: Dayjs): Dayjs => {
    return value.startOf('day');
  };

  endOfDay = (value: Dayjs): Dayjs => {
    return value.endOf('day');
  };

  startOfMonth = (value: Dayjs): Dayjs => {
    return value.startOf('month');
  };

  endOfMonth = (value: Dayjs): Dayjs => {
    return value.endOf('month');
  };

  startOfWeek = (value: Dayjs): Dayjs => {
    return value.startOf('week');
  };

  endOfWeek = (value: Dayjs): Dayjs => {
    return value.endOf('week');
  };

  startOfYear = (value: Dayjs): Dayjs => {
    return value.startOf('year');
  };

  endOfYear = (value: Dayjs): Dayjs => {
    return value.endOf('year');
  };

  // Locale related
  getCurrentLocaleCode = (): string => {
    return this.locale || 'en';
  };

  is12HourCycleInCurrentLocale = (): boolean => {
    // Check if the locale is using a 12-hour cycle format
    return /A|a/.test(dayjs().locale(this.getCurrentLocaleCode()).localeData().longDateFormat('LT'));
  };

  // Calendar helpers

  getWeekArray = (value: Dayjs) => {
    const start = this.startOfWeek(this.startOfMonth(value));
    const end = this.endOfWeek(this.endOfMonth(value));

    let count = 0;
    let current = start;
    const nestedWeeks: Dayjs[][] = [];

    while (current < end) {
      const weekNumber = Math.floor(count / 7);
      nestedWeeks[weekNumber] = nestedWeeks[weekNumber] || [];
      nestedWeeks[weekNumber].push(current);

      current = this.addDays(current, 1);

      count += 1;
    }

    return nestedWeeks;
  };

  getWeekNumber = (value: Dayjs): number => {
    return value.week();
  };

  getDayOfWeek(value: Dayjs): number {
    return value.day() + 1;
  }
}
