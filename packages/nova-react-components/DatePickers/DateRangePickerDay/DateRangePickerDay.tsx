'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { DateRangePickerDayProps, DateRangePickerDayOwnerState } from './DateRangePickerDay.types';
import { dateRangePickerDayClasses, getDateRangePickerDayUtilityClass } from './DateRangePickerDay.classes';
import { DatePickerDay } from '../DatePickerDay';

/**
 * Utility to get the proper classes for the DateRangePickerDay component
 */
const useUtilityClasses = (ownerState: DateRangePickerDayOwnerState) => {
  const {
    isHighlighting: isDaySelected,
    isStartOfHighlighting: isDaySelectionStart,
    isEndOfHighlighting: isDaySelectionEnd,
    isPreviewing: isDayPreviewed,
    isStartOfPreviewing: isDayPreviewStart,
    isEndOfPreviewing: isDayPreviewEnd,
    outsideCurrentMonth: isDayOutsideMonth,
    disabled,
    // Additional properties similar to MUI's implementation
    day,
    isFirstVisibleCell,
    isLastVisibleCell,
  } = ownerState;

  // Calculate additional states that MUI's implementation uses
  const isDayInsideSelection = isDaySelected && !isDaySelectionStart && !isDaySelectionEnd;
  const isDayInsidePreview = isDayPreviewed && !isDayPreviewStart && !isDayPreviewEnd;

  const slots = {
    root: [
      'root',
      isDaySelected && 'rangeIntervalDayHighlight',
      isDaySelectionStart && 'rangeIntervalDayHighlightStart',
      isDaySelectionEnd && 'rangeIntervalDayHighlightEnd',
      isDayOutsideMonth && 'outsideCurrentMonth',
      isFirstVisibleCell && 'firstVisibleCell',
      isLastVisibleCell && 'lastVisibleCell',
    ],
    rangeIntervalPreview: [
      'rangeIntervalPreview',
      isDayPreviewed && 'rangeIntervalDayPreview',
      isDayPreviewStart && 'rangeIntervalDayPreviewStart',
      isDayPreviewEnd && 'rangeIntervalDayPreviewEnd',
    ],
    day: [
      'day',
      !isDaySelected && 'notSelectedDate',
      !isDaySelected && 'dayOutsideRangeInterval',
      isDayInsideSelection && 'dayInsideRangeInterval',
    ],
  };

  return composeClasses(slots, getDateRangePickerDayUtilityClass, {});
};

// Styles for end borders (right side)
const endBorderStyle = {
  borderTopRightRadius: '50%',
  borderBottomRightRadius: '50%',
};

// Styles for start borders (left side)
const startBorderStyle = {
  borderTopLeftRadius: '50%',
  borderBottomLeftRadius: '50%',
};

/**
 * Root element of the DateRangePickerDay
 */
const DateRangePickerDayRoot = styled('div')(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',

  // Highlighting styles for the range
  [`&.${dateRangePickerDayClasses.rangeIntervalDayHighlight}`]: {
    backgroundColor: theme.vars.palette.secondaryContainer,
    borderRadius: 0,
  },

  // Start of the range gets a rounded left border
  [`&.${dateRangePickerDayClasses.rangeIntervalDayHighlightStart}`]: {
    ...startBorderStyle,
  },

  // End of the range gets a rounded right border
  [`&.${dateRangePickerDayClasses.rangeIntervalDayHighlightEnd}`]: {
    ...endBorderStyle,
  },

  // First visible cell in calendar
  [`&.${dateRangePickerDayClasses.firstVisibleCell}`]: {
    ...startBorderStyle,
  },

  // Last visible cell in calendar
  [`&.${dateRangePickerDayClasses.lastVisibleCell}`]: {
    ...endBorderStyle,
  },
}));

/**
 * Middle layer for preview effects with dashed borders
 */
const DateRangePickerDayRangeIntervalPreview = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  zIndex: -1,

  // Preview mode styles
  [`&.${dateRangePickerDayClasses.rangeIntervalDayPreview}`]: {
    backgroundColor: theme.vars.palette.secondaryContainer,
  },

  // Start of preview gets a dashed left border
  [`&.${dateRangePickerDayClasses.rangeIntervalDayPreviewStart}`]: {
    borderLeftColor: theme.vars.palette.secondaryContainer,
    ...startBorderStyle,
  },

  // End of preview gets a dashed right border
  [`&.${dateRangePickerDayClasses.rangeIntervalDayPreviewEnd}`]: {
    borderRightColor: theme.vars.palette.secondaryContainer,
    ...endBorderStyle,
  },
}));

/**
 * Styled DatePickerDay component to work with range selection
 */
const DateRangePickerDayButton = styled(DatePickerDay)({
  variants: [
    {
      props: { draggable: true },
      style: {
        cursor: 'grab',
        touchAction: 'none',
      },
    },
  ],
});

/**
 * DateRangePickerDay wraps DatePickerDay and adds range/preview logic and classes.
 * This implementation follows MUI's pattern of using a three-layer structure.
 */
export const DateRangePickerDay = React.forwardRef<HTMLDivElement, DateRangePickerDayProps>(
  function DateRangePickerDay(props, ref) {
    const {
      className,
      isHighlighting = false,
      isStartOfHighlighting = false,
      isEndOfHighlighting = false,
      isPreviewing = false,
      isStartOfPreviewing = false,
      isEndOfPreviewing = false,
      onDaySelect,
      onDayHover,
      disabled = false,
      day,
      outsideCurrentMonth = false,
      selected = false,
      isFirstVisibleCell = false,
      isLastVisibleCell = false,
      draggable,
      onDragStart,
      onDragOver,
      onDragEnd,
      onDrop,
      'data-timestamp': dataTimestamp,
      'data-position': dataPosition,
      ...other
    } = props;

    // Transform our props into the MUI-style ownerState
    const ownerState: DateRangePickerDayOwnerState = {
      ...props,
      isHighlighting,
      isStartOfHighlighting,
      isEndOfHighlighting,
      isPreviewing,
      isStartOfPreviewing,
      isEndOfPreviewing,
      disabled,
      isFirstVisibleCell,
      isLastVisibleCell,
    };

    // Get the utility classes for styling
    const classes = useUtilityClasses(ownerState);

    // Wrapper to handle onMouseEnter event and pass day to parent
    const handleDayMouseEnter = React.useCallback(() => {
      if (onDayHover && !disabled) {
        onDayHover(day);
      }
    }, [onDayHover, day, disabled]);

    // Determine if we should render highlights and previews
    const shouldRenderHighlight = isHighlighting && !outsideCurrentMonth;
    const shouldRenderPreview = isPreviewing && !outsideCurrentMonth;

    return (
      <DateRangePickerDayRoot
        ref={ref}
        className={clsx(classes.root, className)}
        data-testid={shouldRenderHighlight ? 'DateRangeHighlight' : undefined}
      >
        <DateRangePickerDayRangeIntervalPreview
          className={classes.rangeIntervalPreview}
          data-testid={shouldRenderPreview ? 'DateRangePreview' : undefined}
        />
        <DateRangePickerDayButton
          {...other}
          day={day}
          disabled={disabled}
          onMouseEnter={handleDayMouseEnter}
          outsideCurrentMonth={outsideCurrentMonth}
          selected={selected}
          onDaySelect={onDaySelect}
          className={clsx(classes.day)}
          draggable={draggable}
          onDragStart={onDragStart}
          onDragOver={onDragOver}
          onDragEnd={onDragEnd}
          onDrop={onDrop}
          data-timestamp={dataTimestamp}
          data-position={dataPosition}
        />
      </DateRangePickerDayRoot>
    );
  },
);
