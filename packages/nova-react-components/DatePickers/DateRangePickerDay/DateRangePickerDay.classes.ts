import {
  unstable_generateUtilityClass as generateUtilityClass,
  unstable_generateUtilityClasses as generateUtilityClasses,
} from '@mui/utils';

export interface DateRangePickerDayClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the day element. */
  day: string;
  /** Styles applied to the day element when it's selected. */
  daySelected: string;
  /** Styles applied to the day element when it's highlighted. */
  dayHighlight: string;
  /** Styles applied to the day element when it's in preview mode. */
  dayPreview: string;
  /** Styles applied to the day element when it's outside the current month. */
  dayOutsideMonth: string;
  /** Styles applied to the day element when it's not selected. */
  notSelectedDate: string;
  /** Styles applied to the day range interval element. */
  rangeIntervalDayHighlight: string;
  /** Styles applied to the day range interval element when it's the start of a range. */
  rangeIntervalDayHighlightStart: string;
  /** Styles applied to the day range interval element when it's the end of a range. */
  rangeIntervalDayHighlightEnd: string;
  /** Styles applied to the day range interval element in preview mode. */
  rangeIntervalDayPreview: string;
  /** Styles applied to the day range interval element when it's the start of a preview range. */
  rangeIntervalDayPreviewStart: string;
  /** Styles applied to the day range interval element when it's the end of a preview range. */
  rangeIntervalDayPreviewEnd: string;
  /** Styles applied to the day element outside the range interval. */
  dayOutsideRangeInterval: string;
  /** Styles applied to the day inside the range interval. */
  dayInsideRangeInterval: string;
  /** Styles applied to the range interval preview wrapper. */
  rangeIntervalPreview: string;
  /** Styles applied to the root element if day is outside current month. */
  outsideCurrentMonth: string;
  /** Styles applied to the root element if day is the first visible cell. */
  firstVisibleCell: string;
  /** Styles applied to the root element if day is the last visible cell. */
  lastVisibleCell: string;
}

export type DateRangePickerDayClassKey = keyof DateRangePickerDayClasses;

export function getDateRangePickerDayUtilityClass(slot: string) {
  return generateUtilityClass('NovaDateRangePickerDay', slot);
}

export const dateRangePickerDayClasses: DateRangePickerDayClasses = generateUtilityClasses('NovaDateRangePickerDay', [
  'root',
  'day',
  'daySelected',
  'dayHighlight',
  'dayPreview',
  'dayOutsideMonth',
  'notSelectedDate',
  'rangeIntervalDayHighlight',
  'rangeIntervalDayHighlightStart',
  'rangeIntervalDayHighlightEnd',
  'rangeIntervalDayPreview',
  'rangeIntervalDayPreviewStart',
  'rangeIntervalDayPreviewEnd',
  'dayOutsideRangeInterval',
  'dayInsideRangeInterval',
  'rangeIntervalPreview',
  'outsideCurrentMonth',
  'firstVisibleCell',
  'lastVisibleCell',
]);
