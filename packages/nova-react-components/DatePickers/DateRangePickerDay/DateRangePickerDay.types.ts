import * as React from 'react';
import { PickerDateType } from '../models/pickers';
import { DatePickerDayProps } from '../DatePickerDay/DatePickerDay.types';

export interface DateRangePickerDayProps extends DatePickerDayProps {
  /**
   * If `true`, day is within a highlighted date range.
   */
  isHighlighting?: boolean;

  /**
   * If `true`, day is the start of a highlighted date range.
   */
  isStartOfHighlighting?: boolean;

  /**
   * If `true`, day is the end of a highlighted date range.
   */
  isEndOfHighlighting?: boolean;

  /**
   * If `true`, day is within a preview date range.
   */
  isPreviewing?: boolean;

  /**
   * If `true`, day is the start of a preview date range.
   */
  isStartOfPreviewing?: boolean;

  /**
   * If `true`, day is the end of a preview date range.
   */
  isEndOfPreviewing?: boolean;

  /**
   * Callback fired when a day is selected.
   */
  onDaySelect?: (day: PickerDateType) => void;
  onDayHover?: (day: PickerDateType) => void; // Custom handler
  /**
   * If `true`, day is the first visible cell of the month.
   * Either the first day of the month or the first day of the week depending on showDaysOutsideCurrentMonth.
   */
  isFirstVisibleCell?: boolean;

  /**
   * If `true`, day is the last visible cell of the month.
   * Either the last day of the month or the last day of the week depending on showDaysOutsideCurrentMonth.
   */
  isLastVisibleCell?: boolean;

  /**
   * If `true`, the element is draggable, meaning it can be dragged.
   */
  draggable?: boolean;

  /**
   * The timestamp data attribute for identification in drag operations.
   */
  'data-timestamp'?: number;

  /**
   * The position data attribute (start/end) for identification in drag operations.
   */
  'data-position'?: string;
}

export interface DateRangePickerDayOwnerState extends DateRangePickerDayProps {
  /**
   * Indicates if the day is selected (used for internal state mapping)
   */
  isDaySelected?: boolean;

  /**
   * Indicates if the day is outside the current month (used for internal state mapping)
   */
  isDayOutsideMonth?: boolean;

  /**
   * Indicates if the day is the start of a selected range (used for internal state mapping)
   */
  isDaySelectionStart?: boolean;

  /**
   * Indicates if the day is the end of a selected range (used for internal state mapping)
   */
  isDaySelectionEnd?: boolean;

  /**
   * Indicates if the day is inside a selection but not the start or end (used for internal state mapping)
   */
  isDayInsideSelection?: boolean;

  /**
   * Indicates if the day is in preview mode (used for internal state mapping)
   */
  isDayPreviewed?: boolean;

  /**
   * Indicates if the day is the start of a preview range (used for internal state mapping)
   */
  isDayPreviewStart?: boolean;

  /**
   * Indicates if the day is the end of a preview range (used for internal state mapping)
   */
  isDayPreviewEnd?: boolean;

  /**
   * Indicates if the day is inside a preview but not the start or end (used for internal state mapping)
   */
  isDayInsidePreview?: boolean;
}
