'use client';
import * as React from 'react';
import { ModalDatePicker } from '../ModalDatePicker/ModalDatePicker';
import { DockedDatePicker } from '../DockedDatePicker/DockedDatePicker';
import { DatePickerProps } from './DatePicker.types';
import { unstable_createUseMediaQuery as createUseMediaQuery } from '@mui/system/useMediaQuery';

const useMediaQuery = createUseMediaQuery();

/**
 * The DatePicker component provides a responsive date picking experience.
 * On smaller screens, it uses the ModalDatePicker (popup).
 * On larger screens, it uses the DockedDatePicker (inline).
 */
export const DatePicker = React.forwardRef<HTMLDivElement, DatePickerProps>((props, ref) => {
  // Media query to determine which picker to use
  const isMobile = useMediaQuery('@media (pointer: fine)', { noSsr: true, defaultMatches: true });

  if (isMobile) {
    return <ModalDatePicker ref={ref} {...props} />;
  }

  return <DockedDatePicker ref={ref} {...props} />;
});
