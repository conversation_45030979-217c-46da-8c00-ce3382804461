import {
  unstable_generateUtilityClass as generateUtilityClass,
  unstable_generateUtilityClasses as generateUtilityClasses,
} from '@mui/utils';

export interface DateRangeCalendarClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the month container element. */
  monthContainer: string;
  /** Styles applied to the day calendar container when dragging. */
  dayDragging: string;
}

export type DateRangeCalendarClassKey = keyof DateRangeCalendarClasses;

/**
 * Generates a utility class for the DateRangeCalendar component
 * @param {string} slot - The class slot name
 * @returns {string} The generated class name
 */
export function getDateRangeCalendarUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDateRangeCalendar', slot);
}

/**
 * Generated utility classes for the DateRangeCalendar component
 */
export const dateRangeCalendarClasses: DateRangeCalendarClasses = generateUtilityClasses('NovaDateRangeCalendar', [
  'root',
  'monthContainer',
  'dayDragging',
]);
