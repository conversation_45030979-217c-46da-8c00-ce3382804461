import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import { ExportedDateCalendarProps } from '../DateCalendar/DateCalendar.types';
import { PickerDateType } from '../models/pickers';
import { PickerRangeValue, RangePosition } from '../utils/dateRangeUtils';
import { SxProps } from '../../types/theme';
import { DateRangePickerDay, DateRangePickerDayProps } from '../DateRangePickerDay';
import { DatePickerVariant } from '../types';

export interface DateRangeCalendarComponentProps
  extends Omit<ExportedDateCalendarProps, 'value' | 'defaultValue' | 'onChange' | 'openTo' | 'views'> {
  /**
   * The selected range.
   */
  value?: PickerRangeValue;

  /**
   * The default selected range.
   */
  defaultValue?: PickerRangeValue;

  /**
   * Callback fired when the range changes.
   * @param {PickerRangeValue} range The new range value.
   */
  onChange?: (range: PickerRangeValue) => void;

  /**
   * Number of calendars to display side by side.
   * @default 2
   */
  calendars?: 1 | 2 | 3;

  /**
   * Position of the current month in the calendar grid.
   * @default 1
   */
  currentMonthCalendarPosition?: 1 | 2 | 3;

  /**
   * The position in the range that is being edited (start or end).
   */
  rangePosition?: RangePosition;

  /**
   * The default range position for uncontrolled component.
   * @default 'start'
   */
  defaultRangePosition?: RangePosition;

  /**
   * Callback fired when the range position changes.
   * @param {RangePosition} position The new range position.
   */
  onRangePositionChange?: (position: RangePosition) => void;

  /**
   * Available range positions for selection.
   * @default ['start', 'end']
   */
  availableRangePositions?: RangePosition[];

  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;

  /**
   * If `true`, after selecting `start` date calendar will not automatically switch to the month of `end` date.
   * @default false
   */
  disableAutoMonthSwitching?: boolean;

  /**
   * If `true`, editing dates by dragging is disabled.
   * @default false
   */
  disableDragEditing?: boolean;

  /**
   * The variant of the date picker.
   * @default 'docked'
   */
  variant?: DatePickerVariant;
}

export interface DateRangeCalendarSlots {
  /**
   * Custom component for calendar header.
   * @default CalendarHeader
   */
  calendarHeader?: React.ElementType;

  /**
   * Custom component for days.
   * @default DateRangePickerDay
   */
  day?: React.ElementType<DateRangePickerDayProps>;
}

export interface DateRangeCalendarSlotProps {
  calendarHeader?: SlotComponentProps<React.ElementType, object, DateRangeCalendarOwnerState>;
  day?: SlotComponentProps<typeof DateRangePickerDay, object, DateRangeCalendarOwnerState & { day: PickerDateType }>;
}

export interface DateRangeCalendarTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    DateRangeCalendarComponentProps & {
      /**
       * The slots for customizing the component appearance.
       */
      slots?: DateRangeCalendarSlots;

      /**
       * The props used for each slot.
       */
      slotProps?: DateRangeCalendarSlotProps;
    };
  defaultComponent: D;
}

export type DateRangeCalendarProps<D extends React.ElementType = DateRangeCalendarTypeMap['defaultComponent']> =
  OverrideProps<DateRangeCalendarTypeMap<object, D>, D> & {
    component?: D;
  };

export interface DateRangeCalendarOwnerState extends DateRangeCalendarProps {
  /**
   * Whether a day is currently being dragged.
   */
  isDraggingDay?: boolean;
}
