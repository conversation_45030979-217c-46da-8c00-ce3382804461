'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { unstable_composeClasses as composeClasses, unstable_useEventCallback as useEventCallback } from '@mui/utils';
import { useUtils } from '../hooks/useUtils';
import { DayCalendar } from '../DayCalendar/DayCalendar';
import { DateRangeCalendarProps, DateRangeCalendarOwnerState } from './DateRangeCalendar.types';
import { dateRangeCalendarClasses, getDateRangeCalendarUtilityClass } from './DateRangeCalendar.classes';
import {
  PickerRangeValue,
  RangePosition,
  calculateRangeChange,
  calculateRangePreview,
  isStartOfRange,
  isEndOfRange,
  isWithinRange,
} from '../utils/dateRangeUtils';
import { PickerDateType } from '../models/pickers';
import { DateRangePickerDay } from '../DateRangePickerDay';
import { dateRangePickerDayClasses } from '../DateRangePickerDay/DateRangePickerDay.classes';
import { useDragRange } from '../hooks/useDragRange';
import { RangeCalendarHeader } from '../RangeCalendarHeader/RangeCalendarHeader';
import { useRangePosition } from '../hooks/useRangePosition';
import resolveComponentProps from '@mui/utils/resolveComponentProps';

const useUtilityClasses = (ownerState: DateRangeCalendarOwnerState) => {
  const { isDraggingDay } = ownerState;

  const slots = {
    root: ['root'],
    monthContainer: ['monthContainer'],
    dayCalendar: [isDraggingDay && 'dayDragging'],
  };

  return composeClasses(slots, getDateRangeCalendarUtilityClass, {});
};

// Main root component with animation support
const CalendarRoot = styled('div')<{
  variant: 'modal' | 'docked';
  animating?: boolean;
  direction?: 'up' | 'down' | null;
}>({
  display: 'flex',
  flexDirection: 'row',
  overflow: 'hidden',
  position: 'relative',
  transition: 'transform 400ms cubic-bezier(0.2, 0.8, 0.2, 1)' /* iOS-like spring curve */,
  /* Improve touch scrolling on iOS */
  WebkitOverflowScrolling: 'touch',
  /* Optimize animation performance */
  willChange: 'transform',
  variants: [
    {
      props: { variant: 'modal' },
      style: {
        flexDirection: 'column',
      },
    },
  ],
});

const MonthsContainer = styled('div')<{ variant: 'modal' | 'docked' }>({
  display: 'flex',
  flexDirection: 'column',
});

// Create a styled variant of DayCalendar for range selection
const DayCalendarForRange = styled(DayCalendar)(({ theme }) => ({
  [`&.${dateRangeCalendarClasses.dayDragging}`]: {
    [`& .${dateRangePickerDayClasses.day}`]: {
      cursor: 'grabbing',
    },
    [`& .${dateRangePickerDayClasses.root}:not(.${dateRangePickerDayClasses.rangeIntervalDayHighlightStart}):not(.${dateRangePickerDayClasses.rangeIntervalDayHighlightEnd}) .${dateRangePickerDayClasses.day}:not(.${dateRangePickerDayClasses.notSelectedDate})`]:
      {
        opacity: 0.6,
      },
  },
}));

export const DateRangeCalendar = React.forwardRef<HTMLDivElement, DateRangeCalendarProps>((props, ref) => {
  const {
    value: valueProp,
    defaultValue,
    onChange,
    className,
    disableFuture,
    disablePast,
    minDate,
    maxDate,
    shouldDisableDate,
    calendars = 2,
    currentMonthCalendarPosition = 1,
    rangePosition: rangePositionProp,
    defaultRangePosition = 'start',
    onRangePositionChange,
    availableRangePositions = ['start', 'end'],
    slots = {},
    slotProps = {},
    disabled,
    readOnly,
    disableAutoMonthSwitching = false,
    disableDragEditing = false,
    variant,
    ...other
  } = props;

  const utils = useUtils();

  // Use the useRangePosition hook instead of manual state management
  const { rangePosition, setRangePosition } = useRangePosition({
    rangePosition: rangePositionProp,
    defaultRangePosition,
    onRangePositionChange,
  });

  // State for range values
  const [valueState, setValueState] = React.useState<PickerRangeValue>(defaultValue || [null, null]);
  const value = valueProp !== undefined ? valueProp : valueState;

  // State for hover preview
  const [previewValue, setPreviewValue] = React.useState<PickerRangeValue | null>(null);
  const [hoverDay, setHoverDay] = React.useState<PickerDateType | null>(null);

  // Get the starting/base month to display
  const [startMonth, setStartMonth] = React.useState(() => {
    if (value && value[0]) {
      return utils.startOfMonth(value[0]);
    }
    const today = utils.date();
    const monthOffset = Math.floor((currentMonthCalendarPosition - 1) / calendars);
    return utils.addMonths(utils.startOfMonth(today), -monthOffset);
  });

  const [animationDirection, setAnimationDirection] = React.useState<'up' | 'down' | null>(null);
  const [dragOffset, setDragOffset] = React.useState(0);
  // Unified interaction state to track all input types
  const interactionState = React.useRef({
    // Position tracking
    startY: null as number | null,
    currentY: null as number | null,
    startX: null as number | null,
    currentX: null as number | null,
    // Velocity tracking
    lastTime: 0,
    lastPosition: 0,
    velocity: 0,
    // State flags
    isDragging: false,
    inputType: null as 'mouse' | 'touch' | null,
  });

  // Get the calendar root element reference
  const calendarRootRef = React.useRef<HTMLDivElement | null>(null);

  // Handle month change in the calendar
  const handleMonthChange = useEventCallback((newMonth: PickerDateType) => {
    setStartMonth(utils.startOfMonth(newMonth));
  });

  // Simplified logic for handling range position changes using the hook
  const setRangePositionWithPriority = useEventCallback((position: RangePosition) => {
    setRangePosition(position);
  });

  // Use the useDragRange hook for drag-and-drop functionality
  const {
    isDragging,
    dragDay,
    dragPosition,
    handleDragStart,
    handleDragOver,
    handleDragEnd,
    handleDrop,
    getDraggingRange,
    setIsDragging,
    setDragDay,
    setDragPosition,
  } = useDragRange({
    value,
    rangePosition,
    onChange: onChange || setValueState,
    onRangePositionChange: setRangePositionWithPriority,
    utils,
    disabled,
    readOnly,
    disableDragEditing,
  });

  // Create an array of months to display
  const months = React.useMemo(() => {
    const result = [];
    for (let i = 0; i < calendars; i++) {
      const monthStart = utils.addMonths(startMonth, i);
      result.push(monthStart);
    }
    return result;
  }, [calendars, startMonth, utils]);

  // Shared utility functions for drag interactions
  const startDrag = useEventCallback((posY: number, posX: number, inputType: 'mouse' | 'touch') => {
    // Only allow dragging in modal variant
    if (variant !== 'modal') return;

    // Reset animation state
    setAnimationDirection(null);
    setDragOffset(0);

    // Store initial position
    interactionState.current = {
      ...interactionState.current,
      startY: posY,
      currentY: posY,
      startX: posX,
      currentX: posX,
      lastTime: Date.now(),
      lastPosition: posY,
      isDragging: true,
      inputType,
    };

    // Prevent text selection during mouse drag
    if (inputType === 'mouse') {
      document.body.style.userSelect = 'none';
    }
  });

  const updateDrag = useEventCallback((posY: number, posX: number) => {
    const state = interactionState.current;

    // Only proceed if we're dragging
    if (!state.isDragging || variant !== 'modal' || state.startY === null) return;

    // Update current position
    state.currentY = posY;
    state.currentX = posX;

    // Calculate drag distances
    const deltaY = posY - state.startY;
    const deltaX = posX - state.startX!;

    // If more horizontal than vertical, don't drag vertically
    if (Math.abs(deltaX) > Math.abs(deltaY)) return;

    // Calculate velocity for natural feeling
    const now = Date.now();
    const elapsed = now - state.lastTime;
    if (elapsed > 0) {
      const distance = posY - state.lastPosition;
      state.velocity = distance / elapsed;

      // Update for next calculation
      state.lastTime = now;
      state.lastPosition = posY;
    }

    // Apply resistance for natural feel
    const resistance = 0.5; // Lower = more resistance
    const transformY = deltaY * resistance;

    // Apply live transform
    setDragOffset(transformY);

    if (calendarRootRef.current) {
      calendarRootRef.current.style.transform = `translateY(${transformY}px)`;
      if (state.inputType === 'touch') {
        calendarRootRef.current.style.transition = 'none';
      }
    }

    return { deltaY, deltaX };
  });

  const endDrag = useEventCallback(() => {
    const state = interactionState.current;

    // Only proceed if we're dragging
    if (!state.isDragging || variant !== 'modal' || state.startY === null || state.currentY === null) return;

    // Reset transform
    if (calendarRootRef.current) {
      calendarRootRef.current.style.transform = '';
      calendarRootRef.current.style.transition = '';
    }

    // Calculate final values
    const diffY = state.currentY - state.startY;
    const diffX = state.currentX! - state.startX!;
    const minSwipeDistance = 50; // Minimum distance to register as a swipe

    // Consider velocity for more natural swipe
    const velocityThreshold = 0.3; // Pixels per millisecond
    const isHighVelocity = Math.abs(state.velocity) > velocityThreshold;

    // Determine if this is a valid vertical swipe
    if (Math.abs(diffY) > Math.abs(diffX) && (Math.abs(diffY) > minSwipeDistance || isHighVelocity)) {
      if (diffY > 0 || (state.velocity > 0 && isHighVelocity)) {
        // Swipe down - go to previous month
        goToPrevMonth();
      } else {
        // Swipe up - go to next month
        goToNextMonth();
      }
    } else {
      // Short swipe - animate back to original position
      setAnimationDirection(null);
    }

    // Reset interaction state
    interactionState.current = {
      ...interactionState.current,
      startY: null,
      currentY: null,
      startX: null,
      currentX: null,
      isDragging: false,
      velocity: 0,
    };

    // Reset styles
    setDragOffset(0);

    // Reset user-select if using mouse
    if (state.inputType === 'mouse') {
      document.body.style.userSelect = '';
    }

    // Reset input type
    interactionState.current.inputType = null;
  });

  // Cancel drag (e.g., when leaving the component)
  const cancelDrag = useEventCallback(() => {
    if (!interactionState.current.isDragging) return;

    // Reset transform
    if (calendarRootRef.current) {
      calendarRootRef.current.style.transform = '';
      calendarRootRef.current.style.transition = '';
    }

    // Reset state
    interactionState.current = {
      ...interactionState.current,
      startY: null,
      currentY: null,
      startX: null,
      currentX: null,
      isDragging: false,
      velocity: 0,
    };

    // Reset styles
    setDragOffset(0);

    // Reset user-select if using mouse
    if (interactionState.current.inputType === 'mouse') {
      document.body.style.userSelect = '';
    }

    // Reset input type
    interactionState.current.inputType = null;
  });

  // Handlers for navigating months with improved animation
  const goToPrevMonth = useEventCallback(() => {
    // Apply animation only for modal variant
    if (variant === 'modal') {
      setAnimationDirection('up');
      setStartMonth((prevMonth) => utils.addMonths(prevMonth, -calendars));
      setAnimationDirection(null);
    } else {
      // For docked variant, just update the month without animation
      setStartMonth((prevMonth) => utils.addMonths(prevMonth, -calendars));
    }
  });

  const goToNextMonth = useEventCallback(() => {
    // Apply animation only for modal variant
    if (variant === 'modal') {
      setAnimationDirection('down');
      setStartMonth((prevMonth) => utils.addMonths(prevMonth, calendars));
      setAnimationDirection(null);
    } else {
      // For docked variant, just update the month without animation
      setStartMonth((prevMonth) => utils.addMonths(prevMonth, calendars));
    }
  });

  // Handle wheel events for vertical scrolling
  const handleWheel = useEventCallback((event: React.WheelEvent) => {
    // Only apply wheel scrolling for modal variant
    if (variant !== 'modal') return;

    // Process wheel event directly
    if (event.deltaY > 0) {
      // Scrolling down - go to next month set
      goToNextMonth();
    } else if (event.deltaY < 0) {
      // Scrolling up - go to previous month set
      goToPrevMonth();
    }
  });

  // Handle mouse events using shared logic
  const handleMouseDown = useEventCallback((event: React.MouseEvent) => {
    startDrag(event.clientY, event.clientX, 'mouse');
  });

  const handleMouseMove = useEventCallback((event: React.MouseEvent) => {
    const result = updateDrag(event.clientY, event.clientX);

    // Only prevent default if we're actually dragging vertically
    if (result) {
      event.preventDefault();
    }
  });

  const handleMouseUp = useEventCallback(() => {
    endDrag();
  });

  // Handle mouse leave to cancel drag
  const handleMouseLeave = useEventCallback(() => {
    // Handle drag cancellation
    if (interactionState.current.isDragging) {
      cancelDrag();
    }

    // Handle hover day preview
    if (!isDragging) {
      setHoverDay(null);
      setPreviewValue(null);
    }
  });

  // Add global mouse up handler to handle cases when mouse is released outside the component
  React.useEffect(() => {
    if (variant !== 'modal') return;

    const handleGlobalMouseUp = () => {
      if (interactionState.current.isDragging && interactionState.current.inputType === 'mouse') {
        endDrag();
      }
    };

    window.addEventListener('mouseup', handleGlobalMouseUp);
    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);

      // Cleanup any leftover styles
      if (interactionState.current.isDragging && interactionState.current.inputType === 'mouse') {
        document.body.style.userSelect = '';
      }
    };
  }, [variant, endDrag]);

  // Handle touch events using shared logic
  const handleTouchStart = useEventCallback((event: React.TouchEvent) => {
    startDrag(event.touches[0].clientY, event.touches[0].clientX, 'touch');
  });

  const handleTouchMove = useEventCallback((event: React.TouchEvent) => {
    updateDrag(event.touches[0].clientY, event.touches[0].clientX);
  });

  const handleTouchEnd = useEventCallback(() => {
    endDrag();
  });

  // Use the utility functions for range checks
  const checkIsStartOfRange = (day: PickerDateType): boolean => isStartOfRange(utils, day, value);
  const checkIsEndOfRange = (day: PickerDateType): boolean => isEndOfRange(utils, day, value);

  // Create a proper day range for highlighting that spans the full days
  const valueDayRange = React.useMemo<PickerRangeValue>(
    () => [!value[0] ? value[0] : utils.startOfDay(value[0]), !value[1] ? value[1] : utils.endOfDay(value[1])],
    [value, utils],
  );

  const checkIsWithinRange = (day: PickerDateType): boolean => isWithinRange(utils, day, valueDayRange);

  // Preview range helpers
  const isStartOfPreview = (day: PickerDateType): boolean => {
    return previewValue ? isStartOfRange(utils, day, previewValue) : false;
  };
  const isEndOfPreview = (day: PickerDateType): boolean => {
    return previewValue ? isEndOfRange(utils, day, previewValue) : false;
  };
  const isWithinPreview = (day: PickerDateType): boolean => {
    return previewValue ? isWithinRange(utils, day, previewValue) : false;
  };

  // Handle day selection
  const handleDaySelect = useEventCallback((day: PickerDateType) => {
    if (readOnly || disabled || !day) return;

    // Use the range utility function to calculate the new range
    const { nextSelection, newRange } = calculateRangeChange({
      newDate: day,
      utils,
      range: value,
      rangePosition,
    });

    // Update the value first
    if (onChange) {
      onChange(newRange);
    } else {
      setValueState(newRange);
    }

    // When both dates are set, check if they're in the correct order
    // If not, swap them manually for consistent behavior
    if (newRange[0] && newRange[1] && utils.isAfter(newRange[0], newRange[1])) {
      const swappedRange: PickerRangeValue = [newRange[1], newRange[0]];
      if (onChange) {
        onChange(swappedRange);
      } else {
        setValueState(swappedRange);
      }
    }

    // Update position
    setRangePositionWithPriority(nextSelection);

    // Clear any preview that might be active
    setPreviewValue(null);

    // Auto switch month if needed - only if both dates are set and we're selecting the end date
    if (!disableAutoMonthSwitching && newRange[0] && newRange[1] && rangePosition === 'end') {
      const endMonth = utils.startOfMonth(newRange[1]);

      // Check if the end date is already visible in the current view
      const isEndDateVisible = months.some((month) => {
        // Check if the end date's month and year match any visible month
        return utils.isSameMonth(month, newRange[1]);
      });

      // Only switch month if the end date is not visible in the current view
      if (!isEndDateVisible) {
        handleMonthChange(endMonth);
      }
    }
  });

  // Handle mouse enter to preview range
  const handleDayMouseEnter = useEventCallback((day: PickerDateType) => {
    if (readOnly || disabled || isDragging) return;
    setHoverDay(day);

    // Always show preview when at least one date is selected, not just when rangePosition is 'end'
    if (value && (value[0] || value[1])) {
      // Calculate a preview even if we're in 'start' position and have an end date
      const preview = calculateRangePreview({
        utils,
        range: value,
        newDate: day,
        rangePosition,
      });
      setPreviewValue(preview);
    }
  });

  // Handle mouse leave to clear preview
  const handleDayMouseLeave = useEventCallback(() => {
    if (isDragging) return;
    setHoverDay(null);
    setPreviewValue(null);
  });

  const ownerState: DateRangeCalendarOwnerState = {
    ...props,
  };

  const classes = useUtilityClasses(ownerState);

  // A function to generate a key for each month that doesn't rely on format
  const getMonthKey = (month: PickerDateType) => {
    return `${utils.getYear(month)}-${utils.getMonth(month) + 1}`;
  };

  // Configure slots and slot props for DayCalendar
  const dayCalendarSlots = {
    day: DateRangePickerDay,
    ...slots,
  };

  const dayCalendarSlotProps = {
    ...slotProps,
    day: (params) => {
      const { day } = params;

      // Get selection state
      const isSelectedStartDate = checkIsStartOfRange(day);
      const isSelectedEndDate = checkIsEndOfRange(day);
      const isWithinSelectedRange = checkIsWithinRange(day);

      // Calculate dragging state with better logic
      const draggingRange = getDraggingRange();

      const shouldInitDragging = !disableDragEditing && value && value[0] && value[1];
      const isElementDraggable = shouldInitDragging && (isSelectedStartDate || isSelectedEndDate);

      // MUI's highlighting logic - make sure we're correctly identifying start/end days
      const isStartOfHighlighting = isDragging ? isStartOfRange(utils, day, draggingRange) : isSelectedStartDate;
      const isEndOfHighlighting = isDragging ? isEndOfRange(utils, day, draggingRange) : isSelectedEndDate;

      // Preview states
      const inPreview = isWithinPreview(day);
      const isPreviewStart = isStartOfPreview(day);
      const isPreviewEnd = isEndOfPreview(day);

      // Determine day position
      let datePosition: 'start' | 'end' | undefined;
      if (isSelectedStartDate) datePosition = 'start';
      else if (isSelectedEndDate) datePosition = 'end';

      // Fix for single date selection - directly check if the day matches the first date in the range
      const isSingleSelectedDate = value[0] && !value[1] && utils.isSameDay(day, value[0]);

      // Add timestamp for drag operations
      const timestamp = utils.toJsDate(day).valueOf();

      return {
        day,
        // Highlighting applies to all days in the range (background styling)
        isHighlighting: isDragging ? isWithinRange(utils, day, draggingRange) : isWithinSelectedRange,
        isStartOfHighlighting,
        isEndOfHighlighting,
        isPreviewing: inPreview,
        isStartOfPreviewing: isPreviewStart,
        isEndOfPreviewing: isPreviewEnd,
        // Visual selection applies to start/end dates and also to the single selected date
        selected:
          isSelectedStartDate ||
          isSelectedEndDate ||
          isSingleSelectedDate ||
          (isDragging && (isStartOfHighlighting || isEndOfHighlighting)),
        onDaySelect: () => handleDaySelect(day),
        onMouseEnter: () => handleDayMouseEnter(day),
        draggable: isElementDraggable ? true : undefined,
        'data-timestamp': timestamp,
        'data-position': datePosition,
        onDragStart: handleDragStart,
        onDragOver: handleDragOver,
        onDragEnd: handleDragEnd,
        onDrop: handleDrop,
        ...(resolveComponentProps(slotProps?.day, params) ?? {}),
      };
    },
  };

  // Create separate handler for root element drag end to handle type compatibility
  const handleRootDragEnd = useEventCallback((event: React.DragEvent<HTMLDivElement>) => {
    if (!isDragging) return;

    event.preventDefault();

    // Reset drag state
    setIsDragging(false);
    setDragDay(null);
    setDragPosition(null);
  });

  const onMonthChange = (month: PickerDateType) => {
    setStartMonth(month);
  };

  return (
    <div
      className="date-range-calendar-wrapper"
      style={{
        position: 'relative',
        overflow: 'hidden',
        userSelect: 'none',
      }}
      onWheel={handleWheel}
      onTouchStart={variant === 'modal' ? handleTouchStart : undefined}
      onTouchMove={variant === 'modal' ? handleTouchMove : undefined}
      onTouchEnd={variant === 'modal' ? handleTouchEnd : undefined}
      onMouseDown={variant === 'modal' ? handleMouseDown : undefined}
      onMouseMove={variant === 'modal' ? handleMouseMove : undefined}
      onMouseUp={variant === 'modal' ? handleMouseUp : undefined}
      onMouseLeave={handleMouseLeave}
    >
      <CalendarRoot
        ref={ref}
        className={clsx(classes.root, className)}
        onMouseLeave={handleDayMouseLeave}
        onDragEnd={handleRootDragEnd}
        variant={variant}
        direction={animationDirection}
        {...other}
      >
        {months.map((month, index) => (
          <MonthsContainer className={classes.monthContainer} variant={variant} key={index}>
            <RangeCalendarHeader
              month={month}
              monthIndex={index}
              calendars={calendars}
              disabled={disabled}
              readOnly={readOnly}
              disableFuture={disableFuture}
              disablePast={disablePast}
              onMonthChange={onMonthChange}
              maxDate={maxDate}
              minDate={minDate}
              variant={variant}
            />
            <DayCalendarForRange
              key={getMonthKey(month)}
              date={value?.[0] || null}
              selectedDays={value}
              viewDate={month}
              handleDateChange={handleDaySelect}
              disabled={disabled}
              readOnly={readOnly}
              disableFuture={disableFuture}
              displayWeekNumber={false}
              disablePast={disablePast}
              minDate={minDate}
              maxDate={maxDate}
              shouldDisableDate={shouldDisableDate}
              slots={dayCalendarSlots}
              slotProps={dayCalendarSlotProps}
              className={clsx({
                [dateRangeCalendarClasses.dayDragging]: isDragging,
              })}
            />
          </MonthsContainer>
        ))}
      </CalendarRoot>
    </div>
  );
});
