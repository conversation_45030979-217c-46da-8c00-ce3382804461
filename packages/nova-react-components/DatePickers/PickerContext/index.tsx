import * as React from 'react';
import { DateAdapter } from '../models/adapter';
import { PickerDateType } from '../models/pickers';
import { DayjsAdapter } from '../DayjsAdapter';

export interface PickerContextValue {
  utils: DateAdapter;
  defaultDates: {
    minDate: PickerDateType;
    maxDate: PickerDateType;
  };
}

export interface PickerProviderProps<TLocale = any> {
  children: React.ReactNode;
  dateAdapter?: new (...args: any) => DateAdapter<TLocale>;
  //Locale for the date library you are using
  locale?: string;
}

export const PickerContext = React.createContext<PickerContextValue | null>(null);

export function PickerProvider<TLocale>({
  children,
  dateAdapter: DateAdapter = DayjsAdapter,
  locale,
}: PickerProviderProps<TLocale>) {
  // Get parent context if available
  const { utils: parentUtils } = React.useContext(PickerContext) ?? { utils: undefined };

  // Initialize adapter within the component
  const utils = React.useMemo(() => {
    if (!DateAdapter) {
      if (parentUtils) {
        return parentUtils;
      }
      return null;
    }

    // Initialize the adapter with options
    const adapter = new DateAdapter({
      locale: locale,
    });

    return adapter;
  }, [DateAdapter, locale, parentUtils]);

  // Get default min/max dates if not provided
  const defaultDates = React.useMemo(() => {
    if (!utils) {
      return null;
    }

    return {
      minDate: utils.date('1900-01-01T00:00:00.000'),
      maxDate: utils.date('2099-12-31T00:00:00.000'),
    };
  }, [utils]);

  const value = React.useMemo<PickerContextValue>(() => {
    return {
      utils,
      locale,
      defaultDates,
    };
  }, [utils, locale, defaultDates]);

  return <PickerContext.Provider value={value}>{children}</PickerContext.Provider>;
}

export const usePickerContext = () => {
  const context = React.useContext(PickerContext) as PickerContextValue | null;
  if (!context) {
    throw new Error('usePickerContext must be used within a PickerProvider');
  }
  return context;
};
