import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import {
  BaseDateRangePickerProps,
  BaseDateRangePickerSlots,
  BaseDateRangePickerSlotProps,
} from '../DateRangePicker/BaseDateRangePicker.types';
import { PopperPlacementType } from '../../Popper/Popper.types';

/**
 * Docked-specific DateRangePicker props interface
 */
export interface DockedDateRangePickerComponentProps extends BaseDateRangePickerProps {
  /**
   * Position for the popup
   * @default 'bottom-start'
   */
  placement?: PopperPlacementType;

  /**
   * Popper container element
   * @default anchorEl => anchorEl
   */
  container?: React.ReactNode;

  /**
   * If true, the dropdown will display on top of the input
   * @default false
   */
  disablePortal?: boolean;

  /**
   * Component slots and their props
   */
  slots?: BaseDateRangePickerSlots;

  /**
   * Props applied to slot components
   */
  slotProps?: BaseDateRangePickerSlotProps;
}

export interface DockedDateRangePickerTypeMap {
  props: DockedDateRangePickerComponentProps;
  defaultComponent: React.ElementType;
}

/**
 * Docked DateRangePicker component props
 */
export type DockedDateRangePickerProps<D extends React.ElementType = DockedDateRangePickerTypeMap['defaultComponent']> =
  OverrideProps<DockedDateRangePickerTypeMap, D>;

/**
 * Component prop types for Docked DateRangePicker slots
 */
export type DockedDateRangePickerSlotProps = {
  root?: SlotComponentProps<'div', Record<string, unknown>, DockedDateRangePickerComponentProps>;
} & BaseDateRangePickerSlotProps;
