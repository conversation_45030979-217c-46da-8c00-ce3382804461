import { ReactNode } from 'react';
import { TextFieldProps } from '../../TextField';
import { BasePickerProps } from '../types';

/**
 * Props for the DockedDatePicker component
 */
export interface DockedDatePickerProps
  extends Omit<TextFieldProps, 'value' | 'defaultValue' | 'onChange' | 'autoComplete'>,
    BasePickerProps {
  /**
   * Reference to the input element.
   */
  inputRef?: React.Ref<HTMLInputElement>;

  /**
   * Placement of the popup.
   * @default 'bottom-start'
   */
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';

  /**
   * Text for today button.
   * @default 'Today'
   */
  todayText?: string;

  /**
   * Text for clear button.
   * @default 'Clear'
   */
  clearText?: string;

  /**
   * If true, autofocus the calendar on open.
   * @default false
   */
  autoFocus?: boolean;

  /**
   * Text for cancel button.
   * @default 'Cancel'
   */
  cancelText?: string;

  /**
   * Text for apply button.
   * @default 'Apply'
   */
  applyText?: string;
}
