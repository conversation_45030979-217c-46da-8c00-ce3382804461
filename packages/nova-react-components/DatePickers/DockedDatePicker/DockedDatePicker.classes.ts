import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

/**
 * Interface for DockedDatePicker CSS classes
 */
export interface DockedDatePickerClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the root element when disabled. */
  disabled: string;

  /** Styles applied to the root element when error. */
  error: string;

  /** Styles applied to the header element. */
  header: string;

  /** Styles applied to the content area. */
  content: string;

  /** Styles applied to the day view container. */
  dayView: string;

  /** Styles applied to the month view container. */
  monthView: string;

  /** Styles applied to the year view container. */
  yearView: string;

  /** Styles applied to the selected item. */
  selectedItem: string;

  /** Styles applied to the weekday header. */
  weekdayHeader: string;

  /** Styles applied to the calendar grid. */
  calendarGrid: string;

  /** Styles applied to the footer. */
  footer: string;

  /** Styles applied to the calendar container. */
  calendarContainer: string;

  /** Styles applied to the header selection container. */
  headerSelection: string;

  /** Styles applied to the month selector. */
  monthSelector: string;

  /** Styles applied to the year selector. */
  yearSelector: string;

  /** Styles applied to the actions container in footer. */
  actions: string;

  /** Styles applied to action buttons (Clear, Today, etc.) */
  actionButton: string;

  /** Styles applied to the popper container. */
  popper: string;

  /** Styles applied to the text input field. */
  inputField: string;
}

/**
 * Type for DockedDatePicker class keys
 */
export type DockedDatePickerClassKey = keyof DockedDatePickerClasses;

/**
 * Generates a utility class for the DockedDatePicker component
 * @param {string} slot - The class slot name
 * @returns {string} The generated class name
 */
export function getDockedDatePickerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDockedDatePicker', slot);
}

/**
 * Generated utility classes for the DockedDatePicker component
 */
const dockedDatePickerClasses: DockedDatePickerClasses = generateUtilityClasses('NovaDockedDatePicker', [
  // Layout classes
  'root',
  'header',
  'content',
  'dayView',
  'monthView',
  'yearView',
  'listView',
  'selectedItem',
  'weekdayHeader',
  'calendarGrid',
  'footer',
  'calendarContainer',

  // State classes
  'disabled',
  'error',

  // UI Component classes
  'toolbar',
  'viewButton',
  'activeViewButton',
  'headerSelection',
  'monthSelector',
  'yearSelector',
  'actions',
  'actionButton',
  'popper',
  'inputField',
]);

export default dockedDatePickerClasses;
