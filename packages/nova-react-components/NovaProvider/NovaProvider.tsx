'use client';
import * as React from 'react';
import { Locale } from '../Locale';
import LocaleProvider from '../Locale/LocaleProvider';
import RtlProvider from '../RtlProvider/RtlProvider';

export type NovaProviderProps = React.PropsWithChildren<{
  locale?: Locale;
  rtl?: boolean;
}>;

function NovaProvider({ locale, rtl, children }: NovaProviderProps) {
  let childNode = children;
  if (locale) {
    childNode = <LocaleProvider locale={locale}>{childNode}</LocaleProvider>;
  }
  if (rtl) {
    childNode = <RtlProvider rtl>{childNode}</RtlProvider>;
  }
  return childNode;
}

export default NovaProvider;
