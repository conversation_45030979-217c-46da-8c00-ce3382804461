import * as React from 'react';
import StackRoot from '@pigment-css/react/Stack';
import composeClasses from '@mui/utils/composeClasses';
import { OverridableComponent } from '@mui/types';
import clsx from 'clsx';
import { StackProps, StackTypeMap } from './Stack.types';
import { getStackUtilityClass } from './Stack.classes';

const useUtilityClasses = () => {
  const slots = {
    root: ['root'],
  };
  return composeClasses(slots, getStackUtilityClass, {});
};

export const Stack = React.forwardRef(function Stack(props: StackProps, ref: React.Ref<HTMLElement>) {
  const { className, ...rest } = props;
  const classes = useUtilityClasses();
  return <StackRoot ref={ref} className={clsx(classes.root, className)} {...(rest as any)} />;
}) as OverridableComponent<StackTypeMap>;
