import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { Breakpoint } from '@pigment-css/react';
import { SxProps } from '../types/theme';

type CssProperty<T> = T | Array<T> | Partial<Record<Breakpoint, T>>;

export interface StackTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * Defines the `flex-direction` style property.
     * It is applied for all screen sizes.
     * @default 'column'
     */
    direction?: CssProperty<'row' | 'row-reverse' | 'column' | 'column-reverse'>;
    /**
     * Defines the space between immediate children.
     * @default 0
     */
    spacing?: CssProperty<number | string>;
    /**
     * Add an element between each child.
     */
    divider?: React.ReactNode;
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
  };
  defaultComponent: D;
}

export type StackProps<
  D extends React.ElementType = StackTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<StackTypeMap<P, D>, D>;
