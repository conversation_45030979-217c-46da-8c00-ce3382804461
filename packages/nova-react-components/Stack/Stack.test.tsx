import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Stack } from './Stack';

vi.mock('@pigment-css/react/Stack', () => {
  return {
    default: (props) => <div {...props} />,
  };
});

afterEach(() => {
  cleanup();
});

describe('Stack', () => {
  it('should render normal', () => {
    render(<Stack data-testid="NovaStack-root" />);
    expect(screen.getByTestId('NovaStack-root')).toHaveClass('NovaStack-root');
  });
});
