import React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getDataGridUtilityClass } from '../../DataGrid.classes';
import { ColumnType } from '../../types/column.type';
import { clampColumnWidth } from '../../utils';
import { useRtl } from '../../../RtlProvider';

interface HeaderCellResizeHandleProps {
  columnWidth: number;
  column: ColumnType;
  onColumnResize: (column: ColumnType, columnWidth: number, newWidth: number, stopped?: boolean) => void;
  fixed?: 'left' | 'right';
}

type OwnerState = {
  fixed?: 'left' | 'right';
};

const useUtilityClasses = () => {
  const slots = {
    columnResizeContainer: ['columnResizeContainer'],
    columnResizeSlider: ['columnResizeSlider'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const HeaderCellResizeHandleRoot = styled('div')<OwnerState>(({ theme }) => ({
  position: 'absolute',
  overflow: 'hidden',
  display: 'flex',
  alignItems: 'center',
  width: '10px',
  cursor: 'col-resize',
  touchAction: 'none',
  zIndex: 2,
  '--nova-datagrid-resize-color': theme.vars.palette.onBackgroundDisabled,
  ':hover': {
    cursor: 'col-resize',
  },
  ':active': {
    cursor: 'col-resize',
  },
  variants: [
    {
      props: { fixed: 'left' },
      style: {
        left: '1px',
      },
    },
    {
      props: { fixed: 'right' },
      style: {
        right: '-9px',
        left: 'unset',
      },
    },
  ],
}));

const HeaderCellResizeHandleInner = styled('div')(({ theme }) => ({
  width: '1px',
  background: 'var(--nova-datagrid-resize-color)',
  height: '20px',
}));

export const HeaderCellResizeHandle = (props: HeaderCellResizeHandleProps) => {
  const { column, columnWidth, onColumnResize, fixed = 'right' } = props;
  const classes = useUtilityClasses();
  const isRtl = useRtl();
  const onPointerDown = (event: React.PointerEvent<HTMLDivElement>) => {
    if (event.pointerType === 'mouse' && event.buttons !== 1) {
      return;
    }

    // Fix column resizing on a draggable column in FF
    event.preventDefault();

    const { currentTarget, pointerId } = event;
    const headerCell = currentTarget.parentElement!;
    const { left, right } = headerCell.getBoundingClientRect();
    const condition = isRtl ? 'right' : 'left';
    const offset = fixed === condition ? event.clientX - left : right - event.clientX;
    let newWidth = columnWidth;

    function onPointerMove(event: PointerEvent) {
      const { width, left, right } = headerCell.getBoundingClientRect();
      newWidth = fixed === condition ? right - event.clientX - offset : event.clientX + offset - left;
      newWidth = clampColumnWidth(newWidth, column.minWidth || 50, column.maxWidth || Infinity);
      if (width > 0 && newWidth !== width) {
        onColumnResize(column, columnWidth, newWidth);
      }
    }

    function onLostPointerCapture() {
      onColumnResize(column, columnWidth, newWidth, true);
      currentTarget.removeEventListener('pointermove', onPointerMove);
      currentTarget.removeEventListener('lostpointercapture', onLostPointerCapture);
    }

    currentTarget.setPointerCapture(pointerId);
    currentTarget.addEventListener('pointermove', onPointerMove);
    currentTarget.addEventListener('lostpointercapture', onLostPointerCapture);
  };
  return (
    <HeaderCellResizeHandleRoot
      ownerState={{ fixed }}
      className={classes.columnResizeContainer}
      onClick={(event) => {
        event.stopPropagation();
      }}
      onPointerDown={onPointerDown}
    >
      <HeaderCellResizeHandleInner className={classes.columnResizeSlider} />
    </HeaderCellResizeHandleRoot>
  );
};
