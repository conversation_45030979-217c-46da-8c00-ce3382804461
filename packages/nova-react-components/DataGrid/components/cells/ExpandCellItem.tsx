import React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import { getDataGridUtilityClass } from '../../DataGrid.classes';
import { IconButton } from '../../../IconButton';
import KeyboardArrowDown from '../../../internal/svg-icons/KeyboardArrowDown';
import KeyboardArrowRight from '../../../internal/svg-icons/KeyboardArrowRight';

interface ExpandCellItemProps {
  type?: 'header' | 'row';
  showExpandIcon?: boolean;
  expanded?: boolean;
  onExpandClick?: (expanded: boolean, e: React.MouseEvent<HTMLButtonElement>) => void;
  size?: 'small' | 'medium' | 'large';
  colIndex: number;
}

type OwnerState = {
  size?: 'small' | 'medium' | 'large';
};

const useUtilityClasses = () => {
  const slots = {
    expandCell: ['cell', 'expandCell'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const ExpandCellItemRoot = styled('div')<OwnerState>(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flex: '0 0 auto',
  height: 'var(--nova-datagrid-row-height)',
  variants: [
    {
      props: { size: 'small' },
      style: {
        minWidth: '32px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        minWidth: '40px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        minWidth: '40px',
      },
    },
  ],
}));

export const ExpandCellItem = (props: ExpandCellItemProps) => {
  const { type = 'row', expanded = false, onExpandClick, showExpandIcon, size = 'medium', colIndex } = props;
  const classes = useUtilityClasses();
  return (
    <ExpandCellItemRoot
      ownerState={{ size }}
      className={classes.expandCell}
      tabIndex={0}
      data-field={'expand-cell'}
      aria-colindex={colIndex}
      role={type === 'header' ? 'columnheader' : 'gridcell'}
    >
      {showExpandIcon && (
        <IconButton
          variant="neutral"
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            onExpandClick?.(!expanded, e);
          }}
        >
          {expanded ? <KeyboardArrowDown /> : <KeyboardArrowRight />}
        </IconButton>
      )}
    </ExpandCellItemRoot>
  );
};
