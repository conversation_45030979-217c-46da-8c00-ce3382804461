import { useState, useCallback } from 'react';
import { RowId } from '../types/row.type';
import { DataGridProps } from '../DataGrid.types';

const EmptyArray: any[] = [];
export const useExpandedRows = <T>(
  props: Pick<DataGridProps<T>, 'initialState' | 'expandedRows' | 'onExpandedRowsChange'>,
) => {
  const {
    initialState: initialStateProp,
    expandedRows: expandedRowsProp,
    onExpandedRowsChange: onExpandedRowsChangeProp,
  } = props;

  const [expandedRows, setExpandedRows] = useState(expandedRowsProp ?? initialStateProp?.expandedRows ?? EmptyArray);

  const onExpandedRowsChange = useCallback(
    (expandedRows: Array<RowId>) => {
      setExpandedRows(expandedRows);
      onExpandedRowsChangeProp?.(expandedRows);
    },
    [onExpandedRowsChangeProp],
  );

  return {
    expandedRows: expandedRowsProp ?? expandedRows,
    onExpandedRowsChange,
  };
};
