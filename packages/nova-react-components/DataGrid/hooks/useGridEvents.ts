import React, { useCallback, useMemo } from 'react';
import { RowId } from '../types/row.type';
import { CellEventHandlers, CellEventParas, RowEventHandlers, RowEventParas } from '../types/event.type';
import { DataGridProps } from '../DataGrid.types';
import { EditFinishParas, EditingTriggerAction } from '../types/edit.type';

export const useGridEvents = <T>(
  props: Pick<
    DataGridProps<T>,
    | 'rowSelectionMode'
    | 'disableRowSelectionOnClick'
    | 'isRowSelectable'
    | 'selectedRows'
    | 'onSelectedRowsChange'
    | 'cellSelection'
    | 'onSelectedCellsChange'
    | 'editMode'
    | 'editingRows'
    | 'isRowEditable'
    | 'onRowEditStart'
    | 'onRowEditStop'
    | 'editingCells'
    | 'isCellEditable'
    | 'onCellEditStart'
    | 'onCellEditStop'
    | 'cellEvents'
    | 'rowEvents'
  > & {
    startEditing: (params: { rowId: RowId; columnField?: string }) => void;
    stopEditing: (params: {
      rowId: RowId;
      columnField?: string;
      saveChanges?: boolean;
      originalRow: T;
      trigger: EditingTriggerAction;
      onCellEditStop?: DataGridProps<T>['onCellEditStop'];
      onRowEditStop?: DataGridProps<T>['onRowEditStop'];
    }) => Promise<T>;
  },
) => {
  const {
    rowSelectionMode,
    disableRowSelectionOnClick,
    isRowSelectable,
    selectedRows = [],
    onSelectedRowsChange,
    onSelectedCellsChange,
    cellSelection,
    editMode,
    editingRows = [],
    isRowEditable,
    onRowEditStart,
    onRowEditStop,
    editingCells = [],
    isCellEditable,
    onCellEditStart,
    onCellEditStop,
    startEditing,
    stopEditing,
    cellEvents: cellEventsProp,
    rowEvents: rowEventsProp,
  } = props;

  const handleRowClick = useCallback(
    (paras: RowEventParas<T>, event: React.MouseEvent<HTMLDivElement>) => {
      const { rowId, rowData } = paras;

      if (isRowSelectable && !isRowSelectable(rowData)) return;

      let newSelectedRows: RowId[];
      if (rowSelectionMode === 'radioSelection') {
        newSelectedRows = [rowId];
      } else {
        newSelectedRows = selectedRows.includes(rowId)
          ? selectedRows.filter((id) => id !== rowId)
          : [...selectedRows, rowId];
      }

      onSelectedRowsChange?.(newSelectedRows);
    },
    [rowSelectionMode, isRowSelectable, onSelectedRowsChange, selectedRows],
  );

  const handleRowDoubleClick = useCallback(
    (paras: RowEventParas<T>, event: React.MouseEvent<HTMLDivElement>) => {
      const { rowId, rowData } = paras;
      if (editMode === 'row' && (isRowEditable ? isRowEditable(rowData) : true) && editingRows.length === 0) {
        startEditing({ rowId });
        onRowEditStart?.(rowData);
      }
    },
    [editMode, editingRows, isRowEditable, onRowEditStart, startEditing],
  );

  const handleCellClick = useCallback(
    (paras: CellEventParas<T>, event: React.MouseEvent<HTMLDivElement>) => {
      onSelectedCellsChange?.([{ rowId: paras.rowId, columnField: paras.columnField }]);
    },
    [onSelectedCellsChange],
  );

  const handleCellKeyDown = useCallback(
    async (paras: CellEventParas<T>, event: React.KeyboardEvent<HTMLDivElement>) => {
      const { rowId, columnField, column, rowData } = paras;
      let editable = column.editable ?? false;
      let isEditing = false;
      if (editMode === 'cell') {
        if (isCellEditable) {
          editable = editable && isCellEditable(paras);
        }
        isEditing = editingCells.some((c) => c.rowId === rowId && c.columnField === columnField);
      } else if (editMode === 'row') {
        editable = isRowEditable ? isRowEditable(rowData) : true;
        isEditing = editingRows.includes(rowId);
      }

      if (editable) {
        if (isEditing) {
          switch (event.key) {
            case 'Escape':
              await stopEditing({
                rowId,
                columnField,
                originalRow: rowData,
                saveChanges: false,
                trigger: event.key,
                onCellEditStop,
                onRowEditStop,
              });
              break;
            case 'Enter':
            case 'Tab':
              if (editMode === 'row' && event.key === 'Tab') {
                break;
              } else {
                await stopEditing({
                  rowId,
                  columnField,
                  originalRow: rowData,
                  saveChanges: true,
                  trigger: event.key,
                  onCellEditStop,
                  onRowEditStop,
                });
                break;
              }
          }
        }
      }
    },
    [editMode, isCellEditable, editingCells, stopEditing, onCellEditStop, isRowEditable, editingRows, onRowEditStop],
  );

  const handleCellDoubleClick = useCallback(
    (paras: CellEventParas<T>, event: React.MouseEvent<HTMLDivElement>) => {
      const { rowId, columnField, column, rowData } = paras;
      let cellEditable = column.editable ?? false;
      if (isCellEditable) {
        cellEditable = cellEditable && isCellEditable(paras);
      }
      if (editMode === 'cell' && cellEditable && editingCells.length === 0) {
        startEditing({ rowId, columnField });
        onCellEditStart?.({ rowId, columnField });
      } else if (
        editMode === 'row' &&
        cellEditable &&
        (isRowEditable ? isRowEditable(rowData) : true) &&
        editingRows.length === 0
      ) {
        startEditing({ rowId, columnField });
        onRowEditStart?.(rowData);
        event.stopPropagation();
      }
    },
    [editMode, editingCells, editingRows, isCellEditable, isRowEditable, onCellEditStart, onRowEditStart, startEditing],
  );

  const onFinishEditing = useCallback(
    async (paras: EditFinishParas<T>) => {
      const { rowId, columnField, originalRow, saveChanges, trigger } = paras;
      await stopEditing({
        rowId,
        columnField,
        originalRow,
        saveChanges,
        trigger,
        onCellEditStop,
        onRowEditStop,
      });
    },
    [onCellEditStop, onRowEditStop, stopEditing],
  );

  const rowEvents: RowEventHandlers<T> = useMemo(
    () => ({
      ...rowEventsProp,
      onClick: (paras, e) => {
        if (rowSelectionMode && !disableRowSelectionOnClick) {
          handleRowClick(paras, e);
        }
        rowEventsProp?.onClick?.(paras, e);
      },
      onKeyDown: (paras, e) => {
        rowEventsProp?.onKeyDown?.(paras, e);
      },
      onDoubleClick: (paras, e) => {
        handleRowDoubleClick(paras, e);
        rowEventsProp?.onDoubleClick?.(paras, e);
      },
    }),
    [disableRowSelectionOnClick, handleRowClick, handleRowDoubleClick, rowEventsProp, rowSelectionMode],
  );

  const cellEvents: CellEventHandlers<T> = useMemo(
    () => ({
      ...cellEventsProp,
      onClick: (paras, e) => {
        if (cellSelection) handleCellClick(paras, e);
        cellEventsProp?.onClick?.(paras, e);
      },
      onKeyDown: (paras, e) => {
        handleCellKeyDown(paras, e);
        cellEventsProp?.onKeyDown?.(paras, e);
      },
      onDoubleClick: (paras, e) => {
        handleCellDoubleClick(paras, e);
        cellEventsProp?.onDoubleClick?.(paras, e);
      },
    }),
    [cellEventsProp, cellSelection, handleCellClick, handleCellDoubleClick, handleCellKeyDown],
  );

  return {
    rowEvents,
    cellEvents,
    onFinishEditing,
  };
};
