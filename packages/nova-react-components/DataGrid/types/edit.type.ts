import { RowId } from './row.type';

export type EditingTriggerAction = 'Enter' | 'Tab' | 'Escape' | 'ClickAway' | string;

export type CellStopEditParas<T = any> = {
  rowId: RowId;
  columnField: string;
  newRow: T;
  originalRow: T;
  trigger: EditingTriggerAction;
};

export type RowStopEditParas<T = any> = {
  rowId: RowId;
  newRow: T;
  originalRow: T;
  trigger: EditingTriggerAction;
};

export type CellEditParas<T = any> = {
  value: any;
  onValueChange: (value: any) => void;
  originalRow: T;
  autoFocus: boolean;
  cellRef: React.MutableRefObject<HTMLElement | undefined>;
  columnWidth: number;
  onFinishEditing: (paras: EditFinishParas<T>) => void;
};

export type EditFinishParas<T = any> = {
  rowId: RowId;
  columnField?: string;
  originalRow: T;
  saveChanges: boolean;
  trigger: EditingTriggerAction;
};
