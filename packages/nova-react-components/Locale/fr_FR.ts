import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'fr-FR',
  Autocomplete: {
    clearText: 'Effacer',
    closeText: 'Fermer',
    loadingText: 'Chargement…',
    noOptionsText: 'Aucune option',
    openText: 'Ouvrir',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Aller à '}la page ${page}`;
      }
      if (type === 'first') {
        return `Aller à la première page`;
      }
      if (type === 'last') {
        return `Aller à la dernière page`;
      }
      if (type === 'next') {
        return `Aller à la page suivante`;
      }

      return `Aller à la page précédente`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Aller à la première page`;
      }
      if (type === 'last') {
        return `Aller à la dernière page`;
      }
      if (type === 'next') {
        return `Aller à la page suivante`;
      }
      return `Aller à la page précédente`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Page ${page + 1} sur ${totalPages}`;
    },
    labelRowsPerPage: 'Lignes par page :',
  },
};

export default locale;
