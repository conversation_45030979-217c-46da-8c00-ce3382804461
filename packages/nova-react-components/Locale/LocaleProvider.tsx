'use client';
import * as React from 'react';
import { Locale } from '.';
import enUS from './en_US';

type LocaleProviderProps = React.PropsWithChildren<{
  locale?: Locale;
}>;

export type LocaleComponentName = Exclude<keyof Locale, 'locale'>;

const LocaleContext = React.createContext({
  locale: enUS,
});

function LocaleProvider({ locale, children }: LocaleProviderProps) {
  return <LocaleContext.Provider value={{ locale: locale ?? enUS }}>{children}</LocaleContext.Provider>;
}

export const useLocale = <C extends LocaleComponentName = LocaleComponentName>(componentName?: C) => {
  const { locale } = React.useContext(LocaleContext);
  if (componentName) {
    return locale[componentName] || {};
  } else {
    return locale;
  }
};

export const useLanguage = () => {
  const { locale } = React.useContext(LocaleContext);
  return locale.locale;
};

export default LocaleProvider;
