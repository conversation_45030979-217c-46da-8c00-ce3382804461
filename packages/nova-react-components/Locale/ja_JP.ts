import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'ja-JP',
  Autocomplete: {
    clearText: 'クリア',
    closeText: '閉じる',
    loadingText: '読み込み中…',
    noOptionsText: 'オプションがありません',
    openText: '開く',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'ページ '} ${page} に移動`;
      }
      if (type === 'first') {
        return `最初のページに移動`;
      }
      if (type === 'last') {
        return `最後のページに移動`;
      }
      if (type === 'next') {
        return `次のページに移動`;
      }

      return `前のページに移動`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `最初のページに移動`;
      }
      if (type === 'last') {
        return `最後のページに移動`;
      }
      if (type === 'next') {
        return `次のページに移動`;
      }
      return `前のページに移動`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `ページ ${page + 1} / ${totalPages}`;
    },
    labelRowsPerPage: 'ページあたりのアイテム数:',
  },
};

export default locale;
