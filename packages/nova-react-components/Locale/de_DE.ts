import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'de-DE',
  Autocomplete: {
    clearText: 'Löschen',
    closeText: 'Schl<PERSON>ßen',
    loadingText: '<PERSON>ädt…',
    noOptionsText: 'Keine Optionen',
    openText: 'Öffnen',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Gehe zu '}Seite ${page}`;
      }
      if (type === 'first') {
        return `Gehe zu erster Seite`;
      }
      if (type === 'last') {
        return `Gehe zu letzter Seite`;
      }
      if (type === 'next') {
        return `Gehe zu nächster Seite`;
      }

      return `Gehe zu vorheriger Seite`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Gehe zu erster Seite`;
      }
      if (type === 'last') {
        return `Gehe zu letzter Seite`;
      }
      if (type === 'next') {
        return `Gehe zu nächster Seite`;
      }
      return `Gehe zu vorheriger Seite`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Seite ${page + 1} von ${totalPages}`;
    },
    labelRowsPerPage: 'Einträge pro Seite:',
  },
};

export default locale;
