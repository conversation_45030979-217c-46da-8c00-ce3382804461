import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'nl-NL',
  Autocomplete: {
    clearText: 'Wissen',
    closeText: 'Sluiten',
    loadingText: 'Laden…',
    noOptionsText: 'Geen opties',
    openText: 'Openen',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Ga naar '}pagina ${page}`;
      }
      if (type === 'first') {
        return `Ga naar eerste pagina`;
      }
      if (type === 'last') {
        return `Ga naar laatste pagina`;
      }
      if (type === 'next') {
        return `Ga naar volgende pagina`;
      }

      return `Ga naar vorige pagina`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Ga naar eerste pagina`;
      }
      if (type === 'last') {
        return `Ga naar laatste pagina`;
      }
      if (type === 'next') {
        return `Ga naar volgende pagina`;
      }
      return `Ga naar vorige pagina`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Pagina ${page + 1} van ${totalPages}`;
    },
    labelRowsPerPage: 'Items per pagina:',
  },
};

export default locale;
