import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'ru-RU',
  Autocomplete: {
    clearText: 'Очистить',
    closeText: 'Закрыть',
    loadingText: 'Загрузка…',
    noOptionsText: 'Нет вариантов',
    openText: 'Открыть',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Перейти к '}странице ${page}`;
      }
      if (type === 'first') {
        return `Перейти на первую страницу`;
      }
      if (type === 'last') {
        return `Перейти на последнюю страницу`;
      }
      if (type === 'next') {
        return `Перейти на следующую страницу`;
      }

      return `Перейти на предыдущую страницу`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Перейти на первую страницу`;
      }
      if (type === 'last') {
        return `Перейти на последнюю страницу`;
      }
      if (type === 'next') {
        return `Перейти на следующую страницу`;
      }
      return `Перейти на предыдущую страницу`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Страница ${page + 1} из ${totalPages}`;
    },
    labelRowsPerPage: 'Элементов на странице:',
  },
};

export default locale;
