import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'sk-SK',
  Autocomplete: {
    clearText: 'Vymazať',
    closeText: 'Zavrie<PERSON>',
    loadingText: 'Na<PERSON><PERSON>tavam…',
    noOptionsText: '<PERSON>ia<PERSON>e možnosti',
    openText: 'Otvoriť',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Prejdite na '}stranu ${page}`;
      }
      if (type === 'first') {
        return `Prejdite na prvú stranu`;
      }
      if (type === 'last') {
        return `Prejdite na poslednú stranu`;
      }
      if (type === 'next') {
        return `Prejdite na ďalšiu stranu`;
      }

      return `Prejdite na predchádza<PERSON><PERSON><PERSON> stranu`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Prejdite na prvú stranu`;
      }
      if (type === 'last') {
        return `Prejdite na poslednú stranu`;
      }
      if (type === 'next') {
        return `Prejdite na ďalšiu stranu`;
      }
      return `Prejdite na predchádzajúcu stranu`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Strana ${page + 1} z ${totalPages}`;
    },
    labelRowsPerPage: 'Položiek na stranu:',
  },
};

export default locale;
