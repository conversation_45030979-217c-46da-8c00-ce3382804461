import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'da-DK',
  Autocomplete: {
    clearText: 'Ryd',
    closeText: 'Luk',
    loadingText: 'Indlæser…',
    noOptionsText: 'Ingen muligheder',
    openText: 'Åbn',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Gå til '}side ${page}`;
      }
      if (type === 'first') {
        return `Gå til første side`;
      }
      if (type === 'last') {
        return `Gå til sidste side`;
      }
      if (type === 'next') {
        return `Gå til næste side`;
      }

      return `Gå til forrige side`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Gå til første side`;
      }
      if (type === 'last') {
        return `Gå til sidste side`;
      }
      if (type === 'next') {
        return `Gå til næste side`;
      }
      return `Gå til forrige side`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Side ${page + 1} af ${totalPages}`;
    },
    labelRowsPerPage: 'Elementer pr. side:',
  },
};

export default locale;
