import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'it-IT',
  Autocomplete: {
    clearText: 'Cancella',
    closeText: '<PERSON><PERSON>',
    loadingText: 'Caricamento…',
    noOptionsText: 'Nessuna opzione',
    openText: 'Apri',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Vai a '}pagina ${page}`;
      }
      if (type === 'first') {
        return `Vai alla prima pagina`;
      }
      if (type === 'last') {
        return `Vai all'ultima pagina`;
      }
      if (type === 'next') {
        return `Vai alla pagina successiva`;
      }

      return `Vai alla pagina precedente`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Vai alla prima pagina`;
      }
      if (type === 'last') {
        return `Vai all'ultima pagina`;
      }
      if (type === 'next') {
        return `Vai alla pagina successiva`;
      }
      return `Vai alla pagina precedente`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Pagina ${page + 1} di ${totalPages}`;
    },
    labelRowsPerPage: 'Elementi per pagina:',
  },
};

export default locale;
