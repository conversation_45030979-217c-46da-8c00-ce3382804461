import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'es-ES',
  Autocomplete: {
    clearText: 'Borrar',
    closeText: 'Cerrar',
    loadingText: 'Cargando…',
    noOptionsText: 'Sin opciones',
    openText: 'Abrir',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Ir a '}página ${page}`;
      }
      if (type === 'first') {
        return `Ir a la primera página`;
      }
      if (type === 'last') {
        return `Ir a la última página`;
      }
      if (type === 'next') {
        return `Ir a la siguiente página`;
      }

      return `Ir a la página anterior`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Ir a la primera página`;
      }
      if (type === 'last') {
        return `Ir a la última página`;
      }
      if (type === 'next') {
        return `Ir a la siguiente página`;
      }
      return `Ir a la página anterior`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Página ${page + 1} de ${totalPages}`;
    },
    labelRowsPerPage: 'Elementos por página:',
  },
};

export default locale;
