import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'ar-EG',
  Autocomplete: {
    clearText: 'مسح',
    closeText: 'إغلاق',
    loadingText: 'جارٍ التحميل…',
    noOptionsText: 'لا توجد خيارات',
    openText: 'فتح',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'اذهب إلى '}الصفحة ${page}`;
      }
      if (type === 'first') {
        return `اذهب إلى الصفحة الأولى`;
      }
      if (type === 'last') {
        return `اذهب إلى الصفحة الأخيرة`;
      }
      if (type === 'next') {
        return `اذهب إلى الصفحة التالية`;
      }

      return `اذهب إلى الصفحة السابقة`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `اذهب إلى الصفحة الأولى`;
      }
      if (type === 'last') {
        return `اذهب إلى الصفحة الأخيرة`;
      }
      if (type === 'next') {
        return `اذهب إلى الصفحة التالية`;
      }
      return `اذهب إلى الصفحة السابقة`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `الصفحة ${page + 1} من ${totalPages}`;
    },
    labelRowsPerPage: 'العناصر في الصفحة:',
  },
};

export default locale;
