'use client';
import * as React from 'react';
import setRef from '@mui/utils/setRef';
import useEventCallback from '@mui/utils/useEventCallback';
import useControlled from '@mui/utils/useControlled';
import useId from '@mui/utils/useId';
import usePreviousProps from '@mui/utils/usePreviousProps';
import type {
  UseAutocompleteProps,
  UseAutocompleteReturnValue,
  AutocompleteValue,
  AutocompleteChangeReason,
  AutocompleteChangeDetails,
  AutocompleteHighlightChangeReason,
  AutocompleteInputChangeReason,
  CreateFilterOptionsConfig,
  AutocompleteGetItemProps,
} from './useAutocomplete.types';

function stripDiacritics(string: string): string {
  return string.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

export function createFilterOptions<Value>(config: CreateFilterOptionsConfig<Value> = {}) {
  const { ignoreAccents = true, ignoreCase = true, limit, matchFrom = 'any', stringify, trim = false } = config;

  return (
    options: any[],
    { inputValue, getOptionLabel }: { inputValue: string; getOptionLabel: (option: Value) => string },
  ) => {
    let input = trim ? inputValue.trim() : inputValue;
    if (ignoreCase) {
      input = input.toLowerCase();
    }
    if (ignoreAccents) {
      input = stripDiacritics(input);
    }

    const filteredOptions = !input
      ? options
      : options.filter((option) => {
          let candidate = (stringify || getOptionLabel)(option);
          if (ignoreCase) {
            candidate = candidate.toLowerCase();
          }
          if (ignoreAccents) {
            candidate = stripDiacritics(candidate);
          }

          return matchFrom === 'start' ? candidate.startsWith(input) : candidate.includes(input);
        });

    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;
  };
}

const defaultFilterOptions = createFilterOptions();

const pageSize = 5;

const defaultIsActiveElementInListbox = (listboxRef: React.RefObject<HTMLElement | null>) =>
  listboxRef.current !== null && listboxRef.current.parentElement?.contains(document.activeElement);

const MULTIPLE_DEFAULT_VALUE: never[] = [];

function getInputValue<Value>(
  value: Value | null,
  multiple: boolean,
  getOptionLabel: (option: Value) => string,
  renderValue?: any,
): string {
  if (multiple || value == null || renderValue) {
    return '';
  }
  const optionLabel = getOptionLabel(value);
  return typeof optionLabel === 'string' ? optionLabel : '';
}

function useAutocomplete<
  Value,
  Multiple extends boolean | undefined = false,
  DisableClearable extends boolean | undefined = false,
  FreeSolo extends boolean | undefined = false,
>(
  props: UseAutocompleteProps<Value, Multiple, DisableClearable, FreeSolo>,
): UseAutocompleteReturnValue<Value, Multiple, DisableClearable, FreeSolo> {
  const {
    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,
    unstable_classNamePrefix = 'Mui',
    autoComplete = false,
    autoHighlight = false,
    autoSelect = false,
    blurOnSelect = false,
    clearOnBlur = !props.freeSolo,
    clearOnEscape = false,
    componentName = 'useAutocomplete',
    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,
    disableClearable = false,
    disableCloseOnSelect = false,
    disabled: disabledProp,
    disabledItemsFocusable = false,
    disableListWrap = false,
    filterOptions = defaultFilterOptions,
    filterSelectedOptions = false,
    freeSolo = false,
    getOptionDisabled,
    getOptionKey,
    getOptionLabel: getOptionLabelProp = (option: any) => option.label ?? option,
    groupBy,
    handleHomeEndKeys = !props.freeSolo,
    id: idProp,
    includeInputInList = false,
    inputValue: inputValueProp,
    isOptionEqualToValue = (option: Value, value: Value) => option === value,
    multiple = false,
    onChange,
    onClose,
    onHighlightChange,
    onInputChange,
    onOpen,
    open: openProp,
    openOnFocus = false,
    options,
    readOnly = false,
    renderValue,
    selectOnFocus = !props.freeSolo,
    value: valueProp,
  } = props;

  const id = useId(idProp);

  let getOptionLabel = getOptionLabelProp;

  getOptionLabel = (option: Value) => {
    const optionLabel = getOptionLabelProp(option);
    if (typeof optionLabel !== 'string') {
      if (process.env.NODE_ENV !== 'production') {
        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;
        console.error(
          `The \`getOptionLabel\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(
            option,
          )}.`,
        );
      }
      return String(optionLabel);
    }
    return optionLabel;
  };

  const ignoreFocus = React.useRef(false);
  const firstFocus = React.useRef(true);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const listboxRef = React.useRef<HTMLUListElement>(null);
  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);
  const handleSetAnchorEl = React.useCallback((el: HTMLElement | null) => {
    setAnchorEl(el);
  }, []);

  const [focusedItem, setFocusedItem] = React.useState(-1);
  const defaultHighlighted = autoHighlight ? 0 : -1;
  const highlightedIndexRef = React.useRef(defaultHighlighted);

  const initialInputValue = React.useRef(
    getInputValue(defaultValue ?? valueProp, multiple, getOptionLabel, renderValue),
  ).current;

  const [value, setValueState] = useControlled<AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>>({
    controlled: valueProp,
    default: defaultValue as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>,
    name: componentName,
  });
  const [inputValue, setInputValueState] = useControlled({
    controlled: inputValueProp,
    default: initialInputValue,
    name: componentName,
    state: 'inputValue',
  });

  const [focused, setFocused] = React.useState(false);

  const resetInputValue = React.useCallback(
    (
      event: React.SyntheticEvent,
      newValue: AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>,
      reason: AutocompleteInputChangeReason,
    ) => {
      const isOptionSelected = multiple
        ? Array.isArray(value) && Array.isArray(newValue) && value.length < newValue.length
        : newValue !== null;
      if (!isOptionSelected && !clearOnBlur) {
        return;
      }
      const newInputValue = getInputValue(newValue, multiple, getOptionLabel, renderValue);

      if (inputValue === newInputValue) {
        return;
      }

      setInputValueState(newInputValue);

      if (onInputChange) {
        onInputChange(event, newInputValue, reason);
      }
    },
    [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value, renderValue],
  );

  const [open, setOpenState] = useControlled({
    controlled: openProp,
    default: false,
    name: componentName,
    state: 'open',
  });

  const [inputPristine, setInputPristine] = React.useState(true);

  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);

  const popupOpen = open && !readOnly;

  const filteredOptions = popupOpen
    ? filterOptions(
        options.filter((option) => {
          if (
            filterSelectedOptions &&
            (multiple
              ? Array.isArray(value) && Array.isArray(option) && value.length < option.length
              : value !== null) &&
            isOptionEqualToValue(option, value as Value)
          ) {
            return false;
          }
          return true;
        }),
        {
          inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,
          getOptionLabel,
        },
      )
    : [];

  const previousProps = usePreviousProps({
    filteredOptions,
    value,
    inputValue,
  });

  React.useEffect(() => {
    const valueChange = value !== previousProps.value;

    if (focused && !valueChange) {
      return;
    }

    if (freeSolo && !valueChange) {
      return;
    }

    resetInputValue({} as React.SyntheticEvent, value, 'reset');
  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);

  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;

  const focusItem = useEventCallback((itemToFocus: number) => {
    if (itemToFocus === -1) {
      inputRef.current?.focus();
    } else {
      const indexType = renderValue ? 'data-item-index' : 'data-tag-index';
      const element = anchorEl?.querySelector(`[${indexType}="${itemToFocus}"]`);
      if (element) {
        (element as HTMLElement).focus();
      }
    }
  });

  React.useEffect(() => {
    if (multiple && Array.isArray(value) && focusedItem > value.length - 1) {
      setFocusedItem(-1);
      focusItem(-1);
    }
  }, [value, multiple, focusedItem, focusItem]);

  function validOptionIndex(index: number, direction: 'next' | 'previous'): number {
    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {
      return -1;
    }

    let nextFocus = index;

    // eslint-disable-next-line no-constant-condition
    while (true) {
      const option = listboxRef.current.querySelector(`[data-option-index="${nextFocus}"]`) as HTMLElement;

      const nextFocusDisabled = disabledItemsFocusable
        ? false
        : !option ||
          (option as HTMLInputElement).disabled ||
          (option as HTMLLIElement).getAttribute('aria-disabled') === 'true';

      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {
        return nextFocus;
      }

      if (direction === 'next') {
        nextFocus = (nextFocus + 1) % filteredOptions.length;
      } else {
        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;
      }

      if (nextFocus === index) {
        return -1;
      }
    }
  }

  const setHighlightedIndex = useEventCallback(
    ({
      event,
      index,
      reason,
    }: {
      event: React.SyntheticEvent;
      index: number;
      reason: AutocompleteHighlightChangeReason;
    }) => {
      highlightedIndexRef.current = index;

      if (index === -1) {
        inputRef.current?.removeAttribute('aria-activedescendant');
      } else {
        inputRef.current?.setAttribute('aria-activedescendant', `${id}-option-${index}`);
      }

      if (onHighlightChange && ['mouse', 'keyboard', 'touch'].includes(reason as string)) {
        onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);
      }

      if (!listboxRef.current) {
        return;
      }

      const prev = listboxRef.current.querySelector(`[role="option"].${unstable_classNamePrefix}-focused`);
      if (prev) {
        prev.classList.remove(`${unstable_classNamePrefix}-focused`);
        prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);
      }

      let listboxNode = listboxRef.current;
      if (!['listbox', 'menu'].includes(listboxRef.current.getAttribute('role') as string)) {
        const role = listboxRef.current.getAttribute('role') === 'menu' ? 'menu' : 'listbox';
        const parentElement = listboxRef.current.parentElement;
        if (!parentElement) {
          return;
        }
        listboxNode = parentElement.querySelector(`[role="${role}"]`) as HTMLUListElement;
      }

      if (!listboxNode) {
        return;
      }

      if (index === -1) {
        listboxNode.scrollTop = 0;
        return;
      }

      const option = listboxRef.current.querySelector(`[data-option-index="${index}"]`);

      if (!option) {
        return;
      }

      option.classList.add(`${unstable_classNamePrefix}-focused`);
      if (reason === 'keyboard') {
        option.classList.add(`${unstable_classNamePrefix}-focusVisible`);
      }

      if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {
        const element = option as HTMLElement;

        const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;
        const elementBottom = element.offsetTop + element.offsetHeight;
        if (elementBottom > scrollBottom) {
          listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;
        } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {
          listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);
        }
      }
    },
  );

  const changeHighlightedIndex = useEventCallback(
    ({
      event,
      diff,
      direction = 'next',
      reason,
    }: {
      event: React.SyntheticEvent;
      diff: number | 'reset' | 'start' | 'end';
      direction?: 'next' | 'previous';
      reason: AutocompleteHighlightChangeReason;
    }) => {
      if (!popupOpen) {
        return;
      }

      const getNextIndex = () => {
        const maxIndex = filteredOptions.length - 1;

        if (diff === 'reset') {
          return defaultHighlighted;
        }

        if (diff === 'start') {
          return 0;
        }

        if (diff === 'end') {
          return maxIndex;
        }

        const newIndex = highlightedIndexRef.current + diff;

        if (newIndex < 0) {
          if (newIndex === -1 && includeInputInList) {
            return -1;
          }

          if ((disableListWrap && highlightedIndexRef.current !== -1) || Math.abs(diff) > 1) {
            return 0;
          }

          return maxIndex;
        }

        if (newIndex > maxIndex) {
          if (newIndex === maxIndex + 1 && includeInputInList) {
            return -1;
          }

          if (disableListWrap || Math.abs(diff) > 1) {
            return maxIndex;
          }

          return 0;
        }

        return newIndex;
      };

      const nextIndex = validOptionIndex(getNextIndex(), direction);
      setHighlightedIndex({ index: nextIndex, reason, event });

      if (autoComplete && diff !== 'reset') {
        if (nextIndex === -1) {
          inputRef.current!.value = inputValue;
        } else {
          const option = getOptionLabel(filteredOptions[nextIndex]);
          inputRef.current!.value = option;

          const index = option.toLowerCase().indexOf(inputValue.toLowerCase());
          if (index === 0 && inputValue.length > 0) {
            inputRef.current!.setSelectionRange(inputValue.length, option.length);
          }
        }
      }
    },
  );

  const getPreviousHighlightedOptionIndex = () => {
    const isSameValue = (value1: Value | null, value2: Value | null) => {
      const label1 = value1 ? getOptionLabel(value1) : '';
      const label2 = value2 ? getOptionLabel(value2) : '';
      return label1 === label2;
    };

    if (
      highlightedIndexRef.current !== -1 &&
      previousProps.filteredOptions &&
      previousProps.filteredOptions.length !== filteredOptions.length &&
      previousProps.inputValue === inputValue &&
      (multiple
        ? Array.isArray(value) &&
          Array.isArray(previousProps.value) &&
          value.length === previousProps.value.length &&
          previousProps.value.every((val: Value, i: number) => getOptionLabel(value[i]) === getOptionLabel(val))
        : isSameValue(previousProps.value as Value, value as Value))
    ) {
      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];

      if (previousHighlightedOption) {
        return filteredOptions.findIndex((option: Value) => {
          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);
        });
      }
    }
    return -1;
  };

  const syncHighlightedIndex = React.useCallback(() => {
    if (!popupOpen) {
      return;
    }

    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();
    if (previousHighlightedOptionIndex !== -1) {
      highlightedIndexRef.current = previousHighlightedOptionIndex;
      return;
    }

    const valueItem = multiple && Array.isArray(value) ? value[0] : value;

    if (filteredOptions.length === 0 || valueItem == null) {
      changeHighlightedIndex({ diff: 'reset', event: {} as React.SyntheticEvent, reason: 'keyboard' });
      return;
    }

    if (!listboxRef.current) {
      return;
    }

    if (valueItem != null) {
      const currentOption = filteredOptions[highlightedIndexRef.current];

      if (
        multiple &&
        currentOption &&
        Array.isArray(value) &&
        value.findIndex((val: Value) => isOptionEqualToValue(currentOption, val)) !== -1
      ) {
        return;
      }

      const itemIndex = filteredOptions.findIndex((optionItem) => isOptionEqualToValue(optionItem, valueItem));
      if (itemIndex === -1) {
        changeHighlightedIndex({ diff: 'reset', event: {} as React.SyntheticEvent, reason: 'keyboard' });
      } else {
        setHighlightedIndex({ index: itemIndex, reason: 'keyboard', event: {} as React.SyntheticEvent });
      }
      return;
    }

    if (highlightedIndexRef.current >= filteredOptions.length - 1) {
      setHighlightedIndex({ index: filteredOptions.length - 1, reason: 'keyboard', event: {} as React.SyntheticEvent });
      return;
    }

    setHighlightedIndex({ index: highlightedIndexRef.current, reason: 'keyboard', event: {} as React.SyntheticEvent });
  }, [
    filteredOptions.length,
    multiple ? false : value,
    filterSelectedOptions,
    changeHighlightedIndex,
    setHighlightedIndex,
    popupOpen,
    inputValue,
    multiple,
  ]);

  const handleListboxRef = useEventCallback((node: HTMLUListElement | null) => {
    setRef(listboxRef, node);

    if (!node) {
      return;
    }

    syncHighlightedIndex();
  });

  React.useEffect(() => {
    syncHighlightedIndex();
  }, [syncHighlightedIndex]);

  const handleOpen = (event: React.SyntheticEvent) => {
    if (open) {
      return;
    }

    setOpenState(true);
    setInputPristine(true);

    if (onOpen) {
      onOpen(event);
    }
  };

  const handleClose = (event: React.SyntheticEvent, reason: AutocompleteChangeReason) => {
    if (!open) {
      return;
    }

    setOpenState(false);

    if (onClose) {
      onClose(event, reason);
    }
  };

  const handleValue = (
    event: React.SyntheticEvent,
    newValue: AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo> | null,
    reason: AutocompleteChangeReason,
    details?: AutocompleteChangeDetails<Value>,
  ) => {
    if (multiple) {
      if (
        Array.isArray(value) &&
        Array.isArray(newValue) &&
        value.length === newValue.length &&
        value.every((val: Value, i: number) => val === newValue[i])
      ) {
        return;
      }
    } else if (value === newValue) {
      return;
    }

    if (onChange && newValue) {
      onChange(event, newValue, reason, details);
    }

    setValueState(newValue as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>);
  };

  const isTouch = React.useRef(false);

  const selectNewValue = (
    event: React.SyntheticEvent,
    option: AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>,
    reasonProp: AutocompleteChangeReason = 'selectOption',
    origin: 'options' | 'freeSolo' = 'options',
  ) => {
    let reason = reasonProp;
    let newValue = option;

    if (multiple) {
      const newValueArray = Array.isArray(value) ? value.slice() : [];
      newValue = newValueArray as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>;

      if (process.env.NODE_ENV !== 'production') {
        const matches = newValueArray.filter((val) => isOptionEqualToValue(option as Value, val as Value));

        if (matches.length > 1) {
          console.error(
            [
              `The \`isOptionEqualToValue\` method of ${componentName} does not handle the arguments correctly.`,
              `The component expects a single value to match a given option but found ${matches.length} matches.`,
            ].join('\n'),
          );
        }
      }

      const itemIndex = newValueArray.findIndex((valueItem) =>
        isOptionEqualToValue(option as Value, valueItem as Value),
      );

      if (itemIndex === -1) {
        newValueArray.push(option as Value);
      } else if (origin !== 'freeSolo') {
        newValueArray.splice(itemIndex, 1);
        reason = 'removeOption';
      }
      newValue = newValueArray as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>;
    }

    resetInputValue(
      {} as React.SyntheticEvent,
      newValue as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>,
      reason as AutocompleteInputChangeReason,
    );

    handleValue(event, newValue as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>, reason, {
      option: option as Value,
    });
    if (
      !disableCloseOnSelect &&
      !(event as React.KeyboardEvent<HTMLElement>).ctrlKey &&
      !(event as React.KeyboardEvent<HTMLElement>).metaKey
    ) {
      handleClose(event, reason as AutocompleteChangeReason);
    }

    if (
      blurOnSelect === true ||
      (blurOnSelect === 'touch' && isTouch.current) ||
      (blurOnSelect === 'mouse' && !isTouch.current)
    ) {
      inputRef.current!.blur();
    }
  };

  function validItemIndex(index: number, direction: 'next' | 'previous'): number {
    if (index === -1) {
      return -1;
    }

    let nextFocus = index;

    // eslint-disable-next-line no-constant-condition
    while (true) {
      if (
        (direction === 'next' && Array.isArray(value) && nextFocus === value.length) ||
        (direction === 'previous' && nextFocus === -1)
      ) {
        return -1;
      }

      const indexType = renderValue ? 'data-item-index' : 'data-tag-index';
      const option = anchorEl?.querySelector(`[${indexType}="${nextFocus}"]`);

      if (
        !option ||
        !option.hasAttribute('tabindex') ||
        (option as HTMLInputElement).disabled ||
        (option as HTMLLIElement).getAttribute('aria-disabled') === 'true'
      ) {
        nextFocus += direction === 'next' ? 1 : -1;
      } else {
        return nextFocus;
      }
    }
  }

  const handleFocusItem = (event: React.SyntheticEvent, direction: 'next' | 'previous') => {
    if (!multiple) {
      return;
    }

    if (inputValue === '') {
      handleClose(event, 'toggleInput' as AutocompleteChangeReason);
    }

    let nextItem = focusedItem;

    if (focusedItem === -1) {
      if (inputValue === '' && direction === 'previous' && Array.isArray(value)) {
        nextItem = value.length - 1;
      }
    } else {
      nextItem += direction === 'next' ? 1 : -1;

      if (nextItem < 0) {
        nextItem = 0;
      }

      if (Array.isArray(value) && nextItem === value.length) {
        nextItem = -1;
      }
    }

    nextItem = validItemIndex(nextItem, direction);

    setFocusedItem(nextItem);
    focusItem(nextItem);
  };

  const handleClear = (event: React.SyntheticEvent) => {
    ignoreFocus.current = true;
    setInputValueState('');

    if (onInputChange) {
      onInputChange(event, '', 'clear');
    }

    handleValue(
      event,
      multiple ? ([] as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>) : null,
      'clear',
    );
  };

  const handleKeyDown = (other: React.HTMLAttributes<HTMLElement>) => (event: React.KeyboardEvent<HTMLElement>) => {
    if (other.onKeyDown) {
      other.onKeyDown(event);
    }

    if ((event as any).defaultMuiPrevented) {
      return;
    }

    if (focusedItem !== -1 && !['ArrowLeft', 'ArrowRight'].includes(event.key)) {
      setFocusedItem(-1);
      focusItem(-1);
    }

    if (event.which !== 229) {
      switch (event.key) {
        case 'Home':
          if (popupOpen && handleHomeEndKeys) {
            event.preventDefault();
            changeHighlightedIndex({ diff: 'start', direction: 'next', reason: 'keyboard', event });
          }
          break;
        case 'End':
          if (popupOpen && handleHomeEndKeys) {
            event.preventDefault();
            changeHighlightedIndex({
              diff: 'end',
              direction: 'previous',
              reason: 'keyboard',
              event,
            });
          }
          break;
        case 'PageUp':
          event.preventDefault();
          changeHighlightedIndex({
            diff: -pageSize,
            direction: 'previous',
            reason: 'keyboard',
            event,
          });
          handleOpen(event);
          break;
        case 'PageDown':
          event.preventDefault();
          changeHighlightedIndex({ diff: pageSize, direction: 'next', reason: 'keyboard', event });
          handleOpen(event);
          break;
        case 'ArrowDown':
          event.preventDefault();
          changeHighlightedIndex({ diff: 1, direction: 'next', reason: 'keyboard', event });
          handleOpen(event);
          break;
        case 'ArrowUp':
          event.preventDefault();
          changeHighlightedIndex({ diff: -1, direction: 'previous', reason: 'keyboard', event });
          handleOpen(event);
          break;
        case 'ArrowLeft':
          if (!multiple && renderValue) {
            focusItem(0);
          } else {
            handleFocusItem(event, 'previous');
          }
          break;
        case 'ArrowRight':
          if (!multiple && renderValue) {
            focusItem(-1);
          } else {
            handleFocusItem(event, 'next');
          }
          break;
        case 'Enter':
          if (highlightedIndexRef.current !== -1 && popupOpen) {
            const option = filteredOptions[highlightedIndexRef.current];
            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;

            event.preventDefault();

            if (disabled) {
              return;
            }

            selectNewValue(event, option, 'selectOption');

            if (autoComplete) {
              inputRef.current!.setSelectionRange(inputRef.current!.value.length, inputRef.current!.value.length);
            }
          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {
            if (multiple) {
              event.preventDefault();
            }
            selectNewValue(
              event,
              inputValue as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>,
              'createOption',
              'freeSolo',
            );
          }
          break;
        case 'Escape':
          if (popupOpen) {
            event.preventDefault();
            event.stopPropagation();
            handleClose(event, 'escape' as AutocompleteChangeReason);
          } else if (
            clearOnEscape &&
            (inputValue !== '' || (multiple && Array.isArray(value) && value.length > 0) || renderValue)
          ) {
            event.preventDefault();
            event.stopPropagation();
            handleClear(event);
          }
          break;
        case 'Backspace':
          if (multiple && !readOnly && inputValue === '' && Array.isArray(value) && value.length > 0) {
            const index = focusedItem === -1 ? value.length - 1 : focusedItem;
            const newValue = value.slice();
            newValue.splice(index, 1);
            handleValue(
              event,
              newValue as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>,
              'removeOption',
              {
                option: value[index],
              },
            );
          }
          if (!multiple && renderValue && !readOnly) {
            setValueState(null as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>);
            focusItem(-1);
          }
          break;
        case 'Delete':
          if (
            multiple &&
            !readOnly &&
            inputValue === '' &&
            Array.isArray(value) &&
            value.length > 0 &&
            focusedItem !== -1
          ) {
            const index = focusedItem;
            const newValue = value.slice();
            newValue.splice(index, 1);
            handleValue(
              event,
              newValue as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>,
              'removeOption',
              {
                option: value[index],
              },
            );
          }
          if (!multiple && renderValue && !readOnly) {
            setValueState(null as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>);
            focusItem(-1);
          }
          break;
        default:
      }
    }
  };

  const handleFocus = (event: React.FocusEvent) => {
    setFocused(true);

    if (openOnFocus && !ignoreFocus.current) {
      handleOpen(event);
    }
  };

  const handleBlur = (event: React.FocusEvent) => {
    if (unstable_isActiveElementInListbox(listboxRef)) {
      inputRef.current!.focus();
      return;
    }

    setFocused(false);
    firstFocus.current = true;
    ignoreFocus.current = false;

    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {
      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');
    } else if (autoSelect && freeSolo && inputValue !== '') {
      selectNewValue(
        event,
        inputValue as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>,
        'blur',
        'freeSolo',
      );
    } else if (clearOnBlur) {
      resetInputValue({} as React.SyntheticEvent, value, 'blur');
    }

    handleClose(event, 'blur' as AutocompleteChangeReason);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;

    if (inputValue !== newValue) {
      setInputValueState(newValue);
      setInputPristine(false);

      if (onInputChange) {
        onInputChange(event, newValue, 'input');
      }
    }

    if (newValue === '') {
      if (!disableClearable && !multiple) {
        handleValue(event, null, 'clear');
      }
    } else {
      handleOpen(event);
    }
  };

  const handleOptionMouseMove = (event: React.MouseEvent<HTMLLIElement>) => {
    const index = Number(event.currentTarget.getAttribute('data-option-index'));
    if (highlightedIndexRef.current !== index) {
      setHighlightedIndex({
        event,
        index,
        reason: 'mouse',
      });
    }
  };

  const handleOptionTouchStart = (event: React.TouchEvent<HTMLLIElement>) => {
    setHighlightedIndex({
      event,
      index: Number(event.currentTarget.getAttribute('data-option-index')),
      reason: 'touch',
    });
    isTouch.current = true;
  };

  const handleOptionClick = (event: React.MouseEvent<HTMLLIElement>) => {
    const index = Number(event.currentTarget.getAttribute('data-option-index'));
    selectNewValue(event, filteredOptions[index], 'selectOption');

    isTouch.current = false;
  };

  const handleItemDelete = (index: number) => (event: React.SyntheticEvent) => {
    if (!multiple) {
      handleValue(event, null, 'removeOption', {
        option: value as Value,
      });
      return;
    }

    const newValue = Array.isArray(value) ? [...value] : [];
    newValue.splice(index, 1);
    handleValue(
      event,
      (multiple ? newValue : null) as AutocompleteValue<Value, Multiple, DisableClearable, FreeSolo>,
      'removeOption',
      {
        option: Array.isArray(value) ? value[index] : (undefined as unknown as Value),
      },
    );
  };

  const handleSingleItemDelete = (event: React.SyntheticEvent) => {
    handleValue(event, null, 'removeOption', {
      option: value as Value,
    });
  };

  const handlePopupIndicator = (event: React.SyntheticEvent) => {
    if (open) {
      handleClose(event, 'toggleInput' as AutocompleteChangeReason);
    } else {
      handleOpen(event);
    }
  };

  const handleMouseDown = (event: React.MouseEvent<HTMLElement>) => {
    if (!event.currentTarget.contains(event.target as Node)) {
      return;
    }
    if ((event.target as HTMLElement).getAttribute('id') !== id) {
      event.preventDefault();
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!event.currentTarget.contains(event.target as Node)) {
      return;
    }
    inputRef.current!.focus();

    if (
      selectOnFocus &&
      firstFocus.current &&
      inputRef.current!.selectionEnd! - inputRef.current!.selectionStart! === 0
    ) {
      inputRef.current!.select();
    }

    firstFocus.current = false;
  };

  const handleInputMouseDown = (event: React.MouseEvent<HTMLInputElement>) => {
    if (!disabledProp && (inputValue === '' || !open)) {
      handlePopupIndicator(event);
    }
  };

  let dirty = freeSolo && inputValue.length > 0;
  dirty = dirty || (multiple ? (Array.isArray(value) ? value.length > 0 : false) : value !== null);

  let groupedOptions = filteredOptions;
  if (groupBy) {
    const indexBy = new Map<string, boolean>();
    let warn = false;

    groupedOptions = filteredOptions.reduce((acc, option, index) => {
      const group = groupBy(option);

      if (acc.length > 0 && acc[acc.length - 1].group === group) {
        acc[acc.length - 1].options.push(option);
      } else {
        if (process.env.NODE_ENV !== 'production') {
          if (indexBy.get(group) && !warn) {
            console.warn(
              `The options provided combined with the \`groupBy\` method of ${componentName} returns duplicated headers.`,
              'You can solve the issue by sorting the options with the output of `groupBy`.',
            );
            warn = true;
          }
          indexBy.set(group, true);
        }

        acc.push({
          key: index,
          index,
          group,
          options: [option],
        });
      }

      return acc;
    }, []);
  }

  if (disabledProp && focused) {
    handleBlur({} as React.FocusEvent);
  }

  return {
    getRootProps: (other = {}) => ({
      ...other,
      onKeyDown: handleKeyDown(other),
      onMouseDown: handleMouseDown,
      onClick: handleClick,
    }),
    getInputLabelProps: () => ({
      id: `${id}-label`,
      htmlFor: id,
    }),
    getInputProps: () => ({
      id,
      value: inputValue,
      onBlur: handleBlur,
      onFocus: handleFocus,
      onChange: handleInputChange,
      onMouseDown: handleInputMouseDown,
      'aria-activedescendant': popupOpen ? '' : undefined,
      'aria-autocomplete': autoComplete ? 'both' : 'list',
      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,
      'aria-expanded': listboxAvailable,
      autoComplete: 'off',
      ref: inputRef,
      autoCapitalize: 'none',
      spellCheck: 'false',
      role: 'combobox',
      disabled: disabledProp,
    }),
    getClearProps: () => ({
      tabIndex: -1,
      type: 'button',
      onClick: handleClear,
    }),
    getItemProps: (({ index = 0 } = {}) => ({
      ...(multiple && { key: index }),
      ...(renderValue ? { 'data-item-index': index } : { 'data-tag-index': index }),
      tabIndex: -1,
      ...(!readOnly && { onDelete: multiple ? handleItemDelete(index) : handleSingleItemDelete }),
    })) as AutocompleteGetItemProps<Multiple>,
    getPopupIndicatorProps: () => ({
      tabIndex: -1,
      type: 'button',
      onClick: handlePopupIndicator,
    }),
    getTagProps: ({ index }: { index: number }) => ({
      key: index,
      'data-tag-index': index,
      tabIndex: -1,
      ...(!readOnly && { onDelete: handleItemDelete(index) }),
    }),
    getListboxProps: () => ({
      role: 'listbox',
      id: `${id}-listbox`,
      'aria-labelledby': `${id}-label`,
      ref: handleListboxRef,
      onMouseDown: (event) => {
        event.preventDefault();
      },
    }),
    getOptionProps: ({ index, option }: { index: number; option: Value }) => {
      const selected = multiple
        ? Array.isArray(value) && value.some((value2) => value2 != null && isOptionEqualToValue(option, value2))
        : value != null && isOptionEqualToValue(option, value as Value);
      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;

      return {
        key: getOptionKey?.(option) ?? getOptionLabel(option),
        tabIndex: -1,
        role: 'option',
        id: `${id}-option-${index}`,
        onMouseMove: handleOptionMouseMove,
        onClick: handleOptionClick,
        onTouchStart: handleOptionTouchStart,
        'data-option-index': index,
        'aria-disabled': disabled,
        'aria-selected': selected,
      };
    },
    id,
    inputValue,
    value,
    dirty,
    expanded: popupOpen && Boolean(anchorEl),
    popupOpen,
    focused: focused || focusedItem !== -1,
    anchorEl,
    setAnchorEl: handleSetAnchorEl,
    focusedItem,
    focusedTag: focusedItem,
    groupedOptions,
  };
}

export { useAutocomplete };
