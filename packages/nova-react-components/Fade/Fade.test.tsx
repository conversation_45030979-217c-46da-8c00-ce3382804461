/// <reference types="@testing-library/jest-dom" />
import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Fade } from './Fade';

// Mock the react-transition-group
vi.mock('react-transition-group', () => ({
  Transition: ({
    children,
    in: inProp,
    onEnter,
    onEntered,
    onEntering,
    onExit,
    onExited,
    onExiting,
    ...props
  }: any) => {
    const state = inProp ? 'entered' : 'exited';

    React.useEffect(() => {
      if (inProp && onEnter) {
        onEnter();
      }
      if (inProp && onEntering) {
        onEntering();
      }
      if (inProp && onEntered) {
        onEntered();
      }
      if (!inProp && onExit) {
        onExit();
      }
      if (!inProp && onExiting) {
        onExiting();
      }
      if (!inProp && onExited) {
        onExited();
      }
    }, [inProp, onEnter, onEntering, onEntered, onExit, onExiting, onExited]);

    return children(state, props);
  },
}));

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Fade', () => {
  it('renders children when in is true', () => {
    render(
      <Fade in>
        <div data-testid="fade-content">Fade content</div>
      </Fade>,
    );

    expect(screen.getByTestId('fade-content')).toBeInTheDocument();
    expect(screen.getByText('Fade content')).toBeInTheDocument();
  });

  it('renders children when in is false', () => {
    render(
      <Fade in={false}>
        <div data-testid="fade-content">Fade content</div>
      </Fade>,
    );

    expect(screen.getByTestId('fade-content')).toBeInTheDocument();
  });

  it('applies opacity 0 by default and opacity 1 when entered', () => {
    render(
      <Fade in>
        <div data-testid="fade-content">Content</div>
      </Fade>,
    );

    const element = screen.getByTestId('fade-content');
    expect(element).toHaveStyle('opacity: 1');
  });

  it('applies visibility hidden when exited and not in', () => {
    render(
      <Fade in={false}>
        <div data-testid="fade-content">Content</div>
      </Fade>,
    );

    const element = screen.getByTestId('fade-content');
    expect(element).toHaveStyle('visibility: hidden');
  });

  it('does not apply visibility hidden when entered', () => {
    render(
      <Fade in>
        <div data-testid="fade-content">Content</div>
      </Fade>,
    );

    const element = screen.getByTestId('fade-content');
    expect(element).not.toHaveStyle('visibility: hidden');
  });

  it('calls onEnter callback when entering', () => {
    const onEnter = vi.fn();

    render(
      <Fade in onEnter={onEnter}>
        <div>Content</div>
      </Fade>,
    );

    expect(onEnter).toHaveBeenCalled();
  });

  it('calls onEntering callback when entering', () => {
    const onEntering = vi.fn();

    render(
      <Fade in onEntering={onEntering}>
        <div>Content</div>
      </Fade>,
    );

    expect(onEntering).toHaveBeenCalled();
  });

  it('calls onEntered callback when entered', () => {
    const onEntered = vi.fn();

    render(
      <Fade in onEntered={onEntered}>
        <div>Content</div>
      </Fade>,
    );

    expect(onEntered).toHaveBeenCalled();
  });

  it('calls onExit callback when exiting', () => {
    const onExit = vi.fn();

    render(
      <Fade in={false} onExit={onExit}>
        <div>Content</div>
      </Fade>,
    );

    expect(onExit).toHaveBeenCalled();
  });

  it('calls onExiting callback when exiting', () => {
    const onExiting = vi.fn();

    render(
      <Fade in={false} onExiting={onExiting}>
        <div>Content</div>
      </Fade>,
    );

    expect(onExiting).toHaveBeenCalled();
  });

  it('calls onExited callback when exited', () => {
    const onExited = vi.fn();

    render(
      <Fade in={false} onExited={onExited}>
        <div>Content</div>
      </Fade>,
    );

    expect(onExited).toHaveBeenCalled();
  });

  it('handles timeout prop as number', () => {
    render(
      <Fade in timeout={500}>
        <div>Content</div>
      </Fade>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('handles timeout prop as object', () => {
    render(
      <Fade in timeout={{ enter: 300, exit: 500 }}>
        <div>Content</div>
      </Fade>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('handles easing prop as string', () => {
    render(
      <Fade in easing="ease-in-out">
        <div>Content</div>
      </Fade>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('handles easing prop as object', () => {
    const easing = { enter: 'ease-in', exit: 'ease-out' };

    render(
      <Fade in easing={easing}>
        <div>Content</div>
      </Fade>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('renders with appear prop set to false', () => {
    render(
      <Fade in appear={false}>
        <div>Content</div>
      </Fade>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('forwards ref to the child element', () => {
    const ref = React.createRef<HTMLDivElement>();

    render(
      <Fade in>
        <div ref={ref}>Content</div>
      </Fade>,
    );

    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });

  it('merges styles with child element styles', () => {
    render(
      <Fade in>
        <div data-testid="fade-content" style={{ fontSize: '16px' }}>
          Content
        </div>
      </Fade>,
    );

    const element = screen.getByTestId('fade-content');
    expect(element).toHaveStyle('font-size: 16px');
    expect(element).toHaveStyle('opacity: 1');
  });

  it('passes through additional props to TransitionComponent', () => {
    render(
      <Fade in data-testid="fade-root">
        <div>Content</div>
      </Fade>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('works with custom TransitionComponent', () => {
    const CustomTransition = ({ children, in: inProp, ...props }: any) => {
      const state = inProp ? 'entered' : 'exited';
      return children(state, props);
    };

    render(
      <Fade in TransitionComponent={CustomTransition}>
        <div data-testid="fade-content">Content</div>
      </Fade>,
    );

    expect(screen.getByTestId('fade-content')).toBeInTheDocument();
  });

  it('handles addEndListener callback', () => {
    const addEndListener = vi.fn();

    render(
      <Fade in addEndListener={addEndListener}>
        <div>Content</div>
      </Fade>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('preserves child element props', () => {
    render(
      <Fade in>
        <div data-testid="fade-content" className="child-class" id="child-id">
          Content
        </div>
      </Fade>,
    );

    const element = screen.getByTestId('fade-content');
    expect(element).toHaveClass('child-class');
    expect(element).toHaveAttribute('id', 'child-id');
  });

  it('clones child element correctly', () => {
    const ChildComponent = React.forwardRef<HTMLDivElement, any>(({ children, ...props }, ref) => (
      <div ref={ref} data-testid="child-component" {...props}>
        {children}
      </div>
    ));

    render(
      <Fade in>
        <ChildComponent>Content</ChildComponent>
      </Fade>,
    );

    expect(screen.getByTestId('child-component')).toBeInTheDocument();
    expect(screen.getByText('Content')).toBeInTheDocument();
  });
});
