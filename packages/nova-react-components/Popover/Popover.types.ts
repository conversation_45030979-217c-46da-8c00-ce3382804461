import { ComponentProps, ReactNode } from 'react';
import { Popover as BasePopover } from '@base-ui-components/react/popover';

export type BaseTriggerProps = ComponentProps<typeof BasePopover.Trigger>;

export interface PopoverProps extends Omit<ComponentProps<typeof BasePopover.Root>, 'children'> {
  /**
   * The trigger element that will open the popover when clicked.
   */
  children: ReactNode;

  /**
   * The title to be displayed in the popover.
   */
  title?: ReactNode;

  /**
   * The description content to be displayed in the popover.
   */
  description?: ReactNode;

  /**
   * Custom content to be displayed in the popover.
   * If provided, it will replace the default title and description layout.
   */
  content?: ReactNode;

  /**
   * The placement of the popover relative to the trigger element.
   * @default 'bottom'
   */
  placement?: 'top' | 'right' | 'bottom' | 'left';

  /**
   * Whether to show the arrow.
   * @default true
   */
  showArrow?: boolean;

  /**
   * Whether the popover should be modal (trap focus).
   * @default false
   */
  modal?: boolean;

  /**
   * Whether to show the backdrop.
   * @default false
   */
  showBackdrop?: boolean;

  /**
   * Whether to show the close button.
   * @default false
   */
  showClose?: boolean;

  /**
   * Class name applied to the root element.
   */
  className?: string;

  /**
   * Props passed to the sub-components.
   */
  slotProps?: {
    /**
     * Props passed to the Root component.
     */
    root?: Omit<ComponentProps<typeof BasePopover.Root>, 'children'>;

    /**
     * Props passed to the Trigger component.
     */
    trigger?: Omit<ComponentProps<typeof BasePopover.Trigger>, 'children'>;

    /**
     * Props passed to the Portal component.
     */
    portal?: Omit<ComponentProps<typeof BasePopover.Portal>, 'children'>;

    /**
     * Props passed to the Positioner component.
     */
    positioner?: Omit<ComponentProps<typeof BasePopover.Positioner>, 'children'>;

    /**
     * Props passed to the Popup component.
     */
    popup?: Omit<ComponentProps<typeof BasePopover.Popup>, 'children'>;

    /**
     * Props passed to the Arrow component.
     */
    arrow?: Omit<ComponentProps<typeof BasePopover.Arrow>, 'children'>;

    /**
     * Props passed to the Backdrop component.
     */
    backdrop?: Omit<ComponentProps<typeof BasePopover.Backdrop>, 'children'>;

    /**
     * Props passed to the Title component.
     */
    title?: Omit<ComponentProps<typeof BasePopover.Title>, 'children'>;

    /**
     * Props passed to the Description component.
     */
    description?: Omit<ComponentProps<typeof BasePopover.Description>, 'children'>;

    /**
     * Props passed to the Close component.
     */
    close?: Omit<ComponentProps<typeof BasePopover.Close>, 'children'>;
  };
}
