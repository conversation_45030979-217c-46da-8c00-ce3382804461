import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface PopoverClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the trigger element. */
  trigger: string;
  /** Styles applied to the portal element. */
  portal: string;
  /** Styles applied to the positioner element. */
  positioner: string;
  /** Styles applied to the popup element. */
  popup: string;
  /** Styles applied to the arrow element. */
  arrow: string;
  /** Styles applied to the backdrop element. */
  backdrop: string;
  /** Styles applied to the title element. */
  title: string;
  /** Styles applied to the description element. */
  description: string;
  /** Styles applied to the close button element. */
  close: string;
  /** Styles applied to the root element when the popover is open. */
  open: string;
  /** Styles applied to the root element when the popover is closed. */
  closed: string;
}

export type PopoverClassKey = keyof PopoverClasses;

export function getPopoverUtilityClass(slot: string): string {
  return generateUtilityClass('NovaPopover', slot);
}

const popoverClasses: PopoverClasses = generateUtilityClasses('NovaPopover', [
  'root',
  'trigger',
  'portal',
  'positioner',
  'popup',
  'arrow',
  'backdrop',
  'title',
  'description',
  'close',
  'open',
  'closed',
]);

export default popoverClasses;
