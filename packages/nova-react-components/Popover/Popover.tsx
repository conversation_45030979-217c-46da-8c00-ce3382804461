'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import clsx from 'clsx';
import { Popover as BasePopover } from '@base-ui-components/react/popover';
import { PopoverProps, BaseTriggerProps } from './Popover.types';
import { getPopoverUtilityClass } from './Popover.classes';
import useSlotProps from '@mui/utils/useSlotProps';
import CloseIcon from '../internal/svg-icons/CloseOutlined';

// Wrapper component to ensure compatibility
const TriggerWrapper = (props: BaseTriggerProps) => <BasePopover.Trigger {...props} />;

const useUtilityClasses = (ownerState: PopoverProps & { isOpen?: boolean }) => {
  const { isOpen } = ownerState;

  const slots = {
    root: ['root', isOpen && 'open', !isOpen && 'closed'],
    trigger: ['trigger'],
    portal: ['portal'],
    positioner: ['positioner'],
    popup: ['popup'],
    arrow: ['arrow'],
    backdrop: ['backdrop'],
    title: ['title'],
    description: ['description'],
    close: ['close'],
  };

  return composeClasses(slots, getPopoverUtilityClass, {});
};

const StyledPopup = styled('div', {
  name: 'NovaPopover',
  slot: 'Popup',
})<Partial<PopoverProps>>(({ theme }) => ({
  backgroundColor: theme.vars.palette.surfaceContainer,
  color: theme.vars.palette.onSurface,
  padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.md} ${theme.vars.sys.viewport.spacing.padding.leftRight.md}`,
  borderRadius: 'var(--radius-xs)',
  fontFamily: theme.typography.fontFamily,
  boxShadow: theme.shadows[2],
  border: '1px solid',
  borderColor: theme.vars.palette.outlineVariant,
  display: 'flex',
  flexDirection: 'column',
  gap: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg,
  minWidth: '280px',
  position: 'relative',
}));

const StyledTitle = styled('div', {
  name: 'NovaPopover',
  slot: 'Title',
})<Partial<PopoverProps>>(({ theme }) => ({
  ...theme.typography.titleSmall,
  color: theme.vars.palette.onSurfaceVariant,
  margin: 0,
}));

const StyledDescription = styled('div', {
  name: 'NovaPopover',
  slot: 'Description',
})<Partial<PopoverProps>>(({ theme }) => ({
  ...theme.typography.bodyMedium,
  color: theme.vars.palette.onSurfaceVariant,
  margin: 0,
}));

const StyledArrow = styled(BasePopover.Arrow, {
  name: 'NovaPopover',
  slot: 'Arrow',
})<Partial<PopoverProps>>(({ theme }) => ({
  width: '12px',
  height: '12px',
  backgroundColor: theme.vars.palette.surfaceContainer,
  border: '1px solid',
  borderColor: theme.vars.palette.outlineVariant,
  position: 'absolute',
  transform: 'rotate(45deg)',
  '&[data-side="top"]': {
    bottom: '-6px',
    borderTop: 'none',
    borderLeft: 'none',
  },
  '&[data-side="bottom"]': {
    top: '-6px',
    borderBottom: 'none',
    borderRight: 'none',
  },
  '&[data-side="left"]': {
    right: '-6px',
    borderLeft: 'none',
    borderBottom: 'none',
  },
  '&[data-side="right"]': {
    left: '-6px',
    borderRight: 'none',
    borderTop: 'none',
  },
}));

const StyledBackdrop = styled('div', {
  name: 'NovaPopover',
  slot: 'Backdrop',
})(() => ({
  position: 'fixed',
  top: 0,
  right: 0,
  bottom: 0,
  left: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.16)',
  zIndex: 1400,
}));

const BasePopoverTrigger = styled(TriggerWrapper, {
  name: 'NovaPopover',
  slot: 'Trigger',
})<Partial<BaseTriggerProps>>(() => ({
  backgroundColor: 'transparent',
  border: 'none',
  padding: 0,
  cursor: 'pointer',
}));

const StyledCloseButton = styled(BasePopover.Close, {
  name: 'NovaPopover',
  slot: 'CloseButton',
})<Partial<PopoverProps>>(({ theme }) => ({
  position: 'absolute',
  top: '8px',
  right: '8px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '32px',
  height: '32px',
  padding: 0,
  borderRadius: 'var(--radius-xs)',
  border: 'none',
  backgroundColor: 'transparent',
  color: theme.vars.palette.onSurface,
  cursor: 'pointer',
  transition: 'background-color 0.2s ease',
  '&:hover': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },
  '&:focus-visible': {
    outline: `2px solid ${theme.vars.palette.onSurfaceVariant}`,
    outlineOffset: 2,
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
  },
  '&:active': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },
}));

// eslint-disable-next-line react/display-name
export const Popover = React.forwardRef<HTMLDivElement, PopoverProps>((props, ref) => {
  const {
    children,
    title,
    description,
    content,
    className,
    open,
    defaultOpen,
    onOpenChange,
    placement = 'bottom',
    showArrow = false,
    showBackdrop = false,
    showClose = false,
    modal = false,
    slotProps = {},
    ...other
  } = props;

  const [isOpen, setIsOpen] = React.useState(defaultOpen || slotProps.root?.defaultOpen || false);

  const handleOpenChange = React.useCallback(
    (nextOpen: boolean, event?: Event, reason?: any) => {
      setIsOpen(nextOpen);
      onOpenChange?.(nextOpen, event, reason);
      slotProps.root?.onOpenChange?.(nextOpen, event, reason);
    },
    [onOpenChange, slotProps.root],
  );

  const ownerState = {
    ...props,
    open: open ?? slotProps.root?.open ?? isOpen,
    placement,
    className,
  };

  const classes = useUtilityClasses({ ...props, isOpen });

  const popupProps = useSlotProps({
    elementType: StyledPopup,
    externalSlotProps: {},
    additionalProps: {},
    ownerState,
    className: classes.popup,
  });

  const arrowProps = useSlotProps({
    elementType: StyledArrow,
    externalSlotProps: {},
    additionalProps: {},
    ownerState,
    className: classes.arrow,
  });

  const titleProps = useSlotProps({
    elementType: StyledTitle,
    externalSlotProps: {},
    additionalProps: {},
    ownerState,
    className: classes.title,
  });

  const descriptionProps = useSlotProps({
    elementType: StyledDescription,
    externalSlotProps: {},
    additionalProps: {},
    ownerState,
    className: classes.description,
  });

  const withCloseButton = showClose && (
    <StyledCloseButton {...slotProps.close} className={clsx(classes.close, slotProps.close?.className)}>
      <CloseIcon />
    </StyledCloseButton>
  );

  return (
    <BasePopover.Root
      open={open ?? slotProps.root?.open ?? isOpen}
      defaultOpen={defaultOpen ?? slotProps.root?.defaultOpen}
      onOpenChange={handleOpenChange}
      modal={modal}
      {...slotProps.root}
    >
      <BasePopoverTrigger
        ref={ref}
        className={clsx(classes.trigger, className)}
        {...slotProps.trigger}
        {...other}
        style={{ ...slotProps.trigger?.style }}
      >
        {children}
      </BasePopoverTrigger>
      <BasePopover.Portal {...slotProps.portal} className={classes.portal}>
        {showBackdrop && (
          <BasePopover.Backdrop
            {...slotProps.backdrop}
            className={clsx(classes.backdrop, slotProps.backdrop?.className)}
          >
            <StyledBackdrop />
          </BasePopover.Backdrop>
        )}
        <BasePopover.Positioner
          side={placement ?? slotProps.positioner?.side}
          className={clsx(classes.positioner, slotProps.positioner?.className)}
          sideOffset={8}
          align="center"
          alignOffset={0}
          style={{ zIndex: 1500 }}
          {...slotProps.positioner}
        >
          <BasePopover.Popup {...slotProps.popup} className={clsx(classes.popup, slotProps.popup?.className)}>
            <StyledPopup {...popupProps}>
              {content ? (
                <>
                  {content}
                  {withCloseButton}
                </>
              ) : (
                <>
                  {title && (
                    <BasePopover.Title {...slotProps.title} className={clsx(classes.title, slotProps.title?.className)}>
                      <StyledTitle {...titleProps}>{title}</StyledTitle>
                    </BasePopover.Title>
                  )}
                  {description && (
                    <BasePopover.Description
                      {...slotProps.description}
                      className={clsx(classes.description, slotProps.description?.className)}
                    >
                      <StyledDescription {...descriptionProps}>{description}</StyledDescription>
                    </BasePopover.Description>
                  )}
                  {withCloseButton}
                </>
              )}
              {showArrow && <StyledArrow {...arrowProps} />}
            </StyledPopup>
          </BasePopover.Popup>
        </BasePopover.Positioner>
      </BasePopover.Portal>
    </BasePopover.Root>
  );
});
