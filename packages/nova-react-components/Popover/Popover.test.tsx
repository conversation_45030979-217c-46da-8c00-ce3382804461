/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { Popover } from './Popover';

describe('Popover Component', () => {
  test('renders trigger element correctly', () => {
    render(
      <Popover>
        <button>Trigger</button>
      </Popover>,
    );
    expect(screen.getByText('Trigger')).toBeInTheDocument();
  });

  test('shows title and description when clicked', () => {
    render(
      <Popover title="Test Title" description="Test Description">
        <button>Trigger</button>
      </Popover>,
    );

    fireEvent.click(screen.getByText('Trigger'));

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
  });

  test('renders custom content when provided', () => {
    const customContent = <div data-testid="custom-content">Custom Content</div>;
    render(
      <Popover content={customContent}>
        <button>Trigger</button>
      </Popover>,
    );

    fireEvent.click(screen.getByText('Trigger'));
    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
  });

  test('shows arrow when showArrow is true', () => {
    render(
      <Popover showArrow title="Test Title">
        <button>Trigger</button>
      </Popover>,
    );

    fireEvent.click(screen.getByText('Trigger'));
    expect(document.querySelector('[data-side]')).toBeInTheDocument();
  });

  test('shows close button when showClose is true', () => {
    render(
      <Popover showClose title="Test Title">
        <button>Trigger</button>
      </Popover>,
    );

    fireEvent.click(screen.getByText('Trigger'));
    const closeButton = document.querySelector('.NovaPopover-close');
    expect(closeButton).not.toBeNull();
    expect(closeButton).toBeInTheDocument();
  });

  test('shows backdrop when showBackdrop is true', () => {
    render(
      <Popover showBackdrop title="Test Title">
        <button>Trigger</button>
      </Popover>,
    );

    fireEvent.click(screen.getByText('Trigger'));
    expect(document.querySelector('[class*="NovaPopover-backdrop"]')).toBeInTheDocument();
  });

  test('renders with placement', async () => {
    render(
      <Popover placement="top" title="Test Title">
        <button>Trigger</button>
      </Popover>,
    );

    await act(async () => {
      fireEvent.click(screen.getByText('Trigger'));
    });

    await act(async () => {
      const topElement = document.querySelector('[data-side="top"]');
      expect(topElement).not.toBeNull();
      expect(topElement).toBeInTheDocument();
    });
  });

  test('handles open state changes', () => {
    const onOpenChange = vi.fn();
    render(
      <Popover title="Test Title" onOpenChange={onOpenChange}>
        <button>Trigger</button>
      </Popover>,
    );

    fireEvent.click(screen.getByText('Trigger'));
    expect(onOpenChange).toHaveBeenCalledWith(true, expect.any(MouseEvent), 'click');
  });

  test('can be controlled with open prop', () => {
    const { rerender } = render(
      <Popover open={true} title="Test Title">
        <button>Trigger</button>
      </Popover>,
    );

    expect(screen.getByText('Test Title')).toBeInTheDocument();

    rerender(
      <Popover open={false} title="Test Title">
        <button>Trigger</button>
      </Popover>,
    );

    expect(screen.queryByText('Test Title')).not.toBeInTheDocument();
  });

  test('closes when clicking close button', () => {
    render(
      <Popover showClose title="Test Title">
        <button>Trigger</button>
      </Popover>,
    );

    fireEvent.click(screen.getByText('Trigger'));
    const closeButton = document.querySelector('.NovaPopover-close');
    expect(closeButton).not.toBeNull();
    fireEvent.click(closeButton!);

    expect(screen.queryByText('Test Title')).not.toBeInTheDocument();
  });
});
