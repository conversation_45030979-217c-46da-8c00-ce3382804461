import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';

export interface OptionSlots {
  root?: React.ElementType;
}

export type OptionSlotsAndSlotProps = CreateSlotsAndSlotProps<
  OptionSlots,
  {
    root: SlotProps<'li', object, OptionOwnerState>;
  }
>;

export interface OptionTypeMap<P = object, D extends React.ElementType = 'li'> {
  props: P & {
    /**
     * A text representation of the option's content.
     * Used for keyboard text navigation matching.
     */
    label?: string | React.ReactElement<any>;
    /**
     * The option value.
     */
    value: any;
    /**
     * If `true`, the component is disabled.
     * @default false
     */
    disabled?: boolean;
  } & OptionSlotsAndSlotProps;
  defaultComponent: D;
}

export type OptionProps<
  D extends React.ElementType = OptionTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<OptionTypeMap<P, D>, D>;

export interface OptionOwnerState extends OptionProps {
  /**
   * If `true` the item is highlighted.
   */
  highlighted: boolean;
  /**
   * The 0-based index of the item.
   */
  index: number;
  /**
   * If `true` the item is selected.
   */
  selected: boolean;
  row: boolean;
}
