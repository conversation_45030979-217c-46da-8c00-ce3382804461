@font-face {
  font-family: 'NovaIcons';
  src:
    url('./NovaIcons.ttf?43457b2fd8f8503152719add39a4d65e') format('truetype'),
    url('./NovaIcons.woff?43457b2fd8f8503152719add39a4d65e') format('woff'),
    url('./NovaIcons.woff2?43457b2fd8f8503152719add39a4d65e') format('woff2'),
    url('./NovaIcons.eot?43457b2fd8f8503152719add39a4d65e#iefix') format('embedded-opentype'),
    url('./NovaIcons.svg?43457b2fd8f8503152719add39a4d65e#NovaIcons') format('svg');
}

span[class^='nova-']:before,
span[class*=' nova-']:before {
  font-family: NovaIcons !important;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.nova-360-Capture:before {
  content: '\f101';
}
.nova-360-videowalk:before {
  content: '\f102';
}
.nova-3D-Print-Mesh:before {
  content: '\f103';
}
.nova-3D-surface-with-ground:before {
  content: '\f104';
}
.nova-3D-Surface:before {
  content: '\f105';
}
.nova-Angle:before {
  content: '\f106';
}
.nova-BIM-cloudpoint:before {
  content: '\f107';
}
.nova-BIM:before {
  content: '\f108';
}
.nova-Binoculars:before {
  content: '\f109';
}
.nova-BLK-ARC-Off-__hxgn:before {
  content: '\f10a';
}
.nova-BLK-ARC__hxgn:before {
  content: '\f10b';
}
.nova-BLK360-G1-Off:before {
  content: '\f10c';
}
.nova-BLK360-G1:before {
  content: '\f10d';
}
.nova-BLK360-G2-Off__hxgn:before {
  content: '\f10e';
}
.nova-BLK360-G2__hxgn:before {
  content: '\f10f';
}
.nova-Cable-locator-underground:before {
  content: '\f110';
}
.nova-Calculate-polyline-area:before {
  content: '\f111';
}
.nova-Camera-Globe:before {
  content: '\f112';
}
.nova-Catalog:before {
  content: '\f113';
}
.nova-Center-Square:before {
  content: '\f114';
}
.nova-change-catalog:before {
  content: '\f115';
}
.nova-check-surface:before {
  content: '\f116';
}
.nova-COGO-point-projected:before {
  content: '\f117';
}
.nova-compass:before {
  content: '\f118';
}
.nova-Construction-sites-list-helmet:before {
  content: '\f119';
}
.nova-Construction-sites-list:before {
  content: '\f11a';
}
.nova-Control-Point:before {
  content: '\f11b';
}
.nova-Coordinate-system:before {
  content: '\f11c';
}
.nova-Dashboard-Table:before {
  content: '\f11d';
}
.nova-Define-sample-lines:before {
  content: '\f11e';
}
.nova-Define-test-section:before {
  content: '\f11f';
}
.nova-Design-excavation:before {
  content: '\f120';
}
.nova-Device-Connected:before {
  content: '\f121';
}
.nova-Device-connections:before {
  content: '\f122';
}
.nova-Device-not-Connected:before {
  content: '\f123';
}
.nova-Device-unknown:before {
  content: '\f124';
}
.nova-Distance-between-two-points:before {
  content: '\f125';
}
.nova-Dolly:before {
  content: '\f126';
}
.nova-Echo-sounder-arrow:before {
  content: '\f127';
}
.nova-Echo-sounder-boat:before {
  content: '\f128';
}
.nova-Electronic-bubble:before {
  content: '\f129';
}
.nova-ethernet-cable:before {
  content: '\f12a';
}
.nova-FFFL:before {
  content: '\f12b';
}
.nova-Find-on-map-Stop:before {
  content: '\f12c';
}
.nova-Find-on-map:before {
  content: '\f12d';
}
.nova-Floor-Plan:before {
  content: '\f12e';
}
.nova-FLX100- + -Cable-locator:before {
  content: '\f12f';
}
.nova-FLX100-handheld:before {
  content: '\f130';
}
.nova-FLX100-Pole:before {
  content: '\f131';
}
.nova-FLX100-Tilt:before {
  content: '\f132';
}
.nova-FLX100-Upright:before {
  content: '\f133';
}
.nova-Geo-Streaming:before {
  content: '\f134';
}
.nova-Geometrical-transformation:before {
  content: '\f135';
}
.nova-gnss-automatic:before {
  content: '\f136';
}
.nova-gnss-last-position:before {
  content: '\f137';
}
.nova-gnss-measuring:before {
  content: '\f138';
}
.nova-gnss-pole:before {
  content: '\f139';
}
.nova-gnss-tripod-location:before {
  content: '\f13a';
}
.nova-gps-corrections-Low-_hxgn:before {
  content: '\f13b';
}
.nova-gps-corrections-Very-Low_hxgn:before {
  content: '\f13c';
}
.nova-gps_corrections-ON_hxgn:before {
  content: '\f13d';
}
.nova-gps_corrections_off_hxgn:before {
  content: '\f13e';
}
.nova-GS05__hxgn:before {
  content: '\f13f';
}
.nova-GS18__hxgn:before {
  content: '\f140';
}
.nova-iCS20-50__hxgn:before {
  content: '\f141';
}
.nova-Laser-on-any-surface:before {
  content: '\f142';
}
.nova-Laser-pointer:before {
  content: '\f143';
}
.nova-Layers-CAD:before {
  content: '\f144';
}
.nova-Layers-WFS:before {
  content: '\f145';
}
.nova-Layers-WMS:before {
  content: '\f146';
}
.nova-Layers:before {
  content: '\f147';
}
.nova-Legend:before {
  content: '\f148';
}
.nova-loop:before {
  content: '\f149';
}
.nova-magic-wand:before {
  content: '\f14a';
}
.nova-Measurement-of-points-Circle:before {
  content: '\f14b';
}
.nova-Measurement-of-points-Square:before {
  content: '\f14c';
}
.nova-Mini-prism:before {
  content: '\f14d';
}
.nova-Move-unit-Pull-back:before {
  content: '\f14e';
}
.nova-Move-unit-Shere:before {
  content: '\f14f';
}
.nova-navigation-to-a-line:before {
  content: '\f150';
}
.nova-navigation-to-a-point:before {
  content: '\f151';
}
.nova-Object-Avoidance-Active-On:before {
  content: '\f152';
}
.nova-Object-Avoidance-Off:before {
  content: '\f153';
}
.nova-Orbit-Y:before {
  content: '\f154';
}
.nova-Orbit-Z:before {
  content: '\f155';
}
.nova-Orbit-X:before {
  content: '\f156';
}
.nova-P-series__hxgn:before {
  content: '\f157';
}
.nova-Pan:before {
  content: '\f158';
}
.nova-Pen-tool:before {
  content: '\f159';
}
.nova-Place-on-surface:before {
  content: '\f15a';
}
.nova-Plane-X:before {
  content: '\f15b';
}
.nova-Plane-Y:before {
  content: '\f15c';
}
.nova-Plane-Z:before {
  content: '\f15d';
}
.nova-point-on-the-road:before {
  content: '\f15e';
}
.nova-Position-available:before {
  content: '\f15f';
}
.nova-Prism-360°:before {
  content: '\f160';
}
.nova-Prism-long-range:before {
  content: '\f161';
}
.nova-Product_HxDR:before {
  content: '\f162';
}
.nova-Property-to-Go:before {
  content: '\f163';
}
.nova-Rebar:before {
  content: '\f164';
}
.nova-Reference-Line:before {
  content: '\f165';
}
.nova-Reverse-TPS-1-and-2:before {
  content: '\f166';
}
.nova-road-location:before {
  content: '\f167';
}
.nova-road:before {
  content: '\f168';
}
.nova-Rotate-TPS-left:before {
  content: '\f169';
}
.nova-Round-prism:before {
  content: '\f16a';
}
.nova-RTC360__hxgn:before {
  content: '\f16b';
}
.nova-RTK-not-available_hxgn:before {
  content: '\f16c';
}
.nova-scan-automatic:before {
  content: '\f16d';
}
.nova-scan-diffent-directions:before {
  content: '\f16e';
}
.nova-scan-level-point:before {
  content: '\f16f';
}
.nova-scan-line:before {
  content: '\f170';
}
.nova-scan-multiple-line:before {
  content: '\f171';
}
.nova-Set-target-height:before {
  content: '\f172';
}
.nova-Set-telescope-horizontal:before {
  content: '\f173';
}
.nova-Shaded-Rendered:before {
  content: '\f174';
}
.nova-Shaded:before {
  content: '\f175';
}
.nova-Spot__hxgn:before {
  content: '\f176';
}
.nova-spray-paint:before {
  content: '\f177';
}
.nova-Stakeout-peg-circle:before {
  content: '\f178';
}
.nova-Stakeout-peg-rectangle:before {
  content: '\f179';
}
.nova-Style-Lines-1:before {
  content: '\f17a';
}
.nova-Style-Lines:before {
  content: '\f17b';
}
.nova-Symbology:before {
  content: '\f17c';
}
.nova-Tape-target:before {
  content: '\f17d';
}
.nova-TPS-measurement-two-prisms:before {
  content: '\f17e';
}
.nova-TPS-Robotic-Total-Station:before {
  content: '\f17f';
}
.nova-TPS-tripod-view-from-the-top:before {
  content: '\f180';
}
.nova-TPS-tripod:before {
  content: '\f181';
}
.nova-Trak-Edit:before {
  content: '\f182';
}
.nova-Trak:before {
  content: '\f183';
}
.nova-Vertical-point:before {
  content: '\f184';
}
.nova-Video-Globe:before {
  content: '\f185';
}
.nova-View-Side-3D:before {
  content: '\f186';
}
.nova-View-Top:before {
  content: '\f187';
}
.nova-Wireframe:before {
  content: '\f188';
}
.nova-X-ray:before {
  content: '\f189';
}
