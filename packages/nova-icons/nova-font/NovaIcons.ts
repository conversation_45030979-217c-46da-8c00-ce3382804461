export type NovaIconsId =
  | '360-Capture'
  | '360-videowalk'
  | '3D-Print-Mesh'
  | '3D-surface-with-ground'
  | '3D-Surface'
  | 'Angle'
  | 'BIM-cloudpoint'
  | 'BIM'
  | 'Binoculars'
  | 'BLK-ARC-Off-__hxgn'
  | 'BLK-ARC__hxgn'
  | 'BLK360-G1-Off'
  | 'BLK360-G1'
  | 'BLK360-G2-Off__hxgn'
  | 'BLK360-G2__hxgn'
  | 'Cable-locator-underground'
  | 'Calculate-polyline-area'
  | 'Camera-Globe'
  | 'Catalog'
  | 'Center-Square'
  | 'change-catalog'
  | 'check-surface'
  | 'COGO-point-projected'
  | 'compass'
  | 'Construction-sites-list-helmet'
  | 'Construction-sites-list'
  | 'Control-Point'
  | 'Coordinate-system'
  | 'Dashboard-Table'
  | 'Define-sample-lines'
  | 'Define-test-section'
  | 'Design-excavation'
  | 'Device-Connected'
  | 'Device-connections'
  | 'Device-not-Connected'
  | 'Device-unknown'
  | 'Distance-between-two-points'
  | 'Dolly'
  | 'Echo-sounder-arrow'
  | 'Echo-sounder-boat'
  | 'Electronic-bubble'
  | 'ethernet-cable'
  | 'FFFL'
  | 'Find-on-map-Stop'
  | 'Find-on-map'
  | 'Floor-Plan'
  | 'FLX100-+-Cable-locator'
  | 'FLX100-handheld'
  | 'FLX100-Pole'
  | 'FLX100-Tilt'
  | 'FLX100-Upright'
  | 'Geo-Streaming'
  | 'Geometrical-transformation'
  | 'gnss-automatic'
  | 'gnss-last-position'
  | 'gnss-measuring'
  | 'gnss-pole'
  | 'gnss-tripod-location'
  | 'gps-corrections-Low-_hxgn'
  | 'gps-corrections-Very-Low_hxgn'
  | 'gps_corrections-ON_hxgn'
  | 'gps_corrections_off_hxgn'
  | 'GS05__hxgn'
  | 'GS18__hxgn'
  | 'iCS20-50__hxgn'
  | 'Laser-on-any-surface'
  | 'Laser-pointer'
  | 'Layers-CAD'
  | 'Layers-WFS'
  | 'Layers-WMS'
  | 'Layers'
  | 'Legend'
  | 'loop'
  | 'magic-wand'
  | 'Measurement-of-points-Circle'
  | 'Measurement-of-points-Square'
  | 'Mini-prism'
  | 'Move-unit-Pull-back'
  | 'Move-unit-Shere'
  | 'navigation-to-a-line'
  | 'navigation-to-a-point'
  | 'Object-Avoidance-Active-On'
  | 'Object-Avoidance-Off'
  | 'Orbit-Y'
  | 'Orbit-Z'
  | 'Orbit-X'
  | 'P-series__hxgn'
  | 'Pan'
  | 'Pen-tool'
  | 'Place-on-surface'
  | 'Plane-X'
  | 'Plane-Y'
  | 'Plane-Z'
  | 'point-on-the-road'
  | 'Position-available'
  | 'Prism-360°'
  | 'Prism-long-range'
  | 'Product_HxDR'
  | 'Property-to-Go'
  | 'Rebar'
  | 'Reference-Line'
  | 'Reverse-TPS-1-and-2'
  | 'road-location'
  | 'road'
  | 'Rotate-TPS-left'
  | 'Round-prism'
  | 'RTC360__hxgn'
  | 'RTK-not-available_hxgn'
  | 'scan-automatic'
  | 'scan-diffent-directions'
  | 'scan-level-point'
  | 'scan-line'
  | 'scan-multiple-line'
  | 'Set-target-height'
  | 'Set-telescope-horizontal'
  | 'Shaded-Rendered'
  | 'Shaded'
  | 'Spot__hxgn'
  | 'spray-paint'
  | 'Stakeout-peg-circle'
  | 'Stakeout-peg-rectangle'
  | 'Style-Lines-1'
  | 'Style-Lines'
  | 'Symbology'
  | 'Tape-target'
  | 'TPS-measurement-two-prisms'
  | 'TPS-Robotic-Total-Station'
  | 'TPS-tripod-view-from-the-top'
  | 'TPS-tripod'
  | 'Trak-Edit'
  | 'Trak'
  | 'Vertical-point'
  | 'Video-Globe'
  | 'View-Side-3D'
  | 'View-Top'
  | 'Wireframe'
  | 'X-ray';

export type NovaIconsKey =
  | 'i360Capture'
  | 'i360Videowalk'
  | 'i3DPrintMesh'
  | 'i3DSurfaceWithGround'
  | 'i3DSurface'
  | 'Angle'
  | 'BimCloudpoint'
  | 'Bim'
  | 'Binoculars'
  | 'BlkArcOffHxgn'
  | 'BlkArcHxgn'
  | 'Blk360G1Off'
  | 'Blk360G1'
  | 'Blk360G2OffHxgn'
  | 'Blk360G2Hxgn'
  | 'CableLocatorUnderground'
  | 'CalculatePolylineArea'
  | 'CameraGlobe'
  | 'Catalog'
  | 'CenterSquare'
  | 'ChangeCatalog'
  | 'CheckSurface'
  | 'CogoPointProjected'
  | 'Compass'
  | 'ConstructionSitesListHelmet'
  | 'ConstructionSitesList'
  | 'ControlPoint'
  | 'CoordinateSystem'
  | 'DashboardTable'
  | 'DefineSampleLines'
  | 'DefineTestSection'
  | 'DesignExcavation'
  | 'DeviceConnected'
  | 'DeviceConnections'
  | 'DeviceNotConnected'
  | 'DeviceUnknown'
  | 'DistanceBetweenTwoPoints'
  | 'Dolly'
  | 'EchoSounderArrow'
  | 'EchoSounderBoat'
  | 'ElectronicBubble'
  | 'EthernetCable'
  | 'Fffl'
  | 'FindOnMapStop'
  | 'FindOnMap'
  | 'FloorPlan'
  | 'Flx100CableLocator'
  | 'Flx100Handheld'
  | 'Flx100Pole'
  | 'Flx100Tilt'
  | 'Flx100Upright'
  | 'GeoStreaming'
  | 'GeometricalTransformation'
  | 'GnssAutomatic'
  | 'GnssLastPosition'
  | 'GnssMeasuring'
  | 'GnssPole'
  | 'GnssTripodLocation'
  | 'GpsCorrectionsLowHxgn'
  | 'GpsCorrectionsVeryLowHxgn'
  | 'GpsCorrectionsOnHxgn'
  | 'GpsCorrectionsOffHxgn'
  | 'Gs05Hxgn'
  | 'Gs18Hxgn'
  | 'ICs20_50Hxgn'
  | 'LaserOnAnySurface'
  | 'LaserPointer'
  | 'LayersCad'
  | 'LayersWfs'
  | 'LayersWms'
  | 'Layers'
  | 'Legend'
  | 'Loop'
  | 'MagicWand'
  | 'MeasurementOfPointsCircle'
  | 'MeasurementOfPointsSquare'
  | 'MiniPrism'
  | 'MoveUnitPullBack'
  | 'MoveUnitShere'
  | 'NavigationToALine'
  | 'NavigationToAPoint'
  | 'ObjectAvoidanceActiveOn'
  | 'ObjectAvoidanceOff'
  | 'OrbitY'
  | 'OrbitZ'
  | 'OrbitX'
  | 'PSeriesHxgn'
  | 'Pan'
  | 'PenTool'
  | 'PlaceOnSurface'
  | 'PlaneX'
  | 'PlaneY'
  | 'PlaneZ'
  | 'PointOnTheRoad'
  | 'PositionAvailable'
  | 'Prism_360'
  | 'PrismLongRange'
  | 'ProductHxDr'
  | 'PropertyToGo'
  | 'Rebar'
  | 'ReferenceLine'
  | 'ReverseTps_1And_2'
  | 'RoadLocation'
  | 'Road'
  | 'RotateTpsLeft'
  | 'RoundPrism'
  | 'Rtc360Hxgn'
  | 'RtkNotAvailableHxgn'
  | 'ScanAutomatic'
  | 'ScanDiffentDirections'
  | 'ScanLevelPoint'
  | 'ScanLine'
  | 'ScanMultipleLine'
  | 'SetTargetHeight'
  | 'SetTelescopeHorizontal'
  | 'ShadedRendered'
  | 'Shaded'
  | 'SpotHxgn'
  | 'SprayPaint'
  | 'StakeoutPegCircle'
  | 'StakeoutPegRectangle'
  | 'StyleLines_1'
  | 'StyleLines'
  | 'Symbology'
  | 'TapeTarget'
  | 'TpsMeasurementTwoPrisms'
  | 'TpsRoboticTotalStation'
  | 'TpsTripodViewFromTheTop'
  | 'TpsTripod'
  | 'TrakEdit'
  | 'Trak'
  | 'VerticalPoint'
  | 'VideoGlobe'
  | 'ViewSide_3D'
  | 'ViewTop'
  | 'Wireframe'
  | 'XRay';

export enum NovaIcons {
  i360Capture = '360-Capture',
  i360Videowalk = '360-videowalk',
  i3DPrintMesh = '3D-Print-Mesh',
  i3DSurfaceWithGround = '3D-surface-with-ground',
  i3DSurface = '3D-Surface',
  Angle = 'Angle',
  BimCloudpoint = 'BIM-cloudpoint',
  Bim = 'BIM',
  Binoculars = 'Binoculars',
  BlkArcOffHxgn = 'BLK-ARC-Off-__hxgn',
  BlkArcHxgn = 'BLK-ARC__hxgn',
  Blk360G1Off = 'BLK360-G1-Off',
  Blk360G1 = 'BLK360-G1',
  Blk360G2OffHxgn = 'BLK360-G2-Off__hxgn',
  Blk360G2Hxgn = 'BLK360-G2__hxgn',
  CableLocatorUnderground = 'Cable-locator-underground',
  CalculatePolylineArea = 'Calculate-polyline-area',
  CameraGlobe = 'Camera-Globe',
  Catalog = 'Catalog',
  CenterSquare = 'Center-Square',
  ChangeCatalog = 'change-catalog',
  CheckSurface = 'check-surface',
  CogoPointProjected = 'COGO-point-projected',
  Compass = 'compass',
  ConstructionSitesListHelmet = 'Construction-sites-list-helmet',
  ConstructionSitesList = 'Construction-sites-list',
  ControlPoint = 'Control-Point',
  CoordinateSystem = 'Coordinate-system',
  DashboardTable = 'Dashboard-Table',
  DefineSampleLines = 'Define-sample-lines',
  DefineTestSection = 'Define-test-section',
  DesignExcavation = 'Design-excavation',
  DeviceConnected = 'Device-Connected',
  DeviceConnections = 'Device-connections',
  DeviceNotConnected = 'Device-not-Connected',
  DeviceUnknown = 'Device-unknown',
  DistanceBetweenTwoPoints = 'Distance-between-two-points',
  Dolly = 'Dolly',
  EchoSounderArrow = 'Echo-sounder-arrow',
  EchoSounderBoat = 'Echo-sounder-boat',
  ElectronicBubble = 'Electronic-bubble',
  EthernetCable = 'ethernet-cable',
  Fffl = 'FFFL',
  FindOnMapStop = 'Find-on-map-Stop',
  FindOnMap = 'Find-on-map',
  FloorPlan = 'Floor-Plan',
  Flx100CableLocator = 'FLX100-+-Cable-locator',
  Flx100Handheld = 'FLX100-handheld',
  Flx100Pole = 'FLX100-Pole',
  Flx100Tilt = 'FLX100-Tilt',
  Flx100Upright = 'FLX100-Upright',
  GeoStreaming = 'Geo-Streaming',
  GeometricalTransformation = 'Geometrical-transformation',
  GnssAutomatic = 'gnss-automatic',
  GnssLastPosition = 'gnss-last-position',
  GnssMeasuring = 'gnss-measuring',
  GnssPole = 'gnss-pole',
  GnssTripodLocation = 'gnss-tripod-location',
  GpsCorrectionsLowHxgn = 'gps-corrections-Low-_hxgn',
  GpsCorrectionsVeryLowHxgn = 'gps-corrections-Very-Low_hxgn',
  GpsCorrectionsOnHxgn = 'gps_corrections-ON_hxgn',
  GpsCorrectionsOffHxgn = 'gps_corrections_off_hxgn',
  Gs05Hxgn = 'GS05__hxgn',
  Gs18Hxgn = 'GS18__hxgn',
  ICs20_50Hxgn = 'iCS20-50__hxgn',
  LaserOnAnySurface = 'Laser-on-any-surface',
  LaserPointer = 'Laser-pointer',
  LayersCad = 'Layers-CAD',
  LayersWfs = 'Layers-WFS',
  LayersWms = 'Layers-WMS',
  Layers = 'Layers',
  Legend = 'Legend',
  Loop = 'loop',
  MagicWand = 'magic-wand',
  MeasurementOfPointsCircle = 'Measurement-of-points-Circle',
  MeasurementOfPointsSquare = 'Measurement-of-points-Square',
  MiniPrism = 'Mini-prism',
  MoveUnitPullBack = 'Move-unit-Pull-back',
  MoveUnitShere = 'Move-unit-Shere',
  NavigationToALine = 'navigation-to-a-line',
  NavigationToAPoint = 'navigation-to-a-point',
  ObjectAvoidanceActiveOn = 'Object-Avoidance-Active-On',
  ObjectAvoidanceOff = 'Object-Avoidance-Off',
  OrbitY = 'Orbit-Y',
  OrbitZ = 'Orbit-Z',
  OrbitX = 'Orbit-X',
  PSeriesHxgn = 'P-series__hxgn',
  Pan = 'Pan',
  PenTool = 'Pen-tool',
  PlaceOnSurface = 'Place-on-surface',
  PlaneX = 'Plane-X',
  PlaneY = 'Plane-Y',
  PlaneZ = 'Plane-Z',
  PointOnTheRoad = 'point-on-the-road',
  PositionAvailable = 'Position-available',
  Prism_360 = 'Prism-360°',
  PrismLongRange = 'Prism-long-range',
  ProductHxDr = 'Product_HxDR',
  PropertyToGo = 'Property-to-Go',
  Rebar = 'Rebar',
  ReferenceLine = 'Reference-Line',
  ReverseTps_1And_2 = 'Reverse-TPS-1-and-2',
  RoadLocation = 'road-location',
  Road = 'road',
  RotateTpsLeft = 'Rotate-TPS-left',
  RoundPrism = 'Round-prism',
  Rtc360Hxgn = 'RTC360__hxgn',
  RtkNotAvailableHxgn = 'RTK-not-available_hxgn',
  ScanAutomatic = 'scan-automatic',
  ScanDiffentDirections = 'scan-diffent-directions',
  ScanLevelPoint = 'scan-level-point',
  ScanLine = 'scan-line',
  ScanMultipleLine = 'scan-multiple-line',
  SetTargetHeight = 'Set-target-height',
  SetTelescopeHorizontal = 'Set-telescope-horizontal',
  ShadedRendered = 'Shaded-Rendered',
  Shaded = 'Shaded',
  SpotHxgn = 'Spot__hxgn',
  SprayPaint = 'spray-paint',
  StakeoutPegCircle = 'Stakeout-peg-circle',
  StakeoutPegRectangle = 'Stakeout-peg-rectangle',
  StyleLines_1 = 'Style-Lines-1',
  StyleLines = 'Style-Lines',
  Symbology = 'Symbology',
  TapeTarget = 'Tape-target',
  TpsMeasurementTwoPrisms = 'TPS-measurement-two-prisms',
  TpsRoboticTotalStation = 'TPS-Robotic-Total-Station',
  TpsTripodViewFromTheTop = 'TPS-tripod-view-from-the-top',
  TpsTripod = 'TPS-tripod',
  TrakEdit = 'Trak-Edit',
  Trak = 'Trak',
  VerticalPoint = 'Vertical-point',
  VideoGlobe = 'Video-Globe',
  ViewSide_3D = 'View-Side-3D',
  ViewTop = 'View-Top',
  Wireframe = 'Wireframe',
  XRay = 'X-ray',
}

export const NOVA_ICONS_CODEPOINTS: { [key in NovaIcons]: string } = {
  [NovaIcons.i360Capture]: '61697',
  [NovaIcons.i360Videowalk]: '61698',
  [NovaIcons.i3DPrintMesh]: '61699',
  [NovaIcons.i3DSurfaceWithGround]: '61700',
  [NovaIcons.i3DSurface]: '61701',
  [NovaIcons.Angle]: '61702',
  [NovaIcons.BimCloudpoint]: '61703',
  [NovaIcons.Bim]: '61704',
  [NovaIcons.Binoculars]: '61705',
  [NovaIcons.BlkArcOffHxgn]: '61706',
  [NovaIcons.BlkArcHxgn]: '61707',
  [NovaIcons.Blk360G1Off]: '61708',
  [NovaIcons.Blk360G1]: '61709',
  [NovaIcons.Blk360G2OffHxgn]: '61710',
  [NovaIcons.Blk360G2Hxgn]: '61711',
  [NovaIcons.CableLocatorUnderground]: '61712',
  [NovaIcons.CalculatePolylineArea]: '61713',
  [NovaIcons.CameraGlobe]: '61714',
  [NovaIcons.Catalog]: '61715',
  [NovaIcons.CenterSquare]: '61716',
  [NovaIcons.ChangeCatalog]: '61717',
  [NovaIcons.CheckSurface]: '61718',
  [NovaIcons.CogoPointProjected]: '61719',
  [NovaIcons.Compass]: '61720',
  [NovaIcons.ConstructionSitesListHelmet]: '61721',
  [NovaIcons.ConstructionSitesList]: '61722',
  [NovaIcons.ControlPoint]: '61723',
  [NovaIcons.CoordinateSystem]: '61724',
  [NovaIcons.DashboardTable]: '61725',
  [NovaIcons.DefineSampleLines]: '61726',
  [NovaIcons.DefineTestSection]: '61727',
  [NovaIcons.DesignExcavation]: '61728',
  [NovaIcons.DeviceConnected]: '61729',
  [NovaIcons.DeviceConnections]: '61730',
  [NovaIcons.DeviceNotConnected]: '61731',
  [NovaIcons.DeviceUnknown]: '61732',
  [NovaIcons.DistanceBetweenTwoPoints]: '61733',
  [NovaIcons.Dolly]: '61734',
  [NovaIcons.EchoSounderArrow]: '61735',
  [NovaIcons.EchoSounderBoat]: '61736',
  [NovaIcons.ElectronicBubble]: '61737',
  [NovaIcons.EthernetCable]: '61738',
  [NovaIcons.Fffl]: '61739',
  [NovaIcons.FindOnMapStop]: '61740',
  [NovaIcons.FindOnMap]: '61741',
  [NovaIcons.FloorPlan]: '61742',
  [NovaIcons.Flx100CableLocator]: '61743',
  [NovaIcons.Flx100Handheld]: '61744',
  [NovaIcons.Flx100Pole]: '61745',
  [NovaIcons.Flx100Tilt]: '61746',
  [NovaIcons.Flx100Upright]: '61747',
  [NovaIcons.GeoStreaming]: '61748',
  [NovaIcons.GeometricalTransformation]: '61749',
  [NovaIcons.GnssAutomatic]: '61750',
  [NovaIcons.GnssLastPosition]: '61751',
  [NovaIcons.GnssMeasuring]: '61752',
  [NovaIcons.GnssPole]: '61753',
  [NovaIcons.GnssTripodLocation]: '61754',
  [NovaIcons.GpsCorrectionsLowHxgn]: '61755',
  [NovaIcons.GpsCorrectionsVeryLowHxgn]: '61756',
  [NovaIcons.GpsCorrectionsOnHxgn]: '61757',
  [NovaIcons.GpsCorrectionsOffHxgn]: '61758',
  [NovaIcons.Gs05Hxgn]: '61759',
  [NovaIcons.Gs18Hxgn]: '61760',
  [NovaIcons.ICs20_50Hxgn]: '61761',
  [NovaIcons.LaserOnAnySurface]: '61762',
  [NovaIcons.LaserPointer]: '61763',
  [NovaIcons.LayersCad]: '61764',
  [NovaIcons.LayersWfs]: '61765',
  [NovaIcons.LayersWms]: '61766',
  [NovaIcons.Layers]: '61767',
  [NovaIcons.Legend]: '61768',
  [NovaIcons.Loop]: '61769',
  [NovaIcons.MagicWand]: '61770',
  [NovaIcons.MeasurementOfPointsCircle]: '61771',
  [NovaIcons.MeasurementOfPointsSquare]: '61772',
  [NovaIcons.MiniPrism]: '61773',
  [NovaIcons.MoveUnitPullBack]: '61774',
  [NovaIcons.MoveUnitShere]: '61775',
  [NovaIcons.NavigationToALine]: '61776',
  [NovaIcons.NavigationToAPoint]: '61777',
  [NovaIcons.ObjectAvoidanceActiveOn]: '61778',
  [NovaIcons.ObjectAvoidanceOff]: '61779',
  [NovaIcons.OrbitY]: '61780',
  [NovaIcons.OrbitZ]: '61781',
  [NovaIcons.OrbitX]: '61782',
  [NovaIcons.PSeriesHxgn]: '61783',
  [NovaIcons.Pan]: '61784',
  [NovaIcons.PenTool]: '61785',
  [NovaIcons.PlaceOnSurface]: '61786',
  [NovaIcons.PlaneX]: '61787',
  [NovaIcons.PlaneY]: '61788',
  [NovaIcons.PlaneZ]: '61789',
  [NovaIcons.PointOnTheRoad]: '61790',
  [NovaIcons.PositionAvailable]: '61791',
  [NovaIcons.Prism_360]: '61792',
  [NovaIcons.PrismLongRange]: '61793',
  [NovaIcons.ProductHxDr]: '61794',
  [NovaIcons.PropertyToGo]: '61795',
  [NovaIcons.Rebar]: '61796',
  [NovaIcons.ReferenceLine]: '61797',
  [NovaIcons.ReverseTps_1And_2]: '61798',
  [NovaIcons.RoadLocation]: '61799',
  [NovaIcons.Road]: '61800',
  [NovaIcons.RotateTpsLeft]: '61801',
  [NovaIcons.RoundPrism]: '61802',
  [NovaIcons.Rtc360Hxgn]: '61803',
  [NovaIcons.RtkNotAvailableHxgn]: '61804',
  [NovaIcons.ScanAutomatic]: '61805',
  [NovaIcons.ScanDiffentDirections]: '61806',
  [NovaIcons.ScanLevelPoint]: '61807',
  [NovaIcons.ScanLine]: '61808',
  [NovaIcons.ScanMultipleLine]: '61809',
  [NovaIcons.SetTargetHeight]: '61810',
  [NovaIcons.SetTelescopeHorizontal]: '61811',
  [NovaIcons.ShadedRendered]: '61812',
  [NovaIcons.Shaded]: '61813',
  [NovaIcons.SpotHxgn]: '61814',
  [NovaIcons.SprayPaint]: '61815',
  [NovaIcons.StakeoutPegCircle]: '61816',
  [NovaIcons.StakeoutPegRectangle]: '61817',
  [NovaIcons.StyleLines_1]: '61818',
  [NovaIcons.StyleLines]: '61819',
  [NovaIcons.Symbology]: '61820',
  [NovaIcons.TapeTarget]: '61821',
  [NovaIcons.TpsMeasurementTwoPrisms]: '61822',
  [NovaIcons.TpsRoboticTotalStation]: '61823',
  [NovaIcons.TpsTripodViewFromTheTop]: '61824',
  [NovaIcons.TpsTripod]: '61825',
  [NovaIcons.TrakEdit]: '61826',
  [NovaIcons.Trak]: '61827',
  [NovaIcons.VerticalPoint]: '61828',
  [NovaIcons.VideoGlobe]: '61829',
  [NovaIcons.ViewSide_3D]: '61830',
  [NovaIcons.ViewTop]: '61831',
  [NovaIcons.Wireframe]: '61832',
  [NovaIcons.XRay]: '61833',
};
